import { describe, it, expect, vi, beforeEach, afterEach, afterAll } from 'vitest';
import { createLoggerMock } from './utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Mocks simples pour éviter les dépendances
const mockNet = {
  createServer: vi.fn(() => {
    const server = {
      listen: vi.fn((port, callback) => {
        if (callback) callback();
        return server;
      }),
      on: vi.fn(() => server),
      once: vi.fn((event, listener) => {
        if (event === 'listening') {
          setTimeout(() => listener(), 10);
        }
        return server;
      }),
      close: vi.fn((callback) => {
        if (callback) callback();
      })
    };
    return server;
  })
};

const mockChildProcess = {
  exec: vi.fn((command, callback) => {
    callback(null, '', null);
  })
};

// Mock des modules
vi.mock('net', () => mockNet);
vi.mock('child_process', () => mockChildProcess);
vi.mock('fs', () => ({
  readFileSync: vi.fn(() => '7000'),
  writeFileSync: vi.fn()
}));

// Classe simple pour tester la gestion des ports
class PortManager {
  constructor(options = {}) {
    this.port = 7000;
    this.logger = options.logger || loggerMock;
  }

  async checkPort(port) {
    this.logger.debug(`Vérification du port ${port}`);

    return new Promise(resolve => {
      const server = mockNet.createServer();

      server.listen(port, () => {
        this.logger.debug(`Port ${port} libre, serveur démarré`);

        // Fermer le serveur et résoudre avec false (port libre)
        server.close(() => {
          this.logger.debug(`Serveur fermé sur le port ${port}`);
          resolve(false);
        });
      });
    });
  }

  async killProcessOnPort(port) {
    this.logger.debug(`Tentative d'arrêt du processus sur le port ${port}`);

    return new Promise(resolve => {
      mockChildProcess.exec(`netstat -ano | findstr :${port}`, (err, stdout) => {
        if (stdout.trim()) {
          const pid = '1234'; // Simuler un PID
          this.logger.info(`Processus trouvé sur le port ${port}: PID ${pid}`);

          mockChildProcess.exec(`taskkill /F /PID ${pid}`, () => {
            this.logger.info(`Processus ${pid} arrêté`);
            resolve(true);
          });
        } else {
          this.logger.warn(`Aucun processus trouvé sur le port ${port}`);
          resolve(false);
        }
      });
    });
  }

  async findFreePort() {
    this.logger.debug(`Recherche d'un port libre`);
    const port = 7000 + Math.floor(Math.random() * 1000);
    this.logger.info(`Port libre trouvé: ${port}`);
    return port;
  }
}

console.log('\n=== DÉBUT DES TESTS PORT MINIMAL ===\n');

describe('Port Manager Tests Minimaux', () => {
  let portManager;

  beforeEach(() => {
    // Réinitialiser les mocks
    vi.clearAllMocks();
    loggerMock.resetMocks();

    // Créer une nouvelle instance
    portManager = new PortManager({ logger: loggerMock });
  });

  afterAll(() => {
    // Nettoyer après tous les tests
    vi.restoreAllMocks();
    console.log('\n=== FIN DES TESTS PORT MINIMAL ===\n');
  });

  it('should initialize with default port', () => {
    expect(portManager.port).toBe(7000);
  });

  it('should check if port is free', async () => {
    const isOccupied = await portManager.checkPort(7000);
    expect(isOccupied).toBe(false);
    expect(mockNet.createServer).toHaveBeenCalled();
  });

  it('should find a free port', async () => {
    const port = await portManager.findFreePort();
    expect(port).toBeGreaterThan(7000);
    expect(port).toBeLessThan(8000);
  });

  it('should attempt to kill process on port', async () => {
    // Configurer le mock pour simuler un port occupé
    mockChildProcess.exec.mockImplementationOnce((command, callback) => {
      if (command.includes('netstat')) {
        callback(null, '  TCP    0.0.0.0:7000           0.0.0.0:0              LISTENING       1234', null);
      }
    });

    const result = await portManager.killProcessOnPort(7000);
    expect(result).toBe(true);
    expect(mockChildProcess.exec).toHaveBeenCalledWith(expect.stringContaining('netstat'), expect.any(Function));
  });
});
