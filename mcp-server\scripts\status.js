import { exec } from 'child_process';
import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const config = require('./config/config.js');

const port = config.server.port;

exec(`netstat -ano | findstr :${port}`, (err, stdout) => {
  if (stdout) {
    const pid = stdout.trim().split(/\s+/).pop();
    console.log(`Serveur en cours d'exécution (PID ${pid}) sur le port ${port}`);
    process.exit(0);
  } else {
    console.log(`Aucun serveur n'écoute sur le port ${port}`);
    process.exit(1);
  }
});
