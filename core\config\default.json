{"server": {"port": 50778, "portCheck": {"enabled": true, "alternatePorts": [50778, 50779, 50780], "retryStrategy": {"maxAttempts": 3, "delayMs": 1000, "backoffFactor": 2}}, "processManagement": {"portFinderCommand": "netstat -ano | findstr :{port}", "killCommand": "taskkill /F /PID {pid}", "killPrevious": true}}, "logging": {"level": "debug", "file": {"enabled": false, "path": "logs/core.log"}}, "modules": {"chat": {"enabled": true}, "gdevelop": {"enabled": true}, "ai": {"enabled": true}}}