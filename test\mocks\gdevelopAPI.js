import { vi } from 'vitest';

/**
 * Mock complet de l'API GDevelop
 * Ce mock est conçu pour être utilisé dans tous les tests
 */
export default class MockGDevelopAPI {
  constructor() {
    this.baseUrl = 'https://api.mock-gdevelop.com/v1';
    this.apiKey = 'mock-api-key';
    this.projects = new Map();
    this.encrypt = vi.fn().mockImplementation((data) => ({
      iv: 'mockIV1234567890',
      content: `encrypted_${JSON.stringify(data)}`
    }));
  }

  getProject(id) {
    return Promise.resolve({
      id,
      title: `Test Game`,
      version: '1.0.0',
      events: [],
      resources: [],
      objects: [],
      encrypted: false
    });
  }

  updateProject(projectId, data) {
    return Promise.resolve({
      id: projectId,
      ...data,
      updatedAt: new Date().toISOString()
    });
  }

  getEvents(projectId) {
    return Promise.resolve([
      { name: 'Start Scene', type: 'standard' },
      { name: 'Game Over', type: 'standard' }
    ]);
  }

  getResources(projectId) {
    return Promise.resolve([
      'sprite1.png',
      'background.jpg'
    ]);
  }

  exportProject(project) {
    return {
      ...project,
      ...this.encrypt(project),
      encrypted: true
    };
  }

  _getHeaders() {
    return {
      'Authorization': 'Bearer mock-api-key',
      'Content-Type': 'application/json'
    };
  }
}
