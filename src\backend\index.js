import express from 'express'
import cors from 'cors'
import Chat<PERSON>ontroller from './controllers/chatController.js'
import ChatService from './services/chatService.js'

// Initialisation
const app = express()
const port = 4001
const chatService = new ChatService()
const chatController = new ChatController(chatService)

// Middlewares
app.use(cors())
app.use(express.json())

// Routes
app.post('/api/chat', (req, res) => chatController.handleMessage(req, res))

// Démarrage
app.listen(port, () => {
  console.log(`Serveur backend démarré sur http://localhost:${port}`)
})
