import { vi } from 'vitest';

// Import setup from test directory to ensure consistency
import '../test/setup';

// Import centralized mock configuration
import { setupAllMocks } from './mocks';

// Configure all mocks for tests/ directory
setupAllMocks({
  mockNet: true,
  mockChildProcess: true
});

// Additional setup specific to tests/ directory
console.log('Tests directory setup loaded');

// Set environment variables for tests
process.env.NODE_ENV = 'test';
process.env.TEST_MODE = 'true';
process.env.LOG_LEVEL = 'silent';

// Ensure mock configuration is applied before tests run
beforeEach(() => {
  vi.resetAllMocks();
  vi.clearAllMocks();
});
