{"engines": {"npm": ">=11.4.0"}, "type": "module", "imports": {"#logger": "./src/utils/logger.js"}, "name": "gdev-ai", "version": "0.1.0", "description": "Assistant I<PERSON> <PERSON> G<PERSON><PERSON>", "main": "src/backend/index.js", "scripts": {"preinstall": "npx npm-force-resolutions", "cleanup-runs": "node scripts/cleanup-runs.mjs", "check-security": "node test/security.test.js --no-warnings", "lint:imports": "node scripts/check-imports", "precommit": "npm run check-imports && npm test", "predev": "node scripts/portManager.js --run --ports=3002,3007", "dev": "concurrently \"npm run frontend\" \"npm run backend\"", "frontend": "cd src/frontend && set PORT=3002 && npm run dev", "backend": "set PORT=3007 && node core/index.js", "build": "cd src/frontend && npm run build", "pretest": "node scripts/clean-ports.js", "test": "node node_modules/vitest/vitest.mjs run", "test:debug": "set DEBUG_MOCKS=true&& node node_modules/vitest/vitest.mjs run", "test:unit": "node node_modules/vitest/vitest.mjs run test/unit", "test:integration": "node node_modules/vitest/vitest.mjs run test/integration", "test:e2e": "node node_modules/vitest/vitest.mjs run test/e2e", "test:ci": "node node_modules/vitest/vitest.mjs run --coverage --reporter=html && node scripts/archiveCoverage.js", "test:watch": "node node_modules/vitest/vitest.mjs watch", "test:port": "node node_modules/vitest/vitest.mjs run test/portManagement.test.js", "test:coverage": "node node_modules/vitest/vitest.mjs run --coverage", "test:coverage:chat": "node node_modules/vitest/vitest.mjs run test/MessageStorage.test.js test/chatService.test.js test/chatService.integration.test.js test/chatController.test.js --coverage", "test:utils": "node scripts/test-utils.js", "test:validate": "node scripts/validateTests.cjs", "coverage": "vitest run --coverage", "mcp-start": "cd mcp-server && node server.js", "generate-keys": "node scripts/generateKeys.js", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "prettier --write .", "setup": "node setup.js", "verify": "node verify-env.js", "start": "npm run dev", "tailwind": "node scripts/run-tailwind.mjs", "tailwind:build": "node scripts/run-tailwind.mjs --build", "test:token": "node --input-type=module -e \"import { generateToken } from './src/utils/auth.js'; console.log(generateToken({ test: true }));\"", "validate:rbac": "node scripts/validate-rbac.js"}, "keywords": ["gdevelop", "ai", "assistant"], "author": "", "license": "MIT", "dependencies": {"@babel/plugin-transform-classes": "^7.27.1", "@babel/traverse": "^7.27.3", "axios": "^1.8.4", "cors": "^2.8.5", "dotenv": "16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "keytar": "^7.9.0", "openai": "^4.95.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "resolutions": {"globals": "16.1.0", "@eslint/js": "9.27.0", "@babel/traverse": "7.27.3", "@babel/plugin-transform-classes": "7.27.1", "@eslint/eslintrc": "3.3.1"}, "devDependencies": {"@babel/core": "^7.27.3", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.26.3", "@eslint/js": "9.27.0", "@tailwindcss/postcss": "^4.1.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@vitejs/plugin-react": "^4.5.0", "@vitest/coverage-v8": "^3.1.3", "@vitest/ui": "^3.1.2", "concurrently": "^8.2.1", "eslint": "^9.4.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "globals": "16.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.2", "npm-force-resolutions": "^0.0.10", "postcss": "^8.5.3", "postcss-cli": "^11.0.1", "prettier": "^3.2.5", "react": "^19.1.0", "react-router-dom": "^6.23.0", "tailwindcss": "^4.1.4", "vite": "^6.3.1", "vitest": "^3.1.3"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md,yml}": ["prettier --write"]}}