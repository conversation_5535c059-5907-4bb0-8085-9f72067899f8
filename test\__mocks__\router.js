/**
 * Mock pour le Router
 */
import { vi } from 'vitest';
import universalLogger from './universal-logger.js';

class MockRouter {
  constructor() {
    this.modules = new Map();
    this.config = {
      modules: {
        gdevelop: {
          enabled: true
        }
      }
    };
    this.logger = universalLogger;
    
    // Initialiser le module GDevelop
    this.modules.set('gdevelop', {
      process: vi.fn().mockImplementation((message) => {
        return this.routeMessage('gdevelop', message);
      })
    });
  }

  async loadConfig() {
    return Promise.resolve(this.config);
  }

  async routeMessage(module, message) {
    if (module === 'gdevelop') {
      switch (message.action) {
        case 'getProject':
          return {
            status: 'success',
            project: {
              id: message.projectId,
              title: 'Test Game',
              objects: []
            }
          };
        case 'updateProject':
          if (!message.data || Object.keys(message.data).length === 0) {
            throw new Error('Invalid project data');
          }
          return {
            status: 'success',
            updatedAt: new Date().toISOString()
          };
        case 'getEvents':
          return {
            status: 'success',
            events: [
              { name: 'Start Scene', type: 'standard' },
              { name: 'Game Over', type: 'standard' }
            ]
          };
        case 'syncResources':
          return {
            status: 'success',
            resources: ['sprite1.png', 'background.jpg'],
            syncedAt: new Date().toISOString()
          };
        case 'getChanges':
          return {
            status: 'success',
            changes: [
              {
                type: 'update',
                path: 'project.title',
                value: 'Updated Game',
                timestamp: new Date().toISOString()
              }
            ]
          };
        default:
          throw new Error(`Invalid action: ${message.action}`);
      }
    }
    throw new Error(`Module not found: ${module}`);
  }

  initializeModules() {
    // Ne rien faire, les modules sont déjà initialisés
  }
}

export default MockRouter;
