/**
 * Test isolé pour le module GDevelop
 * Ce test n'utilise pas les mocks globaux pour éviter les problèmes
 */
import { vi, describe, beforeEach, afterEach, it, expect } from 'vitest';
import gdevelopModule from '../../../core/modules/gdevelop';

// Mock du logger
const mockLogger = {
  info: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
  child: vi.fn().mockImplementation(() => mockLogger)
};

// Mock du module GDevelop
const mockGDevelopModule = {
  process: vi.fn()
};

// Mock du router
const mockRouter = {
  modules: new Map([['gdevelop', mockGDevelopModule]]),
  routeMessage: vi.fn()
};

describe('GDevelop Module (Isolated)', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Configurer les mocks pour les tests
    mockRouter.routeMessage.mockImplementation((module, message) => {
      if (module === 'gdevelop') {
        return mockGDevelopModule.process(message);
      }
      throw new Error(`Module not found: ${module}`);
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should process getProject action', async () => {
    // Configurer le mock pour retourner un projet
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      project: {
        id: 'test123',
        title: 'Test Game',
        objects: []
      }
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getProject',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.project).toEqual({
      id: 'test123',
      title: 'Test Game',
      objects: []
    });

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'getProject',
      projectId: 'test123'
    });
  });

  it('should process updateProject action', async () => {
    // Configurer le mock pour retourner un succès
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      updatedAt: '2023-01-01T00:00:00.000Z'
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'updateProject',
      projectId: 'test123',
      data: { title: 'Updated Game' }
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.updatedAt).toBeDefined();

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'updateProject',
      projectId: 'test123',
      data: { title: 'Updated Game' }
    });
  });

  it('should handle errors properly', async () => {
    // Configurer le mock pour lancer une erreur
    const error = new Error('Test error');
    mockGDevelopModule.process.mockRejectedValueOnce(error);

    // Appeler la fonction et vérifier qu'elle lance une erreur
    await expect(
      mockRouter.routeMessage('gdevelop', {
        action: 'invalidAction',
        projectId: 'test123'
      })
    ).rejects.toThrow('Test error');
  });

  it('should process getEvents action', async () => {
    // Configurer le mock pour retourner des événements
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      events: [
        { name: 'Start Scene', type: 'standard' },
        { name: 'Game Over', type: 'standard' }
      ]
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getEvents',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.events).toHaveLength(2);
    expect(result.events[0].name).toBe('Start Scene');

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'getEvents',
      projectId: 'test123'
    });
  });

  it('should process syncResources action', async () => {
    // Configurer le mock pour retourner des ressources
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      resources: ['sprite1.png', 'background.jpg'],
      syncedAt: '2023-01-01T00:00:00.000Z'
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'syncResources',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.resources).toEqual(['sprite1.png', 'background.jpg']);
    expect(result.syncedAt).toBeDefined();

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'syncResources',
      projectId: 'test123'
    });
  });

  it('should process getChanges action', async () => {
    // Configurer le mock pour retourner des changements
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      changes: [
        {
          type: 'update',
          path: 'project.title',
          value: 'Updated Game',
          timestamp: '2023-01-01T00:00:00.000Z'
        }
      ]
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getChanges',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.changes).toHaveLength(1);
    expect(result.changes[0].type).toBe('update');
    expect(result.changes[0].path).toBe('project.title');

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'getChanges',
      projectId: 'test123'
    });
  });
});
