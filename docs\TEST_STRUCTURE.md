# Structure des Tests

Ce document explique la structure des tests dans le projet et comment les organiser correctement.

## Organisation des Répertoires

Le projet utilise deux répertoires principaux pour les tests :

```
project/
├── test/           # Tests originaux et mocks partagés
│   ├── mocks/      # Mocks partagés par tous les tests
│   │   ├── fs.js           # Mock standardisé pour fs
│   │   ├── child_process.js # Mock standardisé pour child_process
│   │   ├── net.js          # Mock standardisé pour net
│   │   ├── logger.js       # Mock standardisé pour logger
│   │   └── gdevelopAPI.js  # Mock standardisé pour GDevelop API
│   ├── setup.ts    # Configuration globale des tests
│   └── *.test.js   # Tests individuels
└── tests/          # Nouvelle structure de tests
    ├── unit/       # Tests unitaires
    │   ├── modules/    # Tests des modules
    │   └── services/   # Tests des services
    ├── integration/    # Tests d'intégration
    │   └── api/        # Tests d'API
    ├── e2e/            # Tests end-to-end
    ├── mocks/          # Configuration centralisée des mocks
    │   └── index.js    # Point d'entrée pour tous les mocks
    └── setup.ts        # Configuration spécifique aux tests/
```

## Bonnes Pratiques

### 1. Importation des Modules

Utilisez des chemins d'importation relatifs pour éviter les problèmes de résolution d'alias :

```javascript
// Bon (chemin relatif)
import { Core } from '../../../core';
import Router from '../../../core/router';

// À éviter (alias)
import { Core } from '#core';
import Router from '#router';
```

### 2. Configuration des Mocks

Utilisez la configuration centralisée des mocks pour assurer la cohérence :

```javascript
// Dans votre test
import { setupAllMocks } from '../../mocks';

// Configurer tous les mocks nécessaires
setupAllMocks({
  mockLogger: true,
  mockFS: true,
  mockNet: true,
  mockChildProcess: true,
  mockGDevelopAPI: true,
  mockAIMetrics: true,
  mockAuth: true
});

// Vous pouvez également importer directement les mocks pour les utiliser dans vos tests
import { fsMock, netMock, childProcessMock } from '../../mocks';

// Utiliser les fonctions utilitaires des mocks
fsMock.__setFile('config/test.json', '{"test": true}');
netMock.__setPortOccupied(5003);
```

### 3. Structure des Tests

Suivez cette structure pour vos tests :

```javascript
import { vi, describe, it, expect, beforeEach, afterAll } from 'vitest';
// Import de la configuration centralisée des mocks
import { setupAllMocks } from '../../mocks';

// Configurer tous les mocks nécessaires
setupAllMocks();

// Imports des modules à tester (après la configuration des mocks)
import MyModule from '../path/to/module';

describe('Module Name', () => {
  // Configuration avant tous les tests
  beforeAll(() => {
    // ...
  });

  // Configuration avant chaque test
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // Nettoyage après tous les tests
  afterAll(() => {
    // ...
  });

  // Tests individuels
  it('should do something', () => {
    // ...
  });
});
```

### 4. Exemples Spécifiques

#### Exemple 1: Test avec mock de fs

```javascript
import { vi, describe, it, expect } from 'vitest';
import { setupAllMocks, fsMock } from '../../mocks';

// Configurer tous les mocks nécessaires
setupAllMocks();

// Import du module à tester (après la configuration des mocks)
import { readConfig } from '../../../src/utils/config';

describe('Config Utils', () => {
  beforeEach(() => {
    // Réinitialiser les mocks avant chaque test
    vi.clearAllMocks();
    fsMock.__clearFiles();

    // Configurer un fichier de test
    fsMock.__setFile('config/test.json', JSON.stringify({
      server: { port: 5003 },
      modules: { chat: { enabled: true } }
    }));
  });

  it('should read config file correctly', async () => {
    const config = await readConfig('config/test.json');
    expect(config).toEqual({
      server: { port: 5003 },
      modules: { chat: { enabled: true } }
    });
    expect(fsMock.promises.readFile).toHaveBeenCalledWith('config/test.json', expect.any(Object));
  });
});
```

#### Exemple 2: Test avec mock de child_process

```javascript
import { vi, describe, it, expect } from 'vitest';
import { setupAllMocks, childProcessMock } from '../../mocks';

// Configurer tous les mocks nécessaires
setupAllMocks();

// Import du module à tester (après la configuration des mocks)
import { checkPort } from '../../../src/utils/portManager';

describe('Port Manager', () => {
  beforeEach(() => {
    // Réinitialiser les mocks avant chaque test
    vi.clearAllMocks();
    childProcessMock.__clearAllProcesses();
  });

  it('should detect occupied port', async () => {
    // Configurer un processus sur le port 5003
    childProcessMock.__setProcessOnPort(5003, 12345);

    const isOccupied = await checkPort(5003);
    expect(isOccupied).toBe(true);
    expect(childProcessMock.exec).toHaveBeenCalledWith(
      expect.stringContaining('5003'),
      expect.any(Function)
    );
  });
});
```

## Migration des Tests

Pour migrer un test de l'ancienne structure vers la nouvelle :

1. Créez le répertoire approprié dans `tests/`
2. Copiez le fichier de test dans ce répertoire
3. Mettez à jour les imports pour utiliser la configuration centralisée des mocks :

```javascript
// Avant
import { vi, describe, it, expect } from 'vitest';
vi.mock('fs', () => ({ ... }));
vi.mock('child_process', () => ({ ... }));

// Après
import { vi, describe, it, expect } from 'vitest';
import { setupAllMocks } from '../../mocks';
setupAllMocks();
```

4. Remplacez les imports d'alias par des chemins relatifs :

```javascript
// Avant
import { Core } from '#core';

// Après
import { Core } from '../../../core';
```

5. Exécutez le test pour vérifier qu'il fonctionne correctement

## Résolution des Problèmes Courants

### Problème: Module non trouvé

```
Error: Cannot find module '../../../test/mocks/net'
```

**Solution**: Utilisez la configuration centralisée des mocks :

```javascript
import { netMock, setupAllMocks } from '../../mocks';
setupAllMocks();
```

### Problème: Export manquant dans un mock

```
Error: No "default" export is defined on the "fs" mock
```

**Solution**: Utilisez la configuration centralisée des mocks qui expose correctement tous les exports :

```javascript
import { setupAllMocks } from '../../mocks';
setupAllMocks();
```

### Problème: Erreur de hoisting avec vi.mock

```
Error: There was an error when mocking a module
```

**Solution**: Assurez-vous que les appels à `vi.mock` sont au niveau supérieur du fichier, ou utilisez la configuration centralisée des mocks :

```javascript
import { setupAllMocks } from '../../mocks';
setupAllMocks();
```

### Problème d'Alias

Si vous rencontrez des erreurs comme `Missing "#core" specifier`, utilisez des chemins relatifs au lieu des alias.

### Problème de Mock

Si un mock ne fonctionne pas correctement, vérifiez qu'il est correctement configuré dans le fichier de test et qu'il est importé avant le module qui l'utilise.

### Problème de Configuration

Si la configuration des tests ne fonctionne pas correctement, vérifiez que le fichier `setup.ts` est correctement configuré et qu'il est inclus dans la configuration de Vitest.

## Exécution des Tests

Pour exécuter les tests, utilisez les commandes suivantes :

```bash
# Exécuter tous les tests
npm test

# Exécuter les tests unitaires
npm run test:unit

# Exécuter les tests d'intégration
npm run test:integration

# Exécuter les tests end-to-end
npm run test:e2e
```
