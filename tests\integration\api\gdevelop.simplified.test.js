/**
 * Tests simplifiés pour l'API GDevelop
 * 
 * Ce fichier contient des tests simples et isolés pour l'API GDevelop,
 * sans dépendance à Core ou à d'autres composants complexes.
 */
import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from '../../../test/utils/simpleMocks';

// Mock pour le logger
const loggerMock = createLoggerMock();

vi.mock('#logger', () => {
  return {
    default: loggerMock,
    logger: loggerMock,
    createLogger: vi.fn(() => loggerMock)
  };
});

// Mock simple pour l'API GDevelop
class MockGDevelopAPI {
  constructor() {
    this.baseUrl = 'https://mock-api.gdevelop-app.com/v1';
    this.apiKey = 'mock-api-key';
  }

  async getProject(id) {
    return {
      id,
      name: `Mock project ${id}`,
      version: '1.0.0'
    };
  }

  async updateProject(id, data) {
    return {
      id,
      ...data,
      updatedAt: new Date().toISOString()
    };
  }

  async getEvents(id) {
    return [
      { name: 'Start Scene', type: 'standard' },
      { name: 'Game Over', type: 'standard' }
    ];
  }

  async getResources(id) {
    return ['sprite1.png', 'background.jpg'];
  }
}

// Mock pour le module GDevelop API
vi.mock('../../../integrations/gdevelop/api.js', () => {
  return {
    default: MockGDevelopAPI
  };
});

// Classe simplifiée pour le module GDevelop
class GDevelopModule {
  constructor() {
    this.logger = loggerMock;
    this.api = new MockGDevelopAPI();
  }

  async initialize() {
    this.logger.info('GDevelop module initialized');
    return this;
  }

  async process(message) {
    try {
      const { action, projectId } = message;

      switch (action) {
        case 'getProject':
          const project = await this.api.getProject(projectId);
          return {
            status: 'success',
            project
          };

        case 'updateProject':
          const { data } = message;
          if (!data || Object.keys(data).length === 0) {
            throw new Error('Données de projet invalides');
          }
          const updatedProject = await this.api.updateProject(projectId, data);
          return {
            status: 'success',
            updatedAt: updatedProject.updatedAt
          };

        case 'getEvents':
          const events = await this.api.getEvents(projectId);
          return {
            status: 'success',
            events
          };

        case 'syncResources':
          const resources = await this.api.getResources(projectId);
          return {
            status: 'success',
            resources,
            syncedAt: new Date().toISOString()
          };

        case 'getChanges':
          return {
            status: 'success',
            changes: [
              {
                timestamp: new Date().toISOString(),
                changes: { title: 'Updated Game' }
              }
            ]
          };

        default:
          throw new Error(`Action non supportée: ${action}`);
      }
    } catch (error) {
      this.logger.error('Erreur module GDevelop:', error);
      throw error;
    }
  }
}

describe('GDevelop API Tests (Simplified)', () => {
  let gdevelopModule;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();
    gdevelopModule = new GDevelopModule();
  });

  test('should initialize module', async () => {
    await gdevelopModule.initialize();
    expect(loggerMock.info).toHaveBeenCalledWith('GDevelop module initialized');
  });

  test('should get project details', async () => {
    const result = await gdevelopModule.process({
      action: 'getProject',
      projectId: 'test123'
    });

    expect(result.status).toBe('success');
    expect(result.project).toEqual({
      id: 'test123',
      name: 'Mock project test123',
      version: '1.0.0'
    });
  });

  test('should update project', async () => {
    const result = await gdevelopModule.process({
      action: 'updateProject',
      projectId: 'test123',
      data: { title: 'Updated Game' }
    });

    expect(result.status).toBe('success');
    expect(result.updatedAt).toBeDefined();
  });

  test('should handle invalid project data', async () => {
    await expect(
      gdevelopModule.process({
        action: 'updateProject',
        projectId: 'test123',
        data: {} // Données vides
      })
    ).rejects.toThrow('Données de projet invalides');

    expect(loggerMock.error).toHaveBeenCalled();
  });

  test('should handle invalid actions', async () => {
    await expect(
      gdevelopModule.process({
        action: 'invalidAction',
        projectId: 'test123'
      })
    ).rejects.toThrow('Action non supportée: invalidAction');

    expect(loggerMock.error).toHaveBeenCalled();
  });

  test('should get events', async () => {
    const result = await gdevelopModule.process({
      action: 'getEvents',
      projectId: 'test123'
    });

    expect(result.status).toBe('success');
    expect(result.events).toHaveLength(2);
    expect(result.events[0].name).toBe('Start Scene');
  });

  test('should sync resources', async () => {
    const result = await gdevelopModule.process({
      action: 'syncResources',
      projectId: 'test123'
    });

    expect(result.status).toBe('success');
    expect(result.resources).toEqual(['sprite1.png', 'background.jpg']);
    expect(result.syncedAt).toBeDefined();
  });
});
