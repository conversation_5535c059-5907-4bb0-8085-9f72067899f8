import { vi, describe, it, expect, beforeEach, afterAll } from 'vitest';
import gdevelopModule from '../../../core/modules/gdevelop';
import logger from '#logger';

// Configuration des mocks
vi.mock('../../../core/modules/gdevelop', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    default: {
      getProject: vi.fn((id) => Promise.resolve({
        id,
        name: `Mock project ${id}`,
        version: '1.0.0'
      })),
      updateProject: vi.fn(() => Promise.resolve(true)),
      syncResources: vi.fn(() => Promise.resolve({
        success: true,
        count: 0
      })),
      __forceError: vi.fn(() => {
        throw new Error('Mock error');
      })
    }
  };
});

vi.mock('#logger', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    default: {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      child: vi.fn(() => ({
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn()
      }))
    }
  };
});

describe('GDevelop Module Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterAll(() => {
    vi.restoreAllMocks();
  });

  it('should mock getProject correctly', async () => {
    const project = await gdevelopModule.getProject('test123');
    expect(project).toEqual({
      id: 'test123',
      name: 'Mock project test123',
      version: '1.0.0'
    });
    expect(gdevelopModule.getProject).toHaveBeenCalledOnce();
  });

  it('should mock updateProject correctly', async () => {
    const result = await gdevelopModule.updateProject({id: 'test123'});
    expect(result).toBe(true);
    expect(gdevelopModule.updateProject).toHaveBeenCalledOnce();
  });

  it('should mock syncResources correctly', async () => {
    const result = await gdevelopModule.syncResources();
    expect(result).toEqual({
      success: true,
      count: 0
    });
    expect(gdevelopModule.syncResources).toHaveBeenCalledOnce();
  });

  it('should mock errors when forced', () => {
    expect(() => gdevelopModule.__forceError()).toThrow('Mock error');
  });
});
