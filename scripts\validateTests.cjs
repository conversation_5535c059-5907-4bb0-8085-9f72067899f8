const fs = require('fs');
const path = require('path');

// Vérification des logs en mode test
function checkTestLogs() {
  const filesToCheck = [
    'core/index.js',
    'core/router.js',
    'src/backend/services/chatService.js'
  ];

  const errors = [];

  filesToCheck.forEach(file => {
    const content = fs.readFileSync(path.join(__dirname, '../', file), 'utf8');
    const lines = content.split('\n');
    
    lines.forEach((line, i) => {
      if (line.includes('console.log') && !line.includes('process.env.NODE_ENV')) {
        errors.push({
          file,
          line: i + 1,
          message: "Console.log non protégé en mode test"
        });
      }
    });
  });

  return errors;
}

// Exécution des vérifications
try {
  const logErrors = checkTestLogs();
  if (logErrors.length > 0) {
    console.error('ERREURS DE VALIDATION:');
    logErrors.forEach(err => {
      console.error(`${err.file}:${err.line} - ${err.message}`);
    });
    process.exit(1);
  }
  console.log('✅ Toutes les validations ont réussi');
  process.exit(0);
} catch (error) {
  console.error('ERREUR DANS LE SCRIPT:', error.message);
  process.exit(1);
}
