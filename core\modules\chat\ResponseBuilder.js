class ResponseBuilder {
  buildStandard(text, sender = 'assistant') {
    return {
      text: text || 'No response text',
      sender,
      isAI: false,
      timestamp: new Date().toISOString()
    };
  }

  buildAI(aiResponse) {
    if (!aiResponse?.data?.text) {
      throw new Error('Format de réponse AI invalide');
    }
    
    return {
      text: aiResponse.data.text,
      sender: 'assistant',
      isAI: true,
      tokens: aiResponse.data.tokens || 0,
      timestamp: new Date().toISOString(),
      source: aiResponse.metadata?.source || 'unknown'
    };
  }

  buildError(message = 'Service unavailable') {
    return {
      text: message,
      sender: 'system',
      isAI: false,
      timestamp: new Date().toISOString()
    };
  }
}

export default ResponseBuilder;
