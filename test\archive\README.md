# Tests Archivés

Ce répertoire contient les tests originaux qui ont été remplacés par des versions simplifiées.

## Pourquoi ces tests sont-ils archivés ?

Ces tests présentaient un ou plusieurs des problèmes suivants :

- **Dépendances complexes** : Dépendance à Core ou à d'autres modules complexes
- **Problèmes de port** : Conflits EADDRINUSE et problèmes de serveurs réels
- **Mocks complexes** : Utilisation de mocks difficiles à maintenir
- **Instabilité** : Tests qui échouent de manière imprévisible

## Versions simplifiées

Chaque test archivé a une version simplifiée correspondante dans le répertoire `test/simplified/`.

## Utilisation

Ces tests sont conservés à des fins de référence uniquement. Ils ne sont pas exécutés dans la suite de tests principale.

Pour consulter l'historique des améliorations, voir : `WORKLOG.md`
