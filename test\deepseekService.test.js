import { vi, it, expect, beforeEach, afterEach, describe } from 'vitest';
import DeepseekService from '../src/backend/services/deepseekService.js';
import { createTestContext } from './utils/testContext.js';

// Mock inline pour éviter le hoisting
const mockCreate = vi.fn().mockResolvedValue({
  choices: [{ message: { content: 'Mock response' } }]
});

vi.mock('openai', () => ({
  default: class MockOpenAI {
    constructor() {
      this.chat = {
        completions: {
          create: mockCreate
        }
      };
    }
  }
}));

describe('DeepseekService', () => {
  let service;
  let testContext;

  beforeEach(() => {
    // Créer un contexte de test avec les variables d'environnement nécessaires
    testContext = createTestContext({
      env: {
        DEEPSEEK_API_KEY: 'test-deepseek-key',
        DEEPSEEK_MODEL: 'test-model',
        CHAT_API_KEY: 'test-chat-key'
      }
    });

    vi.clearAllMocks();
    mockCreate.mockClear();
    service = new DeepseekService();
  });

  afterEach(() => {
    // Nettoyer le contexte de test
    testContext.cleanup();
  });

  it('should initialize correctly', () => {
    expect(service).toBeInstanceOf(DeepseekService);
  });

  it('should handle chat completion', async () => {
    // Configurer le mock pour ce test spécifique
    mockCreate.mockResolvedValueOnce({
      choices: [{ message: { content: 'Mock response' } }]
    });

    const result = await service.chatCompletion([{ role: 'user', content: 'test' }]);
    expect(result).toBe('Mock response');
    expect(mockCreate).toHaveBeenCalledWith(
      expect.objectContaining({
        model: expect.any(String),
        messages: [{ role: 'user', content: 'test' }]
      })
    );

    // Test supplémentaire pour vérifier les valeurs par défaut
    expect(mockCreate).toHaveBeenCalledWith(
      expect.objectContaining({
        temperature: 0.7,
        max_tokens: 1000
      })
    );
  });

  it('should reject non-array messages', async () => {
    await expect(service.chatCompletion("invalid"))
      .rejects
      .toThrow("Invalid message format: expected an array of messages");
  });

  it('should reject messages missing role/content', async () => {
    await expect(service.chatCompletion([{role: 'user'}]))
      .rejects.toThrow('must have role and content properties');
    await expect(service.chatCompletion([{content: 'test'}]))
      .rejects.toThrow('must have role and content properties');
  });
});
