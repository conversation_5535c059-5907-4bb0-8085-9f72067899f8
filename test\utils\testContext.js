/**
 * Utilitaire de contexte de test
 * 
 * Fournit un contexte isolé pour les tests avec des mocks préconfigurés
 * pour éviter les conflits entre les tests et assurer la cohérence.
 */
import { vi } from 'vitest';

/**
 * Crée un contexte de test isolé avec des mocks frais
 * @param {Object} options - Options de configuration
 * @returns {Object} Contexte de test
 */
export function createTestContext(options = {}) {
  // Logger mock isolé pour ce contexte
  const loggerMock = {
    info: vi.fn().mockName('logger.info'),
    warn: vi.fn().mockName('logger.warn'),
    error: vi.fn().mockName('logger.error'),
    debug: vi.fn().mockName('logger.debug'),
    child: vi.fn().mockImplementation(() => loggerMock)
  };

  // Services mockés
  const aiServiceMock = {
    generateText: vi.fn().mockName('aiService.generateText')
      .mockResolvedValue('mocked response'),
    analyzeCode: vi.fn().mockName('aiService.analyzeCode')
      .mockResolvedValue({
        suggestions: ['Mocked suggestion'],
        qualityScore: 0.8
      }),
    initialize: vi.fn().mockName('aiService.initialize')
      .mockResolvedValue(true),
    constructor: {
      name: 'AIService'
    }
  };

  const deepseekServiceMock = {
    generateText: vi.fn().mockName('deepseekService.generateText')
      .mockResolvedValue('deepseek response'),
    analyzeCode: vi.fn().mockName('deepseekService.analyzeCode')
      .mockResolvedValue({
        suggestions: ['Deepseek suggestion'],
        qualityScore: 0.9
      }),
    init: vi.fn().mockName('deepseekService.init')
      .mockResolvedValue(true),
    constructor: {
      name: 'DeepseekService'
    }
  };

  // Métriques mockées
  const metricsMock = {
    trackRequest: vi.fn().mockName('metrics.trackRequest'),
    getMetrics: vi.fn().mockName('metrics.getMetrics')
      .mockReturnValue({
        totalRequests: 0,
        avgResponseTime: 0,
        successRate: 100
      })
  };

  // Configuration d'environnement
  const setupEnv = () => {
    const originalEnv = { ...process.env };
    
    // Définir les variables d'environnement pour les tests
    process.env.DEEPSEEK_API_KEY = 'test-api-key';
    process.env.NODE_ENV = 'test';
    process.env.LOG_LEVEL = 'silent';
    
    if (options.env) {
      Object.entries(options.env).forEach(([key, value]) => {
        process.env[key] = value;
      });
    }
    
    return originalEnv;
  };

  // Sauvegarder l'environnement original
  const originalEnv = setupEnv();

  // Fonction de nettoyage
  const cleanup = () => {
    vi.clearAllMocks();
    
    // Restaurer l'environnement original
    Object.keys(process.env).forEach(key => {
      delete process.env[key];
    });
    
    Object.entries(originalEnv).forEach(([key, value]) => {
      process.env[key] = value;
    });
  };

  // Retourner le contexte complet
  return {
    logger: loggerMock,
    services: {
      ai: aiServiceMock,
      deepseek: deepseekServiceMock
    },
    metrics: metricsMock,
    env: {
      setup: setupEnv,
      restore: () => {
        Object.keys(process.env).forEach(key => {
          delete process.env[key];
        });
        
        Object.entries(originalEnv).forEach(([key, value]) => {
          process.env[key] = value;
        });
      }
    },
    cleanup,
    
    // Utilitaires pour configurer les mocks
    mockLogger: () => {
      vi.mock('#logger', () => ({
        default: loggerMock,
        logger: loggerMock,
        createLogger: () => loggerMock
      }));
      return loggerMock;
    },
    
    mockAIService: () => {
      vi.mock('../../src/backend/services/aiService', () => ({
        default: vi.fn().mockImplementation(() => aiServiceMock)
      }));
      return aiServiceMock;
    },
    
    mockDeepseekService: () => {
      vi.mock('../../src/backend/services/deepseekService', () => ({
        default: vi.fn().mockImplementation(() => deepseekServiceMock)
      }));
      return deepseekServiceMock;
    },
    
    mockMetrics: () => {
      vi.mock('../../src/utils/aiMetrics', () => metricsMock);
      return metricsMock;
    }
  };
}
