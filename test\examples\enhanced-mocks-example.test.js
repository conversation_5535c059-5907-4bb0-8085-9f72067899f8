/**
 * Exemple d'utilisation des mocks améliorés
 *
 * Ce fichier montre comment utiliser les mocks améliorés pour créer des tests
 * plus robustes et plus faciles à maintenir.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  createEnhancedLoggerMock,
  createEnhancedFSMock,
  createEnhancedNetMock,
  createEnhancedModuleMock,
  createEnhancedRouterMock
} from '../utils/enhancedMocks.js';

// Créer des mocks améliorés
const loggerMock = createEnhancedLoggerMock({ logToConsole: false });
const fsMock = createEnhancedFSMock({
  initialFiles: {
    'config.json': JSON.stringify({ port: 3000 }),
    'data/users.json': JSON.stringify([{ id: 1, name: 'User 1' }])
  }
});
const netMock = createEnhancedNetMock({ occupiedPorts: [3000, 3001] });
const chatModuleMock = createEnhancedModuleMock('chat', {
  methods: {
    sendMessage: (message) => ({
      status: 'success',
      messageId: 'msg-123',
      timestamp: new Date().toISOString()
    })
  }
});
const routerMock = createEnhancedRouterMock({
  modules: {
    'chat': chatModuleMock
  }
});

// Classe d'exemple qui utilise les mocks
class ConfigService {
  constructor(options = {}) {
    this.options = {
      configPath: 'config.json',
      ...options
    };
    
    this.logger = options.logger || loggerMock;
    this.fs = options.fs || fsMock;
    this.net = options.net || netMock;
    this.router = options.router || routerMock;
    
    this.config = null;
  }

  // Charger la configuration
  loadConfig() {
    try {
      this.logger.info(`Chargement de la configuration depuis ${this.options.configPath}`);
      
      const configContent = this.fs.readFileSync(this.options.configPath, 'utf8');
      this.config = JSON.parse(configContent);
      
      this.logger.info('Configuration chargée avec succès', { config: this.config });
      
      return this.config;
    } catch (error) {
      this.logger.error(`Erreur lors du chargement de la configuration: ${error.message}`);
      throw error;
    }
  }

  // Sauvegarder la configuration
  saveConfig(config) {
    try {
      this.logger.info('Sauvegarde de la configuration');
      
      const configContent = JSON.stringify(config, null, 2);
      this.fs.writeFileSync(this.options.configPath, configContent, 'utf8');
      
      this.config = config;
      this.logger.info('Configuration sauvegardée avec succès');
      
      return true;
    } catch (error) {
      this.logger.error(`Erreur lors de la sauvegarde de la configuration: ${error.message}`);
      throw error;
    }
  }

  // Vérifier si un port est disponible
  async isPortAvailable(port) {
    this.logger.debug(`Vérification de la disponibilité du port ${port}`);
    
    const isOccupied = netMock.isPortOccupied(port);
    
    if (isOccupied) {
      this.logger.warn(`Le port ${port} est déjà occupé`);
      return false;
    }
    
    this.logger.info(`Le port ${port} est disponible`);
    return true;
  }

  // Envoyer un message via le module de chat
  async sendChatMessage(content) {
    this.logger.info('Envoi d\'un message via le module de chat');
    
    try {
      const result = await this.router.routeMessage('chat', {
        action: 'sendMessage',
        content
      });
      
      this.logger.info('Message envoyé avec succès', { messageId: result.messageId });
      
      return result;
    } catch (error) {
      this.logger.error(`Erreur lors de l'envoi du message: ${error.message}`);
      throw error;
    }
  }
}

console.log('\n=== DÉBUT DES TESTS ENHANCED MOCKS EXAMPLE ===\n');

describe('Enhanced Mocks Example', () => {
  let configService;

  beforeEach(() => {
    // Réinitialiser tous les mocks
    loggerMock.resetMocks();
    fsMock.resetMocks();
    netMock.resetMocks();
    chatModuleMock.resetMocks();
    routerMock.resetMocks();
    
    // Créer une nouvelle instance du service
    configService = new ConfigService();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('loadConfig', () => {
    it('devrait charger la configuration depuis le fichier', () => {
      // Act
      const config = configService.loadConfig();
      
      // Assert
      expect(config).toEqual({ port: 3000 });
      expect(fsMock.readFileSync).toHaveBeenCalledWith('config.json', 'utf8');
      expect(loggerMock.info).toHaveBeenCalledWith('Configuration chargée avec succès', { config: { port: 3000 } });
    });
    
    it('devrait gérer les erreurs de chargement', () => {
      // Arrange
      fsMock.readFileSync.mockImplementationOnce(() => {
        const error = new Error('Fichier non trouvé');
        error.code = 'ENOENT';
        throw error;
      });
      
      // Act & Assert
      expect(() => configService.loadConfig()).toThrow('Fichier non trouvé');
      expect(loggerMock.error).toHaveBeenCalledWith(expect.stringContaining('Erreur lors du chargement'));
    });
  });

  describe('saveConfig', () => {
    it('devrait sauvegarder la configuration dans le fichier', () => {
      // Arrange
      const newConfig = { port: 4000 };
      
      // Act
      const result = configService.saveConfig(newConfig);
      
      // Assert
      expect(result).toBe(true);
      expect(fsMock.writeFileSync).toHaveBeenCalledWith(
        'config.json',
        JSON.stringify(newConfig, null, 2),
        'utf8'
      );
      expect(loggerMock.info).toHaveBeenCalledWith('Configuration sauvegardée avec succès');
    });
  });

  describe('isPortAvailable', () => {
    it('devrait détecter un port disponible', async () => {
      // Act
      const result = await configService.isPortAvailable(4000);
      
      // Assert
      expect(result).toBe(true);
      expect(loggerMock.info).toHaveBeenCalledWith('Le port 4000 est disponible');
    });
    
    it('devrait détecter un port occupé', async () => {
      // Act
      const result = await configService.isPortAvailable(3000);
      
      // Assert
      expect(result).toBe(false);
      expect(loggerMock.warn).toHaveBeenCalledWith('Le port 3000 est déjà occupé');
    });
  });

  describe('sendChatMessage', () => {
    it('devrait envoyer un message via le module de chat', async () => {
      // Act
      const result = await configService.sendChatMessage('Hello, world!');
      
      // Assert
      expect(result).toEqual({
        status: 'success',
        messageId: 'msg-123',
        timestamp: expect.any(String)
      });
      expect(routerMock.routeMessage).toHaveBeenCalledWith('chat', {
        action: 'sendMessage',
        content: 'Hello, world!'
      });
      expect(loggerMock.info).toHaveBeenCalledWith('Message envoyé avec succès', { messageId: 'msg-123' });
    });
    
    it('devrait gérer les erreurs d\'envoi de message', async () => {
      // Arrange
      routerMock.routeMessage.mockRejectedValueOnce(new Error('Module non disponible'));
      
      // Act & Assert
      await expect(configService.sendChatMessage('Hello, world!')).rejects.toThrow('Module non disponible');
      expect(loggerMock.error).toHaveBeenCalledWith(expect.stringContaining('Erreur lors de l\'envoi'));
    });
  });

  describe('Fonctionnalités avancées des mocks', () => {
    it('devrait permettre de vérifier les logs par niveau', () => {
      // Arrange
      loggerMock.info('Message d\'info');
      loggerMock.error('Message d\'erreur');
      
      // Act & Assert
      expect(loggerMock.getLogs('info')).toHaveLength(1);
      expect(loggerMock.getLogs('error')).toHaveLength(1);
      expect(loggerMock.hasLoggedMessage('info', 'Message d\'info')).toBe(true);
      expect(loggerMock.getLastLog('info').message).toBe('Message d\'info');
    });
    
    it('devrait permettre de manipuler le système de fichiers', () => {
      // Arrange
      fsMock.setFile('nouveau-fichier.txt', 'Contenu du fichier');
      
      // Act & Assert
      expect(fsMock.existsSync('nouveau-fichier.txt')).toBe(true);
      expect(fsMock.readFileSync('nouveau-fichier.txt')).toBe('Contenu du fichier');
      expect(fsMock.getFileSystem()['nouveau-fichier.txt']).toBe('Contenu du fichier');
    });
    
    it('devrait permettre de manipuler les ports occupés', () => {
      // Arrange
      netMock.setPortOccupied(5000);
      
      // Act & Assert
      expect(netMock.isPortOccupied(5000)).toBe(true);
      expect(netMock.getOccupiedPorts()).toContain(5000);
      
      // Libérer le port
      netMock.setPortFree(5000);
      expect(netMock.isPortOccupied(5000)).toBe(false);
    });
    
    it('devrait permettre de modifier les méthodes d\'un module', () => {
      // Arrange
      chatModuleMock.setMethod('getMessages', () => ({
        status: 'success',
        messages: [{ id: 'msg-1', content: 'Hello' }]
      }));
      
      // Act
      const result = chatModuleMock.process({ action: 'getMessages' });
      
      // Assert
      expect(result).toEqual({
        status: 'success',
        messages: [{ id: 'msg-1', content: 'Hello' }]
      });
    });
  });
});

console.log('\n=== FIN DES TESTS ENHANCED MOCKS EXAMPLE ===\n');
