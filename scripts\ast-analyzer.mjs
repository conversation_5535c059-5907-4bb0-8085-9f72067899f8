import fs from 'fs';
import { parse } from '@babel/parser';
import { fileURLToPath } from 'url';
import path from 'path';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export const analyzeExports = (filePath) => {
  const absolutePath = path.resolve(process.cwd(), filePath);
  
  if (!fs.existsSync(absolutePath)) {
    throw new Error(`Fichier source introuvable: ${absolutePath}\n` +
      `Vérifiez que le chemin est relatif à la racine du projet (d:/Test/GDevAI)`);
  }

  const code = fs.readFileSync(absolutePath, 'utf8');
  
  const ast = parse(code, {
    sourceType: 'module',
    plugins: ['typescript', 'jsx']
  });

  const exports = {
    defaultExport: false,
    namedExports: []
  };

  ast.program.body.forEach(node => {
    if (node.type === 'ExportDefaultDeclaration') {
      exports.defaultExport = true;
    } else if (node.type === 'ExportNamedDeclaration') {
      node.specifiers.forEach(spec => {
        exports.namedExports.push(spec.exported.name);
      });
    }
  });

  return exports;
};

// Utilisation :
// const { defaultExport, namedExports } = analyzeExports('chemin/vers/module.mjs');
