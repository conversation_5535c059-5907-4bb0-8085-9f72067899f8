import { vi } from 'vitest';

// Créer un mock de logger standard
const createLoggerMock = () => ({
  debug: vi.fn(),
  info: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  child: vi.fn().mockImplementation(() => createLoggerMock())
});

// Exporter le mock comme export par défaut et comme export nommé
const loggerMock = createLoggerMock();

export default loggerMock;
export const logger = loggerMock;
