class MessageStorage {
    constructor() {
        this.messages = [];
        this.maxHistory = 100; // Nombre max de messages conservés
    }

    addMessage(sender, text) {
        const message = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            sender,
            text
        };
        this.messages.push(message);
        
        // Garder seulement les messages récents
        if (this.messages.length > this.maxHistory) {
            this.messages.shift();
        }
        
        return message;
    }

    getHistory(limit = 20) {
        return this.messages.slice(-limit);
    }

    clear() {
        this.messages = [];
    }
}

export default MessageStorage;
