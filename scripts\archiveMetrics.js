import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const ARCHIVES_DIR = path.join(__dirname, '../logs/metrics_archives')

function calculateAvg(values) {
  if (!values.length) return 0
  return values.reduce((a, b) => a + b, 0) / values.length
}

async function archiveMetrics(metrics) {
  try {
    const date = new Date().toISOString().split('T')[0]
    
    // Créer le dossier d'archives si inexistant
    await fs.mkdir(ARCHIVES_DIR, { recursive: true })

    // Fichier détaillé JSON
    const detailedData = {
      date,
      metrics,
      summary: {
        successRate: calculateAvg(metrics.successRates),
        avgResponseTime: calculateAvg(metrics.responseTimes),
        totalTokens: metrics.tokenUsage.reduce((a, b) => a + b, 0)
      }
    }
    
    await fs.writeFile(
      path.join(ARCHIVES_DIR, `metrics-${date}.json`),
      JSON.stringify(detailedData, null, 2)
    )

    // Ajout au fichier synthétique
    await fs.appendFile(
      path.join(ARCHIVES_DIR, 'summary.log'),
      `${date},${detailedData.summary.successRate},${detailedData.summary.avgResponseTime}\n`
    )

    console.log(`✔ Métriques archivées pour ${date}`)
    return true
  } catch (error) {
    console.error('✖ Erreur d\'archivage:', error.message)
    return false
  }
}

// Exemple d'utilisation:
// archiveMetrics({
//   responseTimes: [120, 150, 110],
//   successRates: [0.95, 0.98, 0.97], 
//   tokenUsage: [450, 500, 480]
// })

export default archiveMetrics
