# Planification Détaillée

## Jour 1 - Tests et Nettoyage
1. Tester MCP :
   - Vérifier les connexions locales
   - Tester chatTool.js
   - Valider les logs

2. Nettoyer package.json :
   - Supprimer les dépendances inutilisées
   - Mettre à jour les versions
   - Vérifier les scripts

## Jour 2 - Intégration GDevelop
1. Tester les APIs :
   - Importer un projet GDevelop
   - Tester l'export JSON
   - Vérifier les performances

2. Workflow d'export :
   - Configurer un export simple
   - Documenter les étapes
   - Préparer un exemple

## Jour 3 - Sécurité et Sauvegarde
1. Vérifier les permissions :
   - Tester chaque rôle RBAC
   - Valider les accès locaux
   - Documenter les cas

2. Sauvegarde :
   - Configurer la sauvegarde locale
   - Tester la restauration
   - Automatiser le processus

## Commandes Clés
```bash
# Tests
npm test
npm run test:mcp

# GDevelop
npm run gdevelop:import
npm run export

# Sécurité
npm run check:rbac
npm run backup
