import { readFileSync } from 'fs';
import path from 'path';
import yaml from 'js-yaml';
import logger from './logger.js';

export const loadRolesConfig = (configPath = path.join(process.cwd(), 'config/roles.yaml')) => {
  try {
    logger.debug(`Chargement configuration RBAC depuis ${configPath}`);
    const fileContent = readFileSync(configPath, 'utf-8');
    const config = yaml.load(fileContent);
    
    if (!config?.roles || typeof config.roles !== 'object') {
      logger.error('Configuration RBAC invalide - structure roles manquante');
      throw new Error('Configuration RBAC invalide - structure roles manquante');
    }
    logger.debug(`Configuration RBAC chargée avec ${Object.keys(config.roles).length} rôles`);
    return config;
  } catch (err) {
    logger.error(`Erreur de chargement RBAC: ${err.message}`, { stack: err.stack });
    throw err;
  }
};

let rolesConfig = null;

export function getRolesConfig() {
  if (!rolesConfig) {
    rolesConfig = loadRolesConfig();
  }
  return rolesConfig;
}

// Fonction interne pour vérifier les permissions
function checkUserPermission(user, requiredPermission, rolesConfig) {
  if (user.permissions?.includes('*')) {
    logger.debug(`Accès accordé via permission globale (*) pour ${user.username || 'unknown'}`);
    return true;
  }
  if (user.permissions?.includes(requiredPermission)) {
    logger.debug(`Accès accordé via permission directe ${requiredPermission} pour ${user.username || 'unknown'}`);
    return true;
  }

  const userRoles = user.roles || [];
  for (const role of userRoles) {
    const roleDef = rolesConfig.roles[role];
    if (roleDef?.permissions.includes('*')) {
      logger.debug(`Accès accordé via rôle ${role} (permission *) pour ${user.username || 'unknown'}`);
      return true;
    }
    if (roleDef?.permissions.includes(requiredPermission)) {
      logger.debug(`Accès accordé via rôle ${role} (${requiredPermission}) pour ${user.username || 'unknown'}`);
      return true;
    }

    const checkRoleInheritance = (roleName) => {
      const role = rolesConfig.roles[roleName];
      if (!role) return false;
      
      if (role.permissions.includes('*') || 
          role.permissions.includes(requiredPermission)) {
        return true;
      }

      for (const parentRole of role.inherits || []) {
        if (checkRoleInheritance(parentRole)) {
          return true;
        }
      }
      return false;
    };

    if (checkRoleInheritance(role)) {
      logger.debug(`Accès accordé via héritage de rôle ${role} pour ${user.username || 'unknown'}`);
      return true;
    }
  }

  return false;
}

export const checkPermission = (requiredPermission) => {
  return (req, res, next) => {
    if (!req.user) {
      logger.warn('Tentative d\'accès non authentifié', { endpoint: req.path });
      return res.status(401).json({ error: 'Utilisateur non authentifié' });
    }

    if (checkUserPermission(req.user, requiredPermission, getRolesConfig())) {
      return next();
    }

    logger.warn(`Accès refusé - permission "${requiredPermission}" requise`, {
      user: req.user.username,
      roles: req.user.roles,
      endpoint: req.path
    });
    return res.status(403).json({ 
      error: `Permission "${requiredPermission}" requise`,
      required: requiredPermission,
      available: req.user.permissions
    });
  };
};

export const hasPermission = (user, requiredPermission) => {
  if (!user) return false;
  return checkUserPermission(user, requiredPermission, getRolesConfig());
};
