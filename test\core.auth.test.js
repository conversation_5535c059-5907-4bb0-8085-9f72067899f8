import { authMiddleware } from '@utils/auth.js'; // Import du module à tester
import { vi, describe, test, expect } from 'vitest'; // Import de Vitest

// Simplifier complètement le test en évitant d'initialiser Core
describe('Core Auth', () => {
  // Réinitialiser les mocks avant les tests
  beforeAll(() => {
    vi.clearAllMocks();
  });

  test('should apply auth middleware correctly', () => {
    // Simuler un appel à authMiddleware
    const req = {};
    const res = {};
    const next = vi.fn();

    // Appeler le middleware
    authMiddleware(req, res, next);

    // Vérifier que le middleware a été appelé
    expect(authMiddleware).toHaveBeenCalled();
    expect(next).toHaveBeenCalled();
  });

  test('should have valid user context', () => {
    // Simuler un appel à authMiddleware
    const req = {};
    const res = {};
    const next = vi.fn();

    // Appeler le middleware
    authMiddleware(req, res, next);

    // Vérifier que le contexte utilisateur est correct
    expect(req.user).toEqual({
      userId: 'test-user-id',
      roles: [],
      permissions: []
    });
  });
});
