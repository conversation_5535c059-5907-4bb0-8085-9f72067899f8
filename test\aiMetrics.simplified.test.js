/**
 * Tests ultra-simplifiés pour les métriques AI
 *
 * Ce fichier contient des tests extrêmement simples pour les métriques AI,
 * sans aucune dépendance à des modules externes complexes.
 */
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createLoggerMock } from './utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Classe simplifiée pour les métriques AI
class AIMetricsService {
  constructor(options = {}) {
    this.logger = options.logger || loggerMock;
    this.metrics = [];
  }

  // Enregistrer des métriques
  logAIMetrics(metrics) {
    const standardizedMetrics = {
      provider: metrics.provider || 'unknown',
      duration: metrics.duration || 0,
      success: metrics.success !== undefined ? metrics.success : true,
      tokenCount: metrics.tokenCount || 0,
      model: metrics.model || 'default',
      timestamp: new Date().toISOString()
    };

    this.metrics.push(standardizedMetrics);
    this.logger.info('AI request metrics', standardizedMetrics);

    return standardizedMetrics;
  }

  // Suivre une requête
  trackRequest(requestData) {
    const metrics = {
      provider: requestData.provider || 'unknown',
      duration: requestData.responseTime || 0,
      success: requestData.success !== undefined ? requestData.success : true,
      tokenCount: requestData.tokens || 0,
      model: requestData.model || 'default'
    };

    return this.logAIMetrics(metrics);
  }

  // Obtenir des statistiques
  async getAIStats() {
    if (this.metrics.length === 0) {
      return {
        avgResponseTime: 0,
        successRate: 0,
        totalTokens: 0
      };
    }

    const totalDuration = this.metrics.reduce((sum, m) => sum + m.duration, 0);
    const successCount = this.metrics.filter(m => m.success).length;
    const totalTokens = this.metrics.reduce((sum, m) => sum + m.tokenCount, 0);

    return {
      avgResponseTime: totalDuration / this.metrics.length,
      successRate: (successCount / this.metrics.length) * 100,
      totalTokens
    };
  }
}

describe('AI Metrics (Ultra-Simplifié)', () => {
  let metricsService;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();

    // Créer une nouvelle instance du service de métriques
    metricsService = new AIMetricsService({ logger: loggerMock });
  });

  it('devrait enregistrer des métriques AI', () => {
    const metrics = {
      provider: 'test',
      duration: 100,
      success: true,
      tokenCount: 50,
      model: 'test-model'
    };

    metricsService.logAIMetrics(metrics);

    expect(loggerMock.info).toHaveBeenCalledWith(
      'AI request metrics',
      expect.objectContaining(metrics)
    );
  });

  it('devrait suivre les requêtes', () => {
    metricsService.trackRequest({
      provider: 'deepseek',
      responseTime: 200,
      success: true,
      tokens: 75
    });

    expect(loggerMock.info).toHaveBeenCalledWith(
      'AI request metrics',
      expect.objectContaining({
        provider: 'deepseek',
        duration: 200,
        success: true,
        tokenCount: 75,
        model: 'default'
      })
    );
  });

  it('devrait retourner des statistiques par défaut', async () => {
    const stats = await metricsService.getAIStats();

    expect(stats).toEqual({
      avgResponseTime: 0,
      successRate: 0,
      totalTokens: 0
    });
  });

  it('devrait calculer des statistiques correctes', async () => {
    // Ajouter quelques métriques
    metricsService.logAIMetrics({
      provider: 'test1',
      duration: 100,
      success: true,
      tokenCount: 50
    });

    metricsService.logAIMetrics({
      provider: 'test2',
      duration: 200,
      success: false,
      tokenCount: 75
    });

    metricsService.logAIMetrics({
      provider: 'test3',
      duration: 300,
      success: true,
      tokenCount: 100
    });

    // Obtenir les statistiques
    const stats = await metricsService.getAIStats();

    // Vérifier les statistiques
    expect(stats).toMatchObject({
      avgResponseTime: 200, // (100 + 200 + 300) / 3
      totalTokens: 225 // 50 + 75 + 100
    });

    // Vérifier la successRate séparément avec une tolérance
    expect(stats.successRate).toBeCloseTo(66.67, 1);
  });
});
