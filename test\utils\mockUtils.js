/**
 * Utilitaires pour faciliter la création et la vérification des mocks
 */
import { vi } from 'vitest';

/**
 * Crée un mock de fonction avec toutes les propriétés de mock nécessaires
 * @param {Function} implementation - L'implémentation de la fonction mockée
 * @returns {Function} - Une fonction mockée avec toutes les propriétés nécessaires
 */
export function createRobustMock(implementation) {
  const mockFn = vi.fn(implementation);
  
  // S'assurer que toutes les méthodes de mock sont disponibles
  if (!mockFn.mockImplementationOnce) {
    Object.defineProperty(mockFn, 'mockImplementationOnce', {
      enumerable: true,
      value: vi.fn().mockImplementation(fn => {
        return vi.fn().mockImplementationOnce(fn);
      })
    });
  }
  
  return mockFn;
}

/**
 * Vérifie si un mock est correctement configuré
 * @param {Function} mockFn - La fonction mockée à vérifier
 * @returns {boolean} - true si le mock est correctement configuré
 */
export function verifyMock(mockFn) {
  if (typeof mockFn !== 'function') {
    console.error('Le mock n\'est pas une fonction');
    return false;
  }
  
  if (!vi.isMockFunction(mockFn)) {
    console.error('La fonction n\'est pas un mock Vitest');
    return false;
  }
  
  if (!mockFn.mockImplementationOnce) {
    console.error('Le mock n\'a pas la méthode mockImplementationOnce');
    return false;
  }
  
  return true;
}

/**
 * Crée un mock pour le module auth.js
 * @returns {Object} - Un objet contenant les mocks pour auth.js
 */
export function createAuthMock() {
  const authMiddlewareMock = createRobustMock((req, res, next) => {
    req.user = { 
      userId: 'test-user-id',
      roles: [],
      permissions: []
    };
    
    if (req.errorTest) {
      throw new Error('Test error handling');
    }
    
    return next();
  });
  
  return {
    authMiddleware: authMiddlewareMock,
    generateToken: vi.fn(() => 'MOCK_TOKEN'),
    verifyToken: vi.fn(() => ({
      userId: 'test-user-id',
      roles: [],
      permissions: []
    }))
  };
}
