import { vi } from 'vitest';
import { EventEmitter } from 'events';

/**
 * Mock standardisé pour le module net
 * Ce mock est conçu pour être utilisé dans tous les tests
 */

// Registre des ports occupés
const occupiedPorts = new Set();

// Classe pour simuler un serveur
class MockServer extends EventEmitter {
  constructor() {
    super();
    this.listening = false;
    this.port = null;
    this.address = null;
  }

  listen(port, host, callback) {
    if (occupiedPorts.has(port)) {
      // Simuler une erreur EADDRINUSE
      const error = new Error(`EADDRINUSE: address already in use ${host}:${port}`);
      error.code = 'EADDRINUSE';
      error.errno = -98;
      error.syscall = 'listen';
      error.address = host;
      error.port = port;
      
      this.emit('error', error);
      if (callback) callback(error);
      return this;
    }

    this.listening = true;
    this.port = port;
    this.address = host;
    occupiedPorts.add(port);
    
    process.nextTick(() => {
      this.emit('listening');
      if (callback) callback();
    });
    
    return this;
  }

  close(callback) {
    if (this.listening && this.port) {
      occupiedPorts.delete(this.port);
    }
    
    this.listening = false;
    this.port = null;
    this.address = null;
    
    process.nextTick(() => {
      this.emit('close');
      if (callback) callback();
    });
    
    return this;
  }

  address() {
    if (!this.listening) return null;
    return {
      address: this.address,
      family: 'IPv4',
      port: this.port
    };
  }
}

// Créer le mock
const netMock = {
  // Méthodes principales
  createServer: vi.fn(() => {
    return new MockServer();
  }),
  
  // Méthodes utilitaires pour les tests
  __setPortOccupied: (port) => {
    occupiedPorts.add(port);
  },
  
  __clearPort: (port) => {
    occupiedPorts.delete(port);
  },
  
  __clearAllPorts: () => {
    occupiedPorts.clear();
  },
  
  __isPortOccupied: (port) => {
    return occupiedPorts.has(port);
  },
  
  // Réinitialiser tous les mocks
  __resetMocks: () => {
    vi.clearAllMocks();
    occupiedPorts.clear();
  }
};

// Exporter les fonctions utilitaires
export const __setPortOccupied = netMock.__setPortOccupied;
export const __clearAllPorts = netMock.__clearAllPorts;

// Exporter le mock
export default netMock;
