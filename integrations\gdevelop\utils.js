export function validateProject(projectData) {
  if (!projectData || typeof projectData !== 'object') {
    return false;
  }

  // Validation des champs obligatoires
  const requiredFields = ['title', 'version', 'objects'];
  for (const field of requiredFields) {
    if (!projectData[field]) {
      return false;
    }
  }

  // Validation des objets de jeu
  if (!Array.isArray(projectData.objects)) {
    return false;
  }

  return true;
}

export function sanitizeInput(input) {
  if (typeof input === 'string') {
    // Nettoyage basique des strings
    return input.trim().replace(/</g, '<');
  }
  return input;
}

export function formatGDevelopError(error) {
  return {
    error: true,
    message: error.message || 'Erreur GDevelop inconnue',
    code: error.code || 'UNKNOWN_ERROR',
    timestamp: new Date().toISOString()
  };
}
