/**
 * Test de diagnostic pour vérifier que les mocks sont correctement configurés
 */
import { vi } from 'vitest';
// Importer les modules mockés pour vérifier leur configuration
import { authMiddleware, generateToken, verifyToken } from '@utils/auth.js';

// Importer dynamiquement le module ES
let verifyMock;

beforeAll(async () => {
  const mockUtils = await import('./utils/mockUtils.js');
  verifyMock = mockUtils.verifyMock;
});

describe('Mock Diagnostics', () => {
  test('authMiddleware est correctement mocké', () => {
    console.log('Type de authMiddleware:', typeof authMiddleware);
    console.log('Est un mock Vitest:', vi.isMockFunction(authMiddleware));
    console.log('A mockImplementationOnce:', !!authMiddleware.mockImplementationOnce);

    expect(typeof authMiddleware).toBe('function');
    expect(vi.isMockFunction(authMiddleware)).toBe(true);
    expect(verifyMock(authMiddleware)).toBe(true);
  });

  test('generateToken est correctement mocké', () => {
    expect(typeof generateToken).toBe('function');
    expect(vi.isMockFunction(generateToken)).toBe(true);
  });

  test('verifyToken est correctement mocké', () => {
    expect(typeof verifyToken).toBe('function');
    expect(vi.isMockFunction(verifyToken)).toBe(true);
  });

  test('authMiddleware fonctionne comme attendu', () => {
    const req = { headers: {} };
    const res = {};
    const next = vi.fn();

    authMiddleware(req, res, next);

    expect(req.user).toBeDefined();
    expect(req.user.userId).toBe('test-user-id');
    expect(next).toHaveBeenCalled();
  });

  test('authMiddleware.mockImplementationOnce fonctionne', () => {
    const req = { headers: {} };
    const res = {};
    const next = vi.fn();

    // Utiliser mockImplementation pour ce test spécifique
    authMiddleware.mockImplementation((req, res, next) => {
      req.user = { userId: 'custom-user-id' };
      next();
    });

    authMiddleware(req, res, next);

    expect(req.user.userId).toBe('custom-user-id');

    // Restaurer l'implémentation par défaut
    authMiddleware.mockImplementation((req, res, next) => {
      req.user = {
        userId: 'test-user-id',
        roles: [],
        permissions: []
      };
      next();
    });
  });
});
