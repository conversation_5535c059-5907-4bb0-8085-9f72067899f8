// Contrôleur sécurisé pour la phase 2
import logger from '#logger';

export default class SecureChatController {
  constructor(chatService, authService) {
    this.chatService = chatService;
    this.authService = authService;
    logger.info('Initialisation SecureChatController');
  }

  async handleSecureMessage(req, res) {
    try {
      logger.info(`Nouveau message de ${req.user.id}`);
      
      // Vérification RBAC
      const hasAccess = await this.authService.checkAccess(
        req.user.role, 
        'chat:write'
      );
      
      if (!hasAccess) {
        logger.warn(`Accès refusé pour ${req.user.role}`);
        return res.status(403).json({ error: 'Accès refusé' });
      }

      const { message } = req.body;
      const response = await this.chatService.processSecureMessage(
        message,
        req.user.id
      );

      res.json({ 
        text: response,
        sender: 'assistant',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Erreur SecureChat:', { error });
      res.status(500).json({ 
        error: 'Erreur interne',
        details: error.message 
      });
    }
  }
}
