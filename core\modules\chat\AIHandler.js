import ResponseBuilder from './ResponseBuilder.js';

class AIHandler {
  constructor(aiService, responseBuilder = new ResponseBuilder()) {
    this.aiService = aiService;
    this.responseBuilder = responseBuilder;
    this.logger = console; // À remplacer par le vrai logger
  }

  async process(message) {
    try {
      this.logger.debug(`Processing AI message: ${message.text}`);
      
      const response = await this.aiService.process({
        action: 'generateText',
        prompt: message.text,
        context: message.context
      });

      if (!response?.data?.text) {
        throw new Error('Invalid AI response format');
      }

      return this.responseBuilder.buildAI({
        data: {
          text: response.data.text,
          tokens: response.data.tokens || 0
        },
        metadata: {
          isAI: true,
          source: response.metadata?.source || 'ai-service'
        }
      });
    } catch (error) {
      this.logger.error(`AI processing failed: ${error.message}`);
      throw error; // Propagate to allow fallback
    }
  }
}

export default AIHandler;
