name: Enhanced CI Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '20'
  NPM_VERSION: 'latest'

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      cache-key: \${{ steps.cache-deps.outputs.cache-key }}
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: \${{ env.NODE_VERSION }}
        cache: 'npm'
        
    - name: Install compatible npm version
      run: |
        npm install -g npm@11.4.0
        echo "NPM version after update: $(npm -v)"
        echo "PATH=$PATH" >> $GITHUB_ENV

    - name: Verify npm version
      run: |
        if [ $(npm --version | cut -d'.' -f1) -lt 11 ] || 
           { [ $(npm --version | cut -d'.' -f1) -eq 11 ] && [ $(npm --version | cut -d'.' -f2) -lt 4 ]; }; then
          echo "ERROR: npm version must be >=11.4.0 (current: $(npm --version))"
          exit 1
        fi

    - name: Force resolutions
      run: npx npm-force-resolutions

    - name: Sync package files
      run: npm install --legacy-peer-deps
      
    - name: Cache dependencies
      id: cache-deps
      uses: actions/cache@v3
      with:
        path: |
          ~/.npm
          node_modules
          */node_modules
        key: \${{ runner.os }}-node-\${{ hashFiles('**/package-lock.json') }}
        restore-keys: |
          \${{ runner.os }}-node-

    - name: Install system dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libsecret-1-dev

  test-unit:
    needs: setup
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: \${{ env.NODE_VERSION }}
        
    - name: Run unit tests with full debug
      run: |
        echo "=== ENVIRONMENT ==="
        echo "Node: $(node -v)"
        echo "NPM: $(npm -v)"
        echo "=== DEPENDENCIES ==="
        npm list --depth=0
        echo "=== TEST CONFIG ==="
        npm config list
        echo "=== RUNNING TESTS ==="
        npm run test:unit -- --bail=1 --coverage --coverageThreshold='{\"global\":80}' --verbose > test-output.log 2>&1 || true
        echo "=== TEST OUTPUT ==="
        cat test-output.log
        echo "=== FAILED TESTS ==="
        grep -E 'FAIL|error' test-output.log || echo "No failed tests found"
      env:
        NODE_ENV: test
        PORT_RANGE_START: 30000
        PORT_RANGE_END: 30100
        DEBUG: '*'

  test-integration:
    needs: setup
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: \${{ env.NODE_VERSION }}
        
    - name: Run integration tests with full debug
      run: |
        echo "=== ENVIRONMENT ==="
        echo "Node: $(node -v)"
        echo "NPM: $(npm -v)"
        echo "=== DEPENDENCIES ==="
        npm list --depth=0
        echo "=== TEST CONFIG ==="
        npm config list
        echo "=== RUNNING TESTS ==="
        npm run test:integration -- --coverage --verbose > test-output.log 2>&1 || true
        echo "=== TEST OUTPUT ==="
        cat test-output.log
        echo "=== FAILED TESTS ==="
        grep -E 'FAIL|error' test-output.log || echo "No failed tests found"
      env:
        NODE_ENV: test
        PORT_RANGE_START: 30101
        PORT_RANGE_END: 30200
        TEST_INTEGRATION_DIR: test/integration
        DEBUG: '*'

  build:
    needs: [test-unit, test-integration]
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: actions/setup-node@v4
      with:
        node-version: \${{ env.NODE_VERSION }}
        
    - name: Build project
      run: npm run build

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-output
        path: dist
