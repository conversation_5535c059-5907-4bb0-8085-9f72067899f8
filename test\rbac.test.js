import { describe, it, expect, beforeEach, vi } from 'vitest';
import { loadRolesConfig } from '../src/utils/rbac.js';
import logger from '#logger';

vi.mock('#logger', () => ({
  default: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
    child: vi.fn(() => ({
      info: vi.fn(),
      error: vi.fn(),
      warn: vi.fn(),
      debug: vi.fn()
    }))
  }
}));
import { readFileSync } from 'fs';
import yaml from 'js-yaml';

// Configurations de test
const mockConfig = {
  roles: {
    admin: {
      permissions: ['*'],
      inherits: []
    },
    user: {
      permissions: ['read'],
      inherits: []
    }
  },
  permissions: {
    '*': 'Full access',
    'read': 'Read access'
  }
};

const invalidConfig = {
  foo: 'bar'
};

// Mocks globaux
vi.mock('fs', () => ({
  readFileSync: vi.fn(),
  existsSync: vi.fn().mockReturnValue(true),
  default: {
    readFileSync: vi.fn()
  }
}));

vi.mock('js-yaml', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    load: vi.fn().mockImplementation(() => mockConfig),
    default: {
      ...actual.default,
      load: vi.fn().mockImplementation(() => mockConfig)
    }
  };
});

// Tests
describe('RBAC Utility Functions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('loadRolesConfig', () => {
    it('should load valid config successfully', () => {
      // 1. Configurer les mocks
      const mockYamlContent = `
roles:
  admin:
    permissions: ['*']
  user:
    permissions: ['read']
`;
      const expectedConfig = {
        roles: {
          admin: { permissions: ['*'] },
          user: { permissions: ['read'] }
        }
      };

      readFileSync.mockReturnValue(mockYamlContent);
      vi.mocked(yaml.load).mockReturnValue(expectedConfig);

      // 2. Exécuter
      const result = loadRolesConfig();

      // 3. Vérifications
      expect(readFileSync).toHaveBeenCalled();
      expect(yaml.load).toHaveBeenCalledWith(mockYamlContent);
      expect(result).toEqual(expectedConfig);
      expect(logger.error).not.toHaveBeenCalled();
    });

    it('should log error when config loading fails', () => {
      readFileSync.mockImplementation(() => {
        throw new Error('File read error');
      });
      expect(() => loadRolesConfig()).toThrow();
      expect(logger.error).toHaveBeenCalled();
    });

    it('should throw error when roles structure is invalid', () => {
      readFileSync.mockReturnValue('invalid: yaml');
      vi.mocked(yaml.load).mockReturnValue(invalidConfig);
      expect(() => loadRolesConfig()).toThrow('Configuration RBAC invalide');
      expect(logger.error).toHaveBeenCalledWith(
        'Configuration RBAC invalide - structure roles manquante'
      );
    });

    it('should handle missing roles key', () => {
      readFileSync.mockReturnValue('{}');
      vi.mocked(yaml.load).mockReturnValue({});
      expect(() => loadRolesConfig()).toThrow('Configuration RBAC invalide');
    });
  });
});
