// Service de traitement des messages du chat
import fs from 'fs/promises';
import path from 'path';
import MessageStorage from './MessageStorage.js';

export default class ChatService {
  constructor() {
    this.storage = new MessageStorage();
    this.MAX_TOKENS = 10000;
  }

  async saveContext() {
    try {
      this.logger?.debug('Saving chat context...');
      const context = {
        messages: this.storage.getHistory(),
        tokenCount: this.calculateTokenCount(),
        timestamp: new Date().toISOString()
      };
      await fs.writeFile('./CONTEXT.md', JSON.stringify(context, null, 2));
      this.logger?.debug('Context saved successfully');
      await this.updateWorklog('Context saved');
    } catch (error) {
      this.logger?.error('Failed to save context:', error);
      throw error;
    }
  }

  async loadContext() {
    try {
      this.logger?.debug('Loading chat context...');
      const data = await fs.readFile('./CONTEXT.md', 'utf8');
      const { messages } = JSON.parse(data);
      messages.forEach(msg => this.storage.addMessage(msg.sender, msg.text));
      this.logger?.debug(`Loaded ${messages.length} messages from context`);
    } catch (err) {
      if (err.code !== 'ENOENT') {
        this.logger?.error('Failed to load context:', err);
      }
    }
  }

  calculateTokenCount() {
    return this.storage.getHistory()
      .reduce((count, msg) => count + msg.text.split(' ').length, 0);
  }

  async updateWorklog(action) {
    const logEntry = {
      date: new Date().toISOString(),
      action,
      details: `Updated context with ${this.storage.getHistory().length} messages`,
      author: 'System'
    };
    
    const worklogPath = path.join(process.cwd(), 'WORKLOG.md');
    let worklog = '# Historique des opérations\n\n';
    try {
      worklog = await fs.readFile(worklogPath, 'utf8');
    } catch (err) {
      await fs.writeFile(worklogPath, worklog);
    }
    
    const updatedLog = worklog + `\n## ${new Date().toISOString()}\n${JSON.stringify(logEntry, null, 2)}\n`;
    await fs.writeFile(worklogPath, updatedLog);
  }

  async processMessage(message) {
    try {
      this.logger?.debug('Processing message from user');
      this.storage.addMessage('user', message);
      
      const response = {
        data: {
          text: `Réponse à: "${message}"`,
          contextId: Date.now().toString()
        },
        metadata: {
          timestamp: new Date().toISOString(),
          isAI: false,
          source: 'chatService'
        }
      };

      this.logger?.debug('Message processed successfully');
      return response;
    } catch (error) {
      this.logger?.error('Message processing failed:', {
        error: error.message,
        stack: error.stack
      });
      
      return {
        data: null,
        metadata: {
          timestamp: new Date().toISOString(),
          failed: true
        },
        error: {
          code: 'CHAT_001',
          message: error.message,
          details: 'Failed to process user message'
        }
      };
    }
  }
}
