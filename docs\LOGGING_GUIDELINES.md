# Bonnes pratiques pour les logs

## Niveaux de log

### error
- Erre<PERSON> critiques empêchant le fonctionnement normal
- Exemple : Échec de connexion à la base de données

### warn
- Problèmes non bloquants mais nécessitant attention
- Exemple : Permission refusée pour un utilisateur

### info
- Informations opérationnelles importantes
- Exemple : Démarrage d'un service

### debug
- Détails techniques pour le développement
- Exemple : Contenu d'une requête

## Format recommandé
```js
logger.info(`Message traité - user: ${user.id}`, { 
  context: 'chatService',
  messageId: msg.id 
});
```

## Bonnes pratiques
- Toujours fournir un contexte clair
- Structurer les données supplémentaires en objet
- Éviter les données sensibles dans les logs
- Utiliser des templates strings pour les variables
