#!/usr/bin/env node

/**
 * Script pour tester un seul fichier de test simplifié
 */

import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Récupérer le fichier de test depuis les arguments
const testFile = process.argv[2];

if (!testFile) {
  console.error('❌ Veuillez spécifier un fichier de test');
  console.log('Usage: node scripts/test-single-simplified.js <test-file>');
  process.exit(1);
}

console.log(`🧪 Test du fichier: ${testFile}`);

// Exécuter le test avec vitest
const vitestProcess = spawn('node', [
  path.join(__dirname, '../node_modules/vitest/dist/cli.js'),
  'run',
  testFile,
  '--reporter=verbose'
], {
  stdio: 'inherit',
  cwd: path.join(__dirname, '..')
});

vitestProcess.on('close', (code) => {
  if (code === 0) {
    console.log(`✅ ${testFile} - SUCCÈS`);
  } else {
    console.log(`❌ ${testFile} - ÉCHEC`);
  }
  process.exit(code);
});
