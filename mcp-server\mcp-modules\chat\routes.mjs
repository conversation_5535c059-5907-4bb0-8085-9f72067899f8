import { Router } from 'express';
import ChatService from './logic.mjs';
import ChatTool from './tools.mjs';

const router = Router();

// Route pour vérifier le statut du module
router.get('/status', async (req, res) => {
  try {
    res.json({ 
      status: 'OK',
      version: '1.0.0',
      service: 'Chat Module'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Route pour envoyer un message
router.post('/send', async (req, res) => {
  try {
    const { user, message } = req.body;
    if (!user || !message) {
      throw new Error('User and message are required');
    }
    const result = await ChatService.sendMessage({ user, message });
    res.json({
      success: true,
      message: 'Message sent successfully',
      data: result
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Route pour récupérer l'historique
router.get('/history', async (req, res) => {
  try {
    const history = await ChatService.getMessageHistory();
    res.json(history);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

export default router;
