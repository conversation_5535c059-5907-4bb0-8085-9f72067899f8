# Checklist Développement MCP v5.0

## Avant Commit
- [ ] Tests RBAC sur les endpoints MCP
- [ ] Validation rotation des clés API
- [ ] Tests de charge (1000+ req/s)

## Avant Déploiement
- [ ] Vérifier l'expiration des clés dans config.js
- [ ] Confirmer les métriques Prometheus
- [ ] Tester les scénarios d'erreur

## Après Déploiement
- [ ] Monitorer le dashboard Grafana
- [ ] Vérifier les logs d'erreur
- [ ] Confirmer la rétrocompatibilité
