import { vi, describe, it, expect, afterEach, beforeAll } from 'vitest';
// Import des mocks depuis la configuration centralisée
import { setupAllMocks } from '../../mocks';
import MockMCPServer from '../../../test/mocks/mcpServer.js';

// Configurer tous les mocks nécessaires
setupAllMocks({
  mockNet: true,
  mockChildProcess: true
});

// Import des mocks après configuration
import netMock from '../../../test/mocks/net.js';
import childProcessMock from '../../../test/mocks/child_process.js';

// Extraire les fonctions utilitaires des mocks
const { __setPortOccupied, __clearAllPorts } = netMock;
const { __clearAllProcesses } = childProcessMock;

// Import des modules après les mocks
import { createServer } from 'net';

describe('Port Management', () => {

  afterEach(() => {
    vi.clearAllMocks();
    __clearAllPorts();
    __clearAllProcesses();
  });

  it('should find occupied port', async () => {
    // Configurer un port occupé
    __setPortOccupied(5003);

    // Créer un serveur de test pour vérifier si le port est occupé
    const checkPort = (port) => {
      return new Promise(resolve => {
        const tester = createServer()
          .once('error', () => resolve(true))
          .once('listening', () => {
            tester.close();
            resolve(false);
          })
          .listen(port);
      });
    };

    // Vérifier que le port est détecté comme occupé
    const isOccupied = await checkPort(5003);
    expect(isOccupied).toBe(true);
  });

  it('should use auto-assigned port', async () => {
    const port = 50000 + Math.floor(Math.random() * 10000);
    expect(port).toBeGreaterThan(0);
    expect(port).toBeLessThanOrEqual(65535);
  });

  it('should handle invalid port numbers', () => {
    const validatePort = (port) => {
      if (isNaN(port)) throw new Error('Invalid port');
      return true;
    };
    expect(() => validatePort('abc')).toThrow();
  });

  it('should handle port liberation correctly', async () => {
    // Créer une instance du serveur MCP mocké
    const server = new MockMCPServer();

    // Espionner la méthode forceKill
    const forceKillSpy = vi.spyOn(server, 'forceKill');

    // Appeler directement forceKill pour éviter la boucle infinie
    await server.forceKill(7000);

    // Vérifier que forceKill a été appelé
    expect(forceKillSpy).toHaveBeenCalledWith(7000);
  });
});
