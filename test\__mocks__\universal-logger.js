/**
 * Mock universel pour le logger
 * Compatible avec tous les types d'imports
 */
import { vi } from 'vitest';

// Créer un mock de base pour le logger
const createLoggerMock = () => {
  const loggerInstance = {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
    trace: vi.fn(),
    child: vi.fn()
  };

  // Faire en sorte que child() retourne une nouvelle instance du logger
  loggerInstance.child.mockImplementation(() => createLoggerMock());

  return loggerInstance;
};

// Créer l'instance principale du logger
const loggerMock = createLoggerMock();

// Fonction pour réinitialiser les mocks
const resetLoggerMocks = () => {
  loggerMock.info.mockClear();
  loggerMock.error.mockClear();
  loggerMock.warn.mockClear();
  loggerMock.debug.mockClear();
  loggerMock.trace.mockClear();
  loggerMock.child.mockClear();
};

// Fonction pour créer le logger (utilisée par certains modules)
const createLogger = vi.fn().mockImplementation(() => loggerMock);

// Ajouter des propriétés et méthodes supplémentaires au logger
loggerMock.createLogger = createLogger;
loggerMock.resetLoggerMocks = resetLoggerMocks;
loggerMock.logger = loggerMock; // Pour les imports de type { logger }

// Fonctions utilitaires souvent utilisées avec le logger
loggerMock.logRequest = vi.fn();
loggerMock.logError = vi.fn();

// Exporter toutes les variantes possibles
export default loggerMock;
export { loggerMock as logger };
export { resetLoggerMocks };
export { createLogger };

// Pour la compatibilité avec les imports CommonJS
// Note: Ne pas utiliser module.exports directement car cela peut causer des problèmes
// avec les modules ESM
