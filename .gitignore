# Fichiers de dépendances
node_modules/
npm-debug.log

# Fichiers de configuration locale
config/private.key
config/public.key
config/roles.json

# Fichiers de build et logs
dist/
logs/
coverage/

# Fichiers système
.DS_Store
Thumbs.db

# Fichiers de configuration locaux
*.env.*
.secure-vault
config/backup.json
scripts/backup.js
# Fichiers IDE
.vscode/
.idea/

# Fichiers temporaires
*.tmp
*.bak
*.log
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# Exclusions spécifiques
test_restore/
*.7z
project_backup.*
html/assets/*
!html/assets/index.*

# Fichiers de sauvegarde
backups/
temp/

# Fichiers de cache
.cache/
.temp/

# Fichiers de tests
test-results.json
test/__snapshots__/
