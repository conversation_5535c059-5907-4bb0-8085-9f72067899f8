import { vi, describe, beforeEach, afterEach, it, expect } from 'vitest';

// Créer un mock simple pour le module de chat
const createMockChatModule = () => ({
  process: vi.fn(),
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    debug: vi.fn()
  },
  responseBuilder: {
    buildStandard: vi.fn(),
    buildAI: vi.fn(),
    buildError: vi.fn()
  },
  serviceHandler: {
    process: vi.fn()
  },
  aiHandler: {
    process: vi.fn()
  },
  errorHandler: {
    handle: vi.fn()
  }
});

describe('Chat Module Tests', () => {
  let chatModule;

  beforeEach(() => {
    vi.clearAllMocks();

    // Créer le module de chat mock
    chatModule = createMockChatModule();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should process standard messages', async () => {
    // Configurer le mock pour retourner une réponse standard
    chatModule.process.mockResolvedValueOnce({
      text: 'Mock response to: Hello',
      sender: 'assistant',
      isAI: false,
      timestamp: new Date().toISOString()
    });

    const response = await chatModule.process({
      text: 'Hello',
      useAI: false
    });

    expect(response).toEqual({
      text: 'Mock response to: Hello',
      sender: 'assistant',
      isAI: false,
      timestamp: expect.any(String)
    });

    // Vérifier que process a été appelé avec les bons arguments
    expect(chatModule.process).toHaveBeenCalledWith({
      text: 'Hello',
      useAI: false
    });
  });

  it('should process AI messages', async () => {
    // Configurer le mock pour retourner une réponse AI
    chatModule.process.mockResolvedValueOnce({
      text: 'Mock AI response',
      sender: 'assistant',
      isAI: true,
      tokens: 150,
      timestamp: new Date().toISOString()
    });

    const response = await chatModule.process({
      text: 'How does this work?',
      useAI: true
    });

    expect(response).toEqual({
      text: 'Mock AI response',
      sender: 'assistant',
      isAI: true,
      tokens: 150,
      timestamp: expect.any(String)
    });

    // Vérifier que process a été appelé avec les bons arguments
    expect(chatModule.process).toHaveBeenCalledWith({
      text: 'How does this work?',
      useAI: true
    });
  });

  it('should handle service errors', async () => {
    // Configurer le mock pour retourner une réponse d'erreur
    chatModule.process.mockResolvedValueOnce({
      text: 'Service unavailable',
      sender: 'system',
      isAI: false,
      timestamp: new Date().toISOString()
    });

    const response = await chatModule.process({
      text: 'Error test',
      useAI: false
    });

    expect(response).toEqual({
      text: 'Service unavailable',
      sender: 'system',
      isAI: false,
      timestamp: expect.any(String)
    });

    // Vérifier que process a été appelé avec les bons arguments
    expect(chatModule.process).toHaveBeenCalledWith({
      text: 'Error test',
      useAI: false
    });
  });

  it('should fallback to standard service when AI fails', async () => {
    // Configurer le mock pour retourner une réponse de fallback
    chatModule.process.mockResolvedValueOnce({
      text: 'Fallback response',
      sender: 'assistant',
      isAI: false,
      timestamp: new Date().toISOString()
    });

    const response = await chatModule.process({
      text: 'AI fallback test',
      useAI: true
    });

    expect(response).toEqual({
      text: 'Fallback response',
      sender: 'assistant',
      isAI: false,
      timestamp: expect.any(String)
    });

    // Vérifier que process a été appelé avec les bons arguments
    expect(chatModule.process).toHaveBeenCalledWith({
      text: 'AI fallback test',
      useAI: true
    });
  });

  it('should log message processing', async () => {
    // Configurer le mock pour retourner une réponse standard
    chatModule.process.mockResolvedValueOnce({
      text: 'Test response',
      sender: 'assistant',
      isAI: false,
      timestamp: new Date().toISOString()
    });

    const message = {
      text: 'Test log message',
      useAI: false,
      userId: 'test123'
    };

    await chatModule.process(message);

    // Vérifier que process a été appelé avec les bons arguments
    expect(chatModule.process).toHaveBeenCalledWith(message);
  });

  it('should use ResponseBuilder correctly', () => {
    // Configurer les mocks pour retourner des réponses spécifiques
    chatModule.responseBuilder.buildStandard.mockReturnValueOnce({
      text: 'Test',
      sender: 'assistant',
      isAI: false,
      timestamp: new Date().toISOString()
    });

    chatModule.responseBuilder.buildAI.mockReturnValueOnce({
      text: 'AI Test',
      sender: 'assistant',
      isAI: true,
      tokens: 200,
      timestamp: new Date().toISOString()
    });

    chatModule.responseBuilder.buildError.mockReturnValueOnce({
      text: 'Test Error',
      sender: 'system',
      isAI: false,
      timestamp: new Date().toISOString()
    });

    // Tester les méthodes du ResponseBuilder
    const standardResponse = chatModule.responseBuilder.buildStandard('Test');
    expect(standardResponse).toEqual({
      text: 'Test',
      sender: 'assistant',
      isAI: false,
      timestamp: expect.any(String)
    });

    const aiResponse = chatModule.responseBuilder.buildAI('AI Test');
    expect(aiResponse).toEqual({
      text: 'AI Test',
      sender: 'assistant',
      isAI: true,
      tokens: 200,
      timestamp: expect.any(String)
    });

    const errorResponse = chatModule.responseBuilder.buildError('Test Error');
    expect(errorResponse).toEqual({
      text: 'Test Error',
      sender: 'system',
      isAI: false,
      timestamp: expect.any(String)
    });

    // Vérifier que les méthodes ont été appelées avec les bons arguments
    expect(chatModule.responseBuilder.buildStandard).toHaveBeenCalledWith('Test');
    expect(chatModule.responseBuilder.buildAI).toHaveBeenCalledWith('AI Test');
    expect(chatModule.responseBuilder.buildError).toHaveBeenCalledWith('Test Error');
  });
});
