/**
 * Tests ultra-simplifiés pour le système de logging
 *
 * Ce fichier contient des tests extrêmement simples pour le système de logging,
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
import { describe, test, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from './utils/simpleMocks.js';

// Classe simplifiée pour le logger
class SimpleLogger {
  constructor(options = {}) {
    this.options = {
      level: options.level || 'info',
      format: options.format || 'json',
      directory: options.directory || './logs',
      filename: options.filename || 'app.log',
      ...options
    };

    this.logs = [];
    this.fs = options.fs || {
      writeFile: vi.fn((path, content, callback) => {
        this.logs.push({ path, content });
        callback(null);
      }),
      mkdir: vi.fn((path, options, callback) => {
        if (typeof options === 'function') {
          callback = options;
        }
        callback(null);
      }),
      access: vi.fn((path, callback) => {
        callback(null);
      })
    };
  }

  // Méthodes de logging
  info(message, meta = {}) {
    return this._log('info', message, meta);
  }

  error(message, meta = {}) {
    return this._log('error', message, meta);
  }

  warn(message, meta = {}) {
    return this._log('warn', message, meta);
  }

  debug(message, meta = {}) {
    if (this.options.level === 'debug') {
      return this._log('debug', message, meta);
    }
    return null;
  }

  // Méthode interne pour enregistrer un log
  _log(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      level,
      message,
      timestamp,
      ...meta
    };

    // Simuler l'écriture dans un fichier
    const logPath = `${this.options.directory}/${this.options.filename}`;
    const logContent = JSON.stringify(logEntry);

    this.fs.mkdir(this.options.directory, { recursive: true }, (err) => {
      if (err) return;
      this.fs.writeFile(logPath, logContent + '\n', (err) => {
        // Ignorer les erreurs dans ce test simplifié
      });
    });

    return logEntry;
  }

  // Récupérer tous les logs enregistrés (pour les tests)
  getAllLogs() {
    return this.logs;
  }
}

describe('Système de Logging (Ultra-Simplifié)', () => {
  let logger;
  let fsMock;

  beforeEach(() => {
    // Créer un mock pour fs
    fsMock = {
      writeFile: vi.fn((path, content, callback) => callback(null)),
      mkdir: vi.fn((path, options, callback) => {
        if (typeof options === 'function') {
          callback = options;
        }
        callback(null);
      }),
      access: vi.fn((path, callback) => callback(null))
    };

    // Créer une nouvelle instance du logger
    logger = new SimpleLogger({ fs: fsMock });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('devrait initialiser avec les options par défaut', () => {
    expect(logger.options.level).toBe('info');
    expect(logger.options.format).toBe('json');
    expect(logger.options.directory).toBe('./logs');
    expect(logger.options.filename).toBe('app.log');
  });

  test('devrait enregistrer un message info', () => {
    const logEntry = logger.info('Test message');

    expect(logEntry).toEqual({
      level: 'info',
      message: 'Test message',
      timestamp: expect.any(String)
    });

    expect(fsMock.mkdir).toHaveBeenCalled();
    expect(fsMock.writeFile).toHaveBeenCalledWith(
      './logs/app.log',
      expect.stringContaining('"level":"info"'),
      expect.any(Function)
    );
  });

  test('devrait enregistrer un message error', () => {
    const logEntry = logger.error('Error message', { code: 500 });

    expect(logEntry).toEqual({
      level: 'error',
      message: 'Error message',
      timestamp: expect.any(String),
      code: 500
    });

    expect(fsMock.writeFile).toHaveBeenCalledWith(
      './logs/app.log',
      expect.stringContaining('"level":"error"'),
      expect.any(Function)
    );
  });

  test('devrait inclure des métadonnées', () => {
    const metadata = {
      userId: '123',
      action: 'login',
      ip: '127.0.0.1'
    };

    const logEntry = logger.info('User action', metadata);

    expect(logEntry).toEqual({
      level: 'info',
      message: 'User action',
      timestamp: expect.any(String),
      ...metadata
    });

    expect(fsMock.writeFile).toHaveBeenCalledWith(
      './logs/app.log',
      expect.stringContaining('"userId":"123"'),
      expect.any(Function)
    );
  });

  test('ne devrait pas enregistrer les messages debug si le niveau est info', () => {
    logger = new SimpleLogger({ level: 'info', fs: fsMock });

    logger.debug('Debug message');

    expect(fsMock.writeFile).not.toHaveBeenCalled();
  });

  test('devrait enregistrer les messages debug si le niveau est debug', () => {
    logger = new SimpleLogger({ level: 'debug', fs: fsMock });

    logger.debug('Debug message');

    expect(fsMock.writeFile).toHaveBeenCalledWith(
      './logs/app.log',
      expect.stringContaining('"level":"debug"'),
      expect.any(Function)
    );
  });
});
