/**
 * Mock amélio<PERSON> pour child_process
 * Utilisé principalement pour les tests de gestion des ports
 */
import { vi } from 'vitest';

// Simuler les processus en cours d'exécution
const runningProcesses = new Map();

// Mock pour exec
const exec = vi.fn((command, callback) => {
  // Simuler netstat pour trouver les processus par port
  if (command.includes('netstat') && command.includes('findstr')) {
    const portMatch = command.match(/:(\d+)/);
    if (portMatch) {
      const port = parseInt(portMatch[1], 10);
      
      // Vérifier si le port est utilisé par un processus simulé
      if (runningProcesses.has(port)) {
        const pid = runningProcesses.get(port);
        const output = `  TCP    0.0.0.0:${port}           0.0.0.0:0              LISTENING       ${pid}`;
        process.nextTick(() => callback(null, output, null));
      } else {
        // Port libre
        process.nextTick(() => callback(null, '', null));
      }
    } else {
      // Commande netstat mal formée
      process.nextTick(() => callback(new Error('Invalid netstat command'), null, 'Command syntax error'));
    }
  }
  // Simuler taskkill pour tuer un processus
  else if (command.includes('taskkill') && command.includes('/PID')) {
    const pidMatch = command.match(/\/PID\s+(\d+)/);
    if (pidMatch) {
      const pid = parseInt(pidMatch[1], 10);
      
      // Chercher le port associé à ce PID
      let portToFree = null;
      for (const [port, processPid] of runningProcesses.entries()) {
        if (processPid === pid) {
          portToFree = port;
          break;
        }
      }
      
      if (portToFree !== null) {
        runningProcesses.delete(portToFree);
        process.nextTick(() => callback(null, `SUCCESS: The process with PID ${pid} has been terminated.`, null));
      } else {
        // PID non trouvé
        process.nextTick(() => callback(new Error(`Process with PID ${pid} not found`), null, `ERROR: The process "${pid}" not found.`));
      }
    } else {
      // Commande taskkill mal formée
      process.nextTick(() => callback(new Error('Invalid taskkill command'), null, 'Command syntax error'));
    }
  }
  // Autres commandes
  else {
    process.nextTick(() => callback(new Error('Command not supported by mock'), null, 'Command not supported'));
  }
});

// Mock pour execSync
const execSync = vi.fn((command) => {
  // Simuler netstat pour trouver les processus par port
  if (command.includes('netstat') && command.includes('findstr')) {
    const portMatch = command.match(/:(\d+)/);
    if (portMatch) {
      const port = parseInt(portMatch[1], 10);
      
      // Vérifier si le port est utilisé par un processus simulé
      if (runningProcesses.has(port)) {
        const pid = runningProcesses.get(port);
        return Buffer.from(`  TCP    0.0.0.0:${port}           0.0.0.0:0              LISTENING       ${pid}`);
      } else {
        // Port libre
        return Buffer.from('');
      }
    } else {
      // Commande netstat mal formée
      throw new Error('Invalid netstat command');
    }
  }
  // Simuler taskkill pour tuer un processus
  else if (command.includes('taskkill') && command.includes('/PID')) {
    const pidMatch = command.match(/\/PID\s+(\d+)/);
    if (pidMatch) {
      const pid = parseInt(pidMatch[1], 10);
      
      // Chercher le port associé à ce PID
      let portToFree = null;
      for (const [port, processPid] of runningProcesses.entries()) {
        if (processPid === pid) {
          portToFree = port;
          break;
        }
      }
      
      if (portToFree !== null) {
        runningProcesses.delete(portToFree);
        return Buffer.from(`SUCCESS: The process with PID ${pid} has been terminated.`);
      } else {
        // PID non trouvé
        throw new Error(`Process with PID ${pid} not found`);
      }
    } else {
      // Commande taskkill mal formée
      throw new Error('Invalid taskkill command');
    }
  }
  // Autres commandes
  else {
    throw new Error('Command not supported by mock');
  }
});

// Méthodes utilitaires pour les tests
const __setProcessOnPort = (port, pid) => {
  runningProcesses.set(port, pid);
};

const __clearAllProcesses = () => {
  runningProcesses.clear();
};

// Exporter le mock
export { exec, execSync, __setProcessOnPort, __clearAllProcesses };
export default { exec, execSync, __setProcessOnPort, __clearAllProcesses };
