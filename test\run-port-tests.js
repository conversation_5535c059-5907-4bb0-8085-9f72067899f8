// Script pour exécuter les tests de port manuellement
import { describe, it, expect, vi } from 'vitest';

// Mocks simples pour le module net
const mockCreateServer = vi.fn();
const mockServer = {
  once: vi.fn(),
  listen: vi.fn(),
  close: vi.fn()
};

// Fonction checkPort isolée pour le test
async function checkPort(port) {
  return new Promise(resolve => {
    const tester = mockServer
      .once('error', () => resolve(true))
      .once('listening', () => {
        tester.close();
        resolve(false);
      })
      .listen(port);
  });
}

// Tests manuels
async function runTests() {
  console.log('=== DÉBUT DES TESTS MANUELS ===');
  
  // Test 1: Port libre
  console.log('\nTest 1: Port libre');
  mockCreateServer.mockReturnValue(mockServer);
  mockServer.once.mockImplementation((event, callback) => {
    if (event === 'listening') {
      setTimeout(() => callback(), 10);
    }
    return mockServer;
  });
  mockServer.listen.mockReturnValue(mockServer);
  
  const result1 = await checkPort(7000);
  console.log(`Résultat: ${result1 === false ? 'PASS ✅' : 'FAIL ❌'}`);
  
  // Test 2: Port occupé
  console.log('\nTest 2: Port occupé');
  vi.clearAllMocks();
  mockCreateServer.mockReturnValue(mockServer);
  mockServer.once.mockImplementation((event, callback) => {
    if (event === 'error') {
      setTimeout(() => callback(new Error('EADDRINUSE')), 10);
    }
    return mockServer;
  });
  mockServer.listen.mockReturnValue(mockServer);
  
  const result2 = await checkPort(7000);
  console.log(`Résultat: ${result2 === true ? 'PASS ✅' : 'FAIL ❌'}`);
  
  console.log('\n=== FIN DES TESTS MANUELS ===');
}

// Exécuter les tests
runTests().catch(err => {
  console.error('Erreur dans les tests:', err);
  process.exit(1);
});
