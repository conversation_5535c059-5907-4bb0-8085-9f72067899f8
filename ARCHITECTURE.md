# Architecture GDevAI v1.0

## 1. Vue d'Ensemble
```mermaid
graph TD
    A[Frontend] --> B[Backend API]
    B --> C[Services]
    C --> D[(Database)]
    C --> E[GDevelop API]
    C --> F[AI Models]
    B --> G[MCP Server]
    G --> H[Tools]
    G --> I[Resources]
```

## 2. Composants Clés

### 2.1 Frontend (React/Vite)
- Interface utilisateur
- Communication avec backend via REST
- Gestion d'état avec Redux

### 2.2 Backend (Node.js/Express)
- API REST principale
- Authentification (JWT)
- Gestion des permissions (RBAC)

### 2.3 MCP Server
- Serveur d'outils extensible
- Découverte automatique
- Exécution sécurisée

## 3. Modules Principaux

### 3.1 AI Module
- Intégration avec fournisseurs d'IA
- Gestion des modèles
- Cache des réponses

### 3.2 Chat Module
- Historique des conversations
- Gestion des sessions
- Intégration avec GDevelop

### 3.3 RBAC Module
- Gestion des rôles
- Vérification des permissions
- Chargement dynamique de config

## 4. Flux de Données

```mermaid
sequenceDiagram
    Frontend->>+Backend: Requête API
    Backend->>+RBAC: Vérification permission
    RBAC-->>-Backend: OK
    Backend->>+AI_Service: Appel modèle IA
    AI_Service-->>-Backend: Réponse
    Backend-->>-Frontend: Résultat
```

## 5. Sécurité

### 5.1 Authentification
- JWT avec expiration courte
- Refresh tokens
- Rotation des clés

### 5.2 RBAC
- Rôles: admin, developer, user
- Permissions granulaires
- Chargement depuis YAML/JSON

## 6. Performance

### 6.1 Optimisations
- Cache Redis
- Pool de connexions DB
- Load balancing

### 6.2 Monitoring
- Métriques Prometheus
- Logs centralisés
- Alerting
