# Guide Technique des Sauvegardes

## 🔧 Configuration Requise

### Prérequis
- 7-Zip 24.09+ (recommandé) ou PowerShell 5.1+
- Node.js v20+
- 500MB d'espace libre minimum
- Droits administrateur pour la configuration des tâches planifiées

## ⚙️ Paramètres de Compression

### Options 7-Zip
```bash
-t7z    # Format d'archive
-mx9    # Niveau de compression maximum  
-mmt4   # Utilisation de 4 threads CPU
-bb3    # Niveau de journalisation
```

### Comparaison des Méthodes
| Méthode       | Taux Compression | Vitesse | Fichiers Supportés |
|---------------|------------------|---------|--------------------|
| 7-Zip (LZMA2) | ~70%             | Moyenne | Tous               |
| PowerShell    | ~50%             | Rapide  | Limités            |

## 🔄 Stratégie de Rotation

```mermaid
gantt
    title Rotation des Sauvegardes
    dateFormat  YYYY-MM-DD
    section Quotidiennes
    J-7 :done, 2025-05-05, 2025-05-12
    section Hebdomadaires
    S-4 :active, 2025-04-14, 2025-05-12
    section Mensuelles
    M-12 :crit, 2024-05-12, 2025-05-12
```

## 🛠️ Dépannage

### Erreurs Courantes
1. **7-Zip non trouvé** :
   ```bash
   # Solution 1 : Installer 7-Zip x64
   # Solution 2 : Définir SEVEN_ZIP_PATH dans .env
   ```

2. **Echec de suppression** :
   - Attendre que le processus de compression se termine
   - Vérifier les permissions

## ⏱️ Planification Automatique

### Tâches Configurées
1. **Quotidienne** : Exécution à 02:00
2. **Au démarrage** : Vérification et rattrapage si nécessaire

```mermaid
sequenceDiagram
    Activate DailyTask
    DailyTask->>+2h: Backup complet
    2h-->>-DailyTask: Succès
    Activate StartupCheck
    StartupCheck->>+Système: PC démarré ?
    Système-->>-StartupCheck: Oui
    StartupCheck->>Backup: Dernier backup >24h ?
    Backup-->>StartupCheck: Oui
    StartupCheck->>Backup: Lancement rapide
```

## 📈 Bonnes Pratiques

1. Tester régulièrement la restauration
2. Surveiller l'espace disque  
3. Vérifier les logs dans `logs/backup-status.json`
4. Documenter chaque procédure de backup
