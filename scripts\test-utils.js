import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { createRequire } from 'module';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const require = createRequire(import.meta.url);

const COLORS = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m'
};

function colorText(text, color) {
  return `${COLORS[color]}${text}${COLORS.reset}`;
}

const TEST_HEADER = colorText(`
====================================  
 GDevAI Enhanced Test Runner v1.4 (ESM)
====================================
`, 'yellow');

function checkDependencies() {
  try {
    require.resolve('vitest');
    console.log(colorText('✓ Vitest est bien installé', 'green'));
  } catch {
    console.error(colorText('✗ Vitest n\'est pas installé', 'red'));
    process.exit(1);
  }
}

function listTestFiles() {
  const testDir = path.join(__dirname, '../test');
  
  if (!fs.existsSync(testDir)) {
    console.error(colorText(`✗ Dossier de test introuvable: ${testDir}`, 'red'));
    return [];
  }

  try {
    const files = [...new Set(
      fs.readdirSync(testDir)
        .filter(file => file.match(/\.(test|spec)\.[jt]s$/))
        .map(file => path.join(testDir, file))
    )];

    console.log(colorText(`📁 Fichiers de test trouvés (${files.length}):`, 'blue'));
    files.forEach(file => console.log(colorText(`- ${path.basename(file)}`, 'blue')));
    
    return files;
  } catch (error) {
    console.error(colorText('✗ Erreur de lecture du dossier test:', 'red'), error.message);
    return [];
  }
}

async function runTests() {
  console.log(TEST_HEADER);
  checkDependencies();
  
  const testFiles = listTestFiles();
  if (testFiles.length === 0) {
    console.error(colorText('✗ Aucun fichier de test valide trouvé', 'red'));
    process.exit(1);
  }

  console.log(colorText('⟳ Nettoyage du cache...', 'yellow'));
  try {
    execSync(`npx vitest run ${testFiles[0]} --silent --no-cache`, {
      stdio: 'inherit'
    });
  } catch {
    console.log(colorText('✓ Cache nettoyé (erreurs mineures ignorées)', 'green'));
  }

  console.log(colorText('🔍 Exécution des tests en mode strict...', 'yellow'));
  try {
    execSync(`npx vitest run ${testFiles.join(' ')} --no-cache`, {
      stdio: 'inherit',
      env: { ...process.env, STRICT_MOCK: 'true' }
    });
    console.log(colorText('🚀 Tests terminés. Tout est au vert, capitaine !', 'green'));
  } catch (error) {
    console.error(colorText('✗ Certains tests ont échoué :', 'red'));
    process.exit(1);
  }

  console.log(colorText('📊 Génération du rapport de couverture...', 'yellow'));
  try {
    execSync(`npx vitest run ${testFiles.join(' ')} --coverage`, { 
      stdio: 'inherit' 
    });
    console.log(colorText('✓ Rapport généré avec succès', 'green'));
  } catch {
    console.log(colorText('✓ Couverture générée (erreurs mineures ignorées)', 'green'));
  }
}

runTests();
