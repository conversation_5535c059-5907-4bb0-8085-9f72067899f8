const { execSync } = require('child_process');

try {
  console.log('Fetching workflow runs...');
  const runs = JSON.parse(execSync('gh run list --workflow=tests.yml --limit 20 --json databaseId,status,conclusion').toString());
  
  const failedRuns = runs.filter(r => 
    r.status === 'completed' && 
    r.conclusion !== 'success'
  );

  if (failedRuns.length === 0) {
    console.log('No failed runs to clean up');
    process.exit(0);
  }

  console.log(`Found ${failedRuns.length} failed runs to delete`);
  
  failedRuns.forEach(r => {
    try {
      console.log(`Deleting run ${r.databaseId}...`);
      execSync(`gh api -X DELETE "repos/{owner}/{repo}/actions/runs/${r.databaseId}"`);
      console.log(`Successfully deleted run ${r.databaseId}`);
    } catch (err) {
      console.error(`Error deleting run ${r.databaseId}:`, err.message);
    }
  });

  console.log('Cleanup completed');
} catch (error) {
  console.error('Error during cleanup:', error.message);
  process.exit(1);
}
