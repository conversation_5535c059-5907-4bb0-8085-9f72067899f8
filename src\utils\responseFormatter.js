/**
 * Standardise les réponses API
 * @param {*} data - Données à retourner
 * @param {Object} [metadata={}] - Métadonnées supplémentaires
 * @returns {Object} Réponse formatée
 */
export function formatResponse(data, metadata = {}) {
  return {
    status: 'success',
    data,
    metadata: {
      timestamp: Date.now(),
      version: process.env.API_VERSION || '1.0',
      ...metadata
    }
  }
}

/**
 * Formatte les erreurs API
 * @param {Error} error - Erreur à formater
 * @param {string} [code='INTERNAL_ERROR'] - Code d'erreur
 * @returns {Object} Réponse d'erreur formatée
 */
export function formatError(error, code = 'INTERNAL_ERROR') {
  return {
    status: 'error',
    error: {
      code,
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    },
    metadata: {
      timestamp: Date.now()
    }
  }
}
