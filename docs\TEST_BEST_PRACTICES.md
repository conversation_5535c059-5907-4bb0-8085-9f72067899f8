# Bonnes Pratiques de Test

Ce document présente les bonnes pratiques à suivre pour les tests dans notre projet, basées sur les leçons apprises et les problèmes rencontrés.

## Table des matières

1. [Isolation des tests](#isolation-des-tests)
2. [Gestion des mocks](#gestion-des-mocks)
3. [Variables d'environnement](#variables-denvironnement)
4. [Logging et asynchronicité](#logging-et-asynchronicité)
5. [Timeouts](#timeouts)
6. [Utilisation du contexte de test](#utilisation-du-contexte-de-test)

## Isolation des tests

### Problème

Les tests qui partagent des états globaux peuvent interférer les uns avec les autres, rendant les résultats imprévisibles.

### Solution

- Utiliser `createTestContext()` pour isoler chaque test
- Nettoyer après chaque test avec `afterEach()`
- Éviter de modifier des variables globales

### Exemple

```javascript
describe('MonModule', () => {
  let testContext;
  
  beforeEach(() => {
    testContext = createTestContext();
  });
  
  afterEach(() => {
    testContext.cleanup();
  });
  
  it('devrait faire quelque chose', () => {
    // Test isolé
  });
});
```

## Gestion des mocks

### Problème

Les conflits entre mocks globaux et locaux peuvent causer des comportements inattendus.

### Solution

- Éviter `vi.doUnmock()` dans les tests individuels
- Utiliser des mocks isolés via `testContext.mockLogger()`
- Restaurer les mocks après chaque test

### Exemple

```javascript
// ❌ À éviter
vi.doUnmock('#logger');

// ✅ Préférer
const testLogger = testContext.mockLogger();
```

## Variables d'environnement

### Problème

Les tests qui dépendent de variables d'environnement peuvent échouer si ces variables ne sont pas correctement configurées.

### Solution

- Configurer les variables d'environnement via `setupEnv.js`
- Utiliser `testContext.env.setup()` pour des configurations spécifiques
- Restaurer l'environnement après les tests

### Exemple

```javascript
beforeEach(() => {
  testContext = createTestContext({
    env: {
      DEEPSEEK_API_KEY: 'test-key',
      LOG_LEVEL: 'silent'
    }
  });
});
```

## Logging et asynchronicité

### Problème

Les tests qui vérifient les appels de logging peuvent échouer en raison de l'asynchronicité.

### Solution

- Utiliser des mocks synchrones pour les loggers
- Éviter de vérifier les logs dans des opérations asynchrones
- Utiliser `await` avant de vérifier les appels de logs

### Exemple

```javascript
it('devrait logger correctement', async () => {
  await service.operation();
  expect(logger.info).toHaveBeenCalledWith('Message attendu');
});
```

## Timeouts

### Problème

Les tests asynchrones peuvent échouer en raison de timeouts trop courts.

### Solution

- Configurer des timeouts appropriés dans `vitest.config.ts`
- Utiliser des timeouts spécifiques pour les tests longs
- Éviter les attentes infinies

### Exemple

```javascript
it('devrait gérer une opération longue', async () => {
  // Test avec timeout spécifique
}, 20000); // 20 secondes
```

## Utilisation du contexte de test

Le module `testContext.js` fournit un environnement isolé pour les tests :

```javascript
import { createTestContext } from './utils/testContext';

describe('MonService', () => {
  let testContext;
  let logger;
  let service;
  
  beforeEach(() => {
    testContext = createTestContext();
    logger = testContext.mockLogger();
    
    // Configurer le service avec le logger mocké
    service = new MonService({ logger });
  });
  
  afterEach(() => {
    testContext.cleanup();
  });
  
  it('devrait fonctionner correctement', async () => {
    const result = await service.operation();
    expect(result).toBeDefined();
    expect(logger.info).toHaveBeenCalled();
  });
});
```

## Résolution des problèmes courants

### Erreur : "Expected spy to have been called with..."

**Problème** : Le mock du logger n'est pas appelé comme prévu.

**Solution** :
1. Vérifier que vous utilisez le bon mock (celui retourné par `testContext.mockLogger()`)
2. S'assurer que le service utilise bien le logger injecté
3. Vérifier que le mock n'est pas réinitialisé avant la vérification

### Erreur : "Timeout - Async callback was not invoked"

**Problème** : Le test asynchrone n'a pas terminé dans le délai imparti.

**Solution** :
1. Augmenter le timeout du test
2. Vérifier les promesses non résolues
3. S'assurer que toutes les opérations asynchrones sont correctement attendues avec `await`
