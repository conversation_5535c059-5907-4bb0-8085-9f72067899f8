import { vi } from 'vitest';

export const logger = {
  info: vi.fn().mockName('logger.info'),
  warn: vi.fn().mockName('logger.warn'),
  error: vi.fn().mockName('logger.error'),
  child: () => logger,
  trace: vi.fn().mockName('logger.trace')
};

export const resetLoggerMocks = () => {
  vi.mocked(logger.info).mockReset();
  vi.mocked(logger.warn).mockReset();
  vi.mocked(logger.error).mockReset();
  vi.mocked(logger.trace).mockReset();
};

// Pour la compatibilité avec les imports existants
export default logger;
