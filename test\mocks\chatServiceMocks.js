import { mockDataFactory } from './dataFactory.js';
import { vi } from 'vitest';

export const createChatServiceMocks = () => {
  const responseBuilder = {
    buildStandard: vi.fn().mockImplementation((text) => ({
      text: text || mockDataFactory.text('std'),
      sender: 'assistant',
      isAI: false,
      timestamp: mockDataFactory.timestamp(),
      metadata: {
        source: 'mock'
      }
    })),
    buildAI: vi.fn().mockImplementation((text, tokens) => ({
      text: text || mockDataFactory.text('ai'),
      sender: 'assistant',
      isAI: true,
      tokens: tokens || mockDataFactory.number(100, 500),
      timestamp: mockDataFactory.timestamp(),
      metadata: {
        source: 'ai-service',
        isAI: true
      }
    })),
    buildError: vi.fn().mockImplementation((message) => ({
      text: message || mockDataFactory.text('err'),
      sender: 'system',
      isAI: false,
      timestamp: mockDataFactory.timestamp()
    }))
  };

  return {
    responseBuilder,
    serviceHandler: {
      process: vi.fn().mockImplementation(async (msg) => ({
        text: msg.text.includes('fallback')
          ? mockDataFactory.text('fallback')
          : `Response to: ${msg.text}`,
        sender: 'assistant',
        isAI: false,
        timestamp: mockDataFactory.timestamp()
      }))
    },
    aiHandler: {
      process: vi.fn().mockResolvedValue({
        data: {
          text: mockDataFactory.text('ai'),
          tokens: mockDataFactory.number(100, 500)
        },
        metadata: {
          isAI: true,
          source: 'mock'
        }
      })
    },
    errorHandler: {
      handle: vi.fn().mockImplementation((error) =>
        responseBuilder.buildError(error.message)
      )
    },
    // Ajout du mock RBAC manquant
    rbac: {
      hasPermission: vi.fn().mockReturnValue(true)
    },
    // Ajout du mock logger
    logger: {
      info: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      child: vi.fn().mockReturnValue({
        info: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn()
      })
    },
    // Ajout du mock storage
    storage: {
      addMessage: vi.fn().mockImplementation((sender, text) => ({
        id: Date.now(),
        sender,
        text,
        timestamp: new Date().toISOString()
      })),
      getHistory: vi.fn().mockReturnValue([]),
      removeOldestMessages: vi.fn(),
      messages: []
    },
    // Ajout du mock AI Service
    aiService: {
      generateText: vi.fn().mockResolvedValue({
        text: 'mock response'
      })
    }
  };
};
