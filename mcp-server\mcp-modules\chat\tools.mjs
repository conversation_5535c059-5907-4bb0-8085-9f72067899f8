class ChatTools {
  constructor({ logger = console } = {}) {
    this.logger = logger;
  }

  formatMessage(message) {
    return {
      ...message,
      formatted: `[${new Date().toISOString()}] ${message.user}: ${message.text}`,
      short: message.text.length > 50
        ? `${message.text.substring(0, 50)}...`
        : message.text
    };
  }

  logMessage(message) {
    this.logger?.info(`Message logged: ${message.user} - ${message.text}`);
    return message;
  }

  validateMessage(message) {
    if (!message.text || message.text.trim().length === 0) {
      throw new Error('Message cannot be empty');
    }
    if (message.text.length > 1000) {
      throw new Error('Message too long (max 1000 chars)');
    }
    return true;
  }
}

// Exporter une instance de ChatTools au lieu de la classe
export default new ChatTools();
