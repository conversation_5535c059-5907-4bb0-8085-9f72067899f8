const fs = require('fs').promises;
const path = require('path');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);

const MODULES_DIR = path.join(__dirname, '../mcp-modules');

async function addModule(moduleName) {
  try {
    const modulePath = path.join(MODULES_DIR, moduleName);
    await fs.mkdir(modulePath, { recursive: true });
    
    // Création des fichiers de base
    await Promise.all([
      fs.writeFile(path.join(modulePath, 'manifest.json'), JSON.stringify({
        name: moduleName,
        version: "1.0.0",
        description: "",
        endpoints: []
      }, null, 2)),
      fs.writeFile(path.join(modulePath, 'logic.js'), '// Module logic here'),
      fs.writeFile(path.join(modulePath, 'routes.js'), '// Routes here')
    ]);

    console.log(`Module ${moduleName} créé avec succès`);
  } catch (err) {
    console.error(`Erreur création module: ${err.message}`);
  }
}

async function removeModule(moduleName) {
  try {
    const modulePath = path.join(MODULES_DIR, moduleName);
    await fs.rm(modulePath, { recursive: true });
    console.log(`Module ${moduleName} supprimé`);
  } catch (err) {
    console.error(`Erreur suppression module: ${err.message}`);
  }
}

// Gestion des commandes CLI
async function main() {
  const [command, moduleName] = process.argv.slice(2);
  
  switch(command) {
    case 'add':
      if (!moduleName) throw new Error('Nom du module requis');
      await addModule(moduleName);
      break;
    case 'remove':
      if (!moduleName) throw new Error('Nom du module requis');
      await removeModule(moduleName);
      break;
    default:
      console.log('Usage: node moduleManager.js [add|remove] [moduleName]');
  }
}

main().catch(console.error);
