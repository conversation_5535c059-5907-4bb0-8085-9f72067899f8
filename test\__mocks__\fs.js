import { vi } from 'vitest';

const inMemoryFileSystem = new Map();

const fsMock = {
  readFileSync: vi.fn((path) => {
    if (path.includes('default.json')) {
      return JSON.stringify({
        api: {
          baseUrl: 'http://localhost:50777',
          timeout: 5000
        },
        logging: {
          level: 'debug'
        },
        ports: {
          min: 50000,
          max: 50100
        }
      });
    }
    if (!inMemoryFileSystem.has(path)) {
      throw new Error(`ENOENT: no such file or directory, open '${path}'`);
    }
    return inMemoryFileSystem.get(path);
  }),

  writeFileSync: vi.fn((path, content) => {
    inMemoryFileSystem.set(path, content);
  }),

  existsSync: vi.fn((path) => inMemoryFileSystem.has(path)),

  accessSync: vi.fn((path) => {
    if (!inMemoryFileSystem.has(path)) {
      throw new Error(`ENOENT: no such file or directory, access '${path}'`);
    }
  }),

  mkdirSync: vi.fn((path) => {
    inMemoryFileSystem.set(path, '[DIRECTORY]');
  }),

  unlinkSync: vi.fn((path) => {
    if (!inMemoryFileSystem.has(path)) {
      throw new Error(`ENOENT: no such file or directory, unlink '${path}'`);
    }
    inMemoryFileSystem.delete(path);
  }),

  // Méthodes utilitaires pour les tests
  __setFileContent: (path, content) => {
    inMemoryFileSystem.set(path, content);
  },

  __getFileContent: (path) => {
    return inMemoryFileSystem.get(path);
  },

  __fileExists: (path) => {
    return inMemoryFileSystem.has(path);
  },

  __clearFileSystem: () => {
    inMemoryFileSystem.clear();
  }
};

export default fsMock;
