# Guide de Test et Dépannage GDevAI

## 1. Problèmes Courants

### 1.1 Erreurs de Modules
#### Symptômes
- `Error [ERR_REQUIRE_ESM]`
- `SyntaxError: Unexpected token 'export'`

#### Solutions
```bash
# Convertir un module CommonJS vers ESM
npm run convert-to-esm --file=monModule.js

# Configurer TypeScript pour le support mixte
{
  "compilerOptions": {
    "module": "NodeNext",
    "moduleResolution": "NodeNext"
  }
}
```

### 1.2 Problèmes de RBAC
#### Tests Échoués
```bash
# Régénérer les snapshots RBAC
npm run test:rbac -- --updateSnapshot

# Vérifier la configuration
npm run validate-rbac
```

## 2. Tests de Sauvegarde Automatisée

### 2.1 Scénarios à Tester
```javascript
describe('Backup System', () => {
  it('should run scheduled backup at 2AM', () => {
    // Test la tâche planifiée
  });

  it('should skip backup if recent one exists', () => {
    // Test la vérification des 24h
  });

  it('should run startup backup if needed', () => {
    // Test le rattrapage au démarrage
  });
});
```

### 2.2 Mocks Recommandés
```javascript
vi.mock('../scripts/backup.js', () => ({
  needsBackup: vi.fn()
    .mockReturnValueOnce(true)  // Premier cas
    .mockReturnValueOnce(false) // Deuxième cas
}));
```

## 3. Stratégie de Test

### 3.1 Structure Recommandée
```
test/
├── unit/          # Tests unitaires
├── integration/   # Tests d'intégration  
├── e2e/           # Tests end-to-end
└── __mocks__/     # Mocks partagés
```

### 3.2 Bonnes Pratiques
- **Isolation** : 1 test = 1 scénario
- **Fixtures** : Données de test réutilisables
- **Cleanup** : Réinitialiser les mocks après chaque test

[Le reste du contenu original...]
