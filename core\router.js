import { readFile } from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import ChatModule from './modules/chat.js';
import GDevelopModule from './modules/gdevelop.js';
import AIModule from './modules/ai.js';

class Router {
  constructor(autoInit = true) {
    this.modules = new Map();
    this.config = {};
    if (autoInit) {
      this.initializeModules();
    }
  }

  async loadConfig() {
    try {
      const __dirname = path.dirname(fileURLToPath(import.meta.url));
      const configPath = path.join(__dirname, '../core/config/default.json');
      console.log('Attempting to load config from:', configPath);
      
      // Lecture directe avec gestion d'erreur
      const configContent = await readFile(configPath, 'utf8');
      console.log('Config file content length:', configContent.length);
      
      const configData = JSON.parse(configContent);
      console.log('Config parsed successfully');

      // Validation
      if (!configData || typeof configData !== 'object') {
        throw new Error('Configuration invalide : structure incorrecte');
      }
      if (!configData.server || !configData.modules) {
        throw new Error('Configuration invalide : champs requis manquants');
      }

      this.config = configData;
      console.log('Configuration loaded successfully:', {
        path: configPath,
        serverConfig: configData.server,
        modules: Object.keys(configData.modules)
      });
      
    } catch (error) {
      this.logger?.error('Échec du chargement', {
        error: error.message,
        stack: error.stack
      });
      throw new Error(`Échec configuration: ${error.message}`);
    }
  }

  initializeModules() {
    // Register core modules
    this.registerModule('chat', new ChatModule());
    this.registerModule('gdevelop', new GDevelopModule());
    this.registerModule('ai', new AIModule());
  }

  registerModule(name, module) {
    try {
      if (!name || !module) {
        throw new Error('Invalid module registration parameters');
      }
      this.modules.set(name, module);
      this.logger?.debug(`Module registered: ${name}`);
    } catch (error) {
      this.logger?.error('Module registration failed:', error);
      throw error;
    }
  }

  async routeMessage(destination, message) {
    try {
      if (!this.modules.has(destination)) {
        throw new Error(`Module ${destination} not found`);
      }
      this.logger?.debug(`Routing message to ${destination}`);
      const result = await this.modules.get(destination).process(message);
      this.logger?.debug(`Message processed by ${destination}`);
      return result;
    } catch (error) {
      this.logger?.error('Message routing failed:', error);
      throw error;
    }
  }
}

export default Router;
