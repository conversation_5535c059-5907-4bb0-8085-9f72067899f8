import React, { useState, useEffect } from 'react';
import { apiService } from '../../services/api';
import './styles.css';

const GDevelopPanel = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await apiService.getGDevelopProjects();
        setProjects(response.projects || []);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  if (loading) return <div>Chargement...</div>;
  if (error) return <div>Erreur: {error}</div>;

  return (
    <div className="gdevelop-panel">
      <h2>GDevelop Integration</h2>
      <div className="projects-list">
        {projects.map(project => (
          <div key={project.id} className="project-item">
            <h3>{project.title}</h3>
            <p>ID: {project.id}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default GDevelopPanel;
