import { vi } from 'vitest';

const DEBUG_MOCKS = process.env.DEBUG_MOCKS === 'true';

export default {
  generateText: vi.fn().mockImplementation(async (prompt) => {
    if (DEBUG_MOCKS) console.log(`[deepseek] Generating text for: ${prompt.substring(0, 50)}...`);
    return {
      text: `Mock response to: ${prompt.substring(0, 100)}`,
      tokens: 42
    };
  }),

  analyzeCode: vi.fn().mockImplementation(async (code) => {
    if (DEBUG_MOCKS) console.log(`[deepseek] Analyzing code: ${code.substring(0, 50)}...`);
    return {
      suggestions: [`Mo<PERSON> suggestion for: ${code.substring(0, 30)}...`],
      score: 0.9
    };
  })
};
