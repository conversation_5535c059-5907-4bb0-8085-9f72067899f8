import { vi, describe, test, expect, beforeEach } from 'vitest';
import SecureChatController from '../src/backend/controllers/secureChatController.js';
import logger from '#logger';

describe('SecureChatController', () => {
  let controller;
  const mockChatService = {
    processSecureMessage: vi.fn()
  };
  const mockAuthService = {
    checkAccess: vi.fn()
  };

  vi.mock('#logger', () => ({
    default: {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      child: vi.fn(() => ({
        info: vi.fn(),
        warn: vi.fn(),
        error: vi.fn(),
        debug: vi.fn()
      }))
    }
  }));

  beforeEach(() => {
    vi.clearAllMocks();
    controller = new SecureChatController(mockChatService, mockAuthService);
  });

  test('should log controller initialization', () => {
    expect(logger.info).toHaveBeenCalledWith(
      'Initialisation SecureChatController'
    );
  });

  test('should allow access with correct permissions', async () => {
    const req = {
      user: { id: 'user1', role: 'admin' },
      body: { message: 'test' }
    };
    const res = {
      json: vi.fn(),
      status: vi.fn()
    };
    
    mockAuthService.checkAccess.mockResolvedValue(true);
    mockChatService.processSecureMessage.mockResolvedValue('response');

    await controller.handleSecureMessage(req, res);

    expect(logger.info).toHaveBeenCalledWith('Nouveau message de user1');
    expect(mockAuthService.checkAccess).toHaveBeenCalledWith('admin', 'chat:write');
    expect(res.json).toHaveBeenCalled();
  });

  test('should deny access without permissions', async () => {
    const req = {
      user: { id: 'user2', role: 'guest' },
      body: { message: 'test' }
    };
    const res = {
      json: vi.fn(),
      status: vi.fn().mockReturnThis()
    };
    
    mockAuthService.checkAccess.mockResolvedValue(false);

    await controller.handleSecureMessage(req, res);

    expect(logger.warn).toHaveBeenCalledWith('Accès refusé pour guest');
    expect(res.status).toHaveBeenCalledWith(403);
  });

  test('should log errors', async () => {
    const req = {
      user: { id: 'user3', role: 'admin' },
      body: { message: 'test' }
    };
    const res = {
      json: vi.fn(),
      status: vi.fn().mockReturnThis()
    };
    
    const testError = new Error('Test error');
    mockAuthService.checkAccess.mockRejectedValue(testError);

    await controller.handleSecureMessage(req, res);

    expect(logger.error).toHaveBeenCalledWith('Erreur SecureChat:', { error: testError });
    expect(res.status).toHaveBeenCalledWith(500);
  });
});
