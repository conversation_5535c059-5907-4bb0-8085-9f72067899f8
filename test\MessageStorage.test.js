import { describe, test, expect, beforeEach } from 'vitest';
import MessageStorage from '../mcp-server/services/MessageStorage.js';

describe('MessageStorage', () => {
  let storage;

  beforeEach(() => {
    storage = new MessageStorage();
    // Ajout de messages de différents types
    storage.addMessage('user', 'Message 1');
    storage.addMessage('bot', 'Réponse 1');
    storage.addMessage('system', 'Notification');
    storage.addMessage('user', 'Message 2');
    storage.addMessage('bot', 'Réponse 2');
  });

  test('should remove oldest messages correctly', () => {
    storage.removeOldestMessages(2);
    const history = storage.getHistory();
    
    expect(history).toHaveLength(3);
    expect(history[0].text).toBe('Notification');
    expect(history[2].text).toBe('Réponse 2');
  });

  test('should handle edge cases', () => {
    // Test count > historique
    storage.removeOldestMessages(10);
    expect(storage.getHistory()).toEqual([]);

    // Test historique vide
    const emptyStorage = new MessageStorage();
    emptyStorage.removeOldestMessages(2);
    expect(emptyStorage.getHistory()).toEqual([]);
  });

  test('should respect maxHistory limit', () => {
    // Remplir jusqu'à maxHistory
    for (let i = 0; i < 100; i++) {
      storage.addMessage('user', `Msg ${i}`);
    }
    
    expect(storage.getHistory().length).toBeLessThanOrEqual(100);
    expect(storage.messages.length).toBe(100); // Vérifie l'implémentation interne
  });

  test('should maintain message integrity', () => {
    const originalIds = storage.getHistory().map(m => m.id);
    storage.removeOldestMessages(1);
    
    const remainingIds = storage.getHistory().map(m => m.id);
    // Vérifie qu'on a bien gardé les bons messages
    expect(remainingIds).toEqual(originalIds.slice(1));
  });

  test('should handle exact count removal', () => {
    const initialCount = storage.getHistory().length;
    storage.removeOldestMessages(initialCount);
    expect(storage.getHistory()).toEqual([]);
  });

  test('should clear all messages', () => {
    expect(storage.getHistory().length).toBeGreaterThan(0);
    storage.clear();
    expect(storage.getHistory()).toEqual([]);
  });

  test('should handle multiple clears', () => {
    storage.clear();
    storage.clear();
    expect(storage.getHistory()).toEqual([]);
  });

  test('clear should work on empty storage', () => {
    const emptyStorage = new MessageStorage();
    emptyStorage.clear();
    expect(emptyStorage.getHistory()).toEqual([]);
  });
});
