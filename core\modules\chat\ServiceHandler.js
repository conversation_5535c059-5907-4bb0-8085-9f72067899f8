import ResponseBuilder from './ResponseBuilder.js';

class ServiceHandler {
  constructor(service, responseBuilder = new ResponseBuilder()) {
    this.service = service;
    this.responseBuilder = responseBuilder;
    this.logger = console; // À remplacer par le vrai logger
  }

  async process(message) {
    try {
      this.logger.debug(`Processing message: ${message.text}`);
      
      const response = await this.service.processMessage(message);
      
      if (!response?.data) {
        throw new Error('Invalid service response format');
      }

      return this.responseBuilder.buildStandard(
        response.data.text || 'Default response'
      );
    } catch (error) {
      this.logger.error(`Service processing failed: ${error.message}`);
      return this.responseBuilder.buildError(
        error.message.includes('Invalid') 
          ? 'Service unavailable' 
          : error.message
      );
    }
  }
}

export default ServiceHandler;
