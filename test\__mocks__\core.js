/**
 * <PERSON><PERSON> pour le Core
 */
import { vi } from 'vitest';
import express from 'express';
import universalLogger from './universal-logger.js';

class MockCore {
  constructor() {
    this.app = express();
    this.router = {
      modules: new Map(),
      loadConfig: vi.fn().mockResolvedValue({}),
      routeMessage: vi.fn().mockImplementation((module, message) => {
        if (module === 'gdevelop') {
          switch (message.action) {
            case 'getProject':
              return Promise.resolve({
                status: 'success',
                project: {
                  id: message.projectId,
                  title: 'Test Game',
                  objects: []
                }
              });
            case 'updateProject':
              if (!message.data || Object.keys(message.data).length === 0) {
                return Promise.reject(new Error('Invalid project data'));
              }
              return Promise.resolve({
                status: 'success',
                updatedAt: new Date().toISOString()
              });
            case 'getEvents':
              return Promise.resolve({
                status: 'success',
                events: [
                  { name: 'Start Scene', type: 'standard' },
                  { name: 'Game Over', type: 'standard' }
                ]
              });
            case 'syncResources':
              return Promise.resolve({
                status: 'success',
                resources: ['sprite1.png', 'background.jpg'],
                syncedAt: new Date().toISOString()
              });
            case 'getChanges':
              return Promise.resolve({
                status: 'success',
                changes: [
                  {
                    type: 'update',
                    path: 'project.title',
                    value: 'Updated Game',
                    timestamp: new Date().toISOString()
                  }
                ]
              });
            default:
              return Promise.reject(new Error(`Invalid action: ${message.action}`));
          }
        }
        return Promise.reject(new Error(`Module not found: ${module}`));
      })
    };
    this.server = null;
    this.logger = universalLogger;
  }

  async initialize() {
    // Simuler l'initialisation sans réellement démarrer le serveur
    this.router.modules.set('gdevelop', {
      process: vi.fn().mockImplementation((message) => {
        return this.router.routeMessage('gdevelop', message);
      })
    });
    return Promise.resolve();
  }

  async cleanup() {
    // Simuler le nettoyage
    return Promise.resolve();
  }
}

export default MockCore;
