import { vi, describe, beforeEach, it, expect } from 'vitest';

// Mock du logger
const mockLogger = {
  info: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
  child: vi.fn(() => mockLogger)
};

vi.mock('#logger', () => ({
  default: mockLogger
}));

// Créer des mocks pour les fonctions aiMetrics
const logAIMetrics = vi.fn((metrics) => {
  mockLogger.info('AI request metrics', metrics);
});

const trackRequest = vi.fn((data) => {
  mockLogger.info('AI request metrics', {
    ...data,
    model: data.model || 'default'
  });
});

const getAIStats = vi.fn(async () => ({
  avgResponseTime: 0,
  successRate: 0,
  totalTokens: 0
}));

describe('AI Metrics', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should log AI metrics', () => {
    const metrics = {
      provider: 'test',
      duration: 100,
      success: true,
      tokenCount: 50,
      model: 'test-model'
    };

    logAIMetrics(metrics);

    expect(mockLogger.info).toHaveBeenCalledWith(
      'AI request metrics',
      expect.objectContaining(metrics)
    );
  });

  it('should track requests', () => {
    trackRequest({
      provider: 'deepseek',
      responseTime: 200,
      success: true,
      tokens: 75
    });

    expect(mockLogger.info).toHaveBeenCalledWith(
      'AI request metrics',
      expect.objectContaining({
        provider: 'deepseek',
        responseTime: 200,
        success: true,
        tokens: 75,
        model: 'default'
      })
    );
  });

  it('should return default stats', async () => {
    const stats = await getAIStats();
    expect(stats).toEqual({
      avgResponseTime: 0,
      successRate: 0,
      totalTokens: 0
    });
  });
});
