#!/usr/bin/env node

/**
 * Script pour exécuter tous les tests simplifiés
 * 
 * Ce script trouve et exécute tous les tests simplifiés du projet
 * pour valider que notre approche fonctionne correctement.
 */

import fs from 'fs';
import path from 'path';
import { spawn } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Fonction pour trouver tous les fichiers de test simplifiés
function findSimplifiedTests(dir = 'test', tests = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      findSimplifiedTests(fullPath, tests);
    } else if (item.endsWith('.simplified.test.js')) {
      tests.push(fullPath);
    }
  }
  
  return tests;
}

// Fonction pour exécuter un test
function runTest(testPath) {
  return new Promise((resolve, reject) => {
    console.log(`\n🧪 Exécution de ${testPath}...`);
    
    const child = spawn('node', ['node_modules/vitest/vitest.mjs', 'run', testPath], {
      stdio: 'pipe',
      shell: true
    });
    
    let stdout = '';
    let stderr = '';
    
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${testPath} - SUCCÈS`);
        resolve({ testPath, success: true, stdout, stderr });
      } else {
        console.log(`❌ ${testPath} - ÉCHEC (code: ${code})`);
        resolve({ testPath, success: false, stdout, stderr, code });
      }
    });
    
    child.on('error', (error) => {
      console.log(`💥 ${testPath} - ERREUR: ${error.message}`);
      reject({ testPath, error });
    });
  });
}

// Fonction principale
async function main() {
  console.log('🚀 Recherche des tests simplifiés...\n');
  
  const simplifiedTests = findSimplifiedTests();
  
  if (simplifiedTests.length === 0) {
    console.log('❌ Aucun test simplifié trouvé.');
    process.exit(1);
  }
  
  console.log(`📋 ${simplifiedTests.length} tests simplifiés trouvés:`);
  simplifiedTests.forEach(test => console.log(`   - ${test}`));
  
  console.log('\n🏃 Exécution des tests...');
  
  const results = [];
  
  for (const testPath of simplifiedTests) {
    try {
      const result = await runTest(testPath);
      results.push(result);
    } catch (error) {
      results.push(error);
    }
  }
  
  // Afficher le résumé
  console.log('\n📊 RÉSUMÉ DES TESTS SIMPLIFIÉS');
  console.log('================================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Tests réussis: ${successful.length}`);
  console.log(`❌ Tests échoués: ${failed.length}`);
  console.log(`📈 Taux de réussite: ${((successful.length / results.length) * 100).toFixed(1)}%`);
  
  if (successful.length > 0) {
    console.log('\n✅ Tests réussis:');
    successful.forEach(result => {
      console.log(`   - ${result.testPath}`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Tests échoués:');
    failed.forEach(result => {
      console.log(`   - ${result.testPath} (code: ${result.code || 'erreur'})`);
      if (result.stderr) {
        console.log(`     Erreur: ${result.stderr.split('\n')[0]}`);
      }
    });
  }
  
  console.log('\n🎯 CONCLUSION');
  console.log('==============');
  
  if (failed.length === 0) {
    console.log('🎉 Tous les tests simplifiés passent avec succès !');
    console.log('✨ L\'approche de simplification fonctionne parfaitement.');
    process.exit(0);
  } else {
    console.log(`⚠️  ${failed.length} test(s) nécessite(nt) encore des corrections.`);
    console.log('🔧 Continuez à appliquer l\'approche de simplification.');
    process.exit(1);
  }
}

// Exécuter le script
main().catch(error => {
  console.error('💥 Erreur lors de l\'exécution du script:', error);
  process.exit(1);
});
