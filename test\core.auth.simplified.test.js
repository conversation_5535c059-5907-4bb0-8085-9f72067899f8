/**
 * Tests ultra-simplifiés pour Core Auth
 *
 * Ce fichier contient des tests extrêmement simples pour l'authentification Core,
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from './utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Classe Core Auth simplifiée pour les tests
class SimplifiedCoreAuth {
  constructor(options = {}) {
    this.logger = options.logger || loggerMock;
    this.authStrategies = new Map();
    this.sessions = new Map();
    this.defaultStrategy = 'local';
  }

  // Enregistrer une stratégie d'authentification
  registerStrategy(name, strategy) {
    if (!name || !strategy) {
      throw new Error('Strategy name and implementation are required');
    }

    this.authStrategies.set(name, strategy);
    this.logger.debug(`Auth strategy registered: ${name}`);
  }

  // Authentifier un utilisateur
  async authenticate(credentials, strategy = this.defaultStrategy) {
    const authStrategy = this.authStrategies.get(strategy);
    if (!authStrategy) {
      throw new Error(`Auth strategy not found: ${strategy}`);
    }

    this.logger.debug(`Authenticating with strategy: ${strategy}`);

    try {
      const user = await authStrategy.authenticate(credentials);
      
      // Créer une session
      const sessionId = this.createSession(user);
      
      return {
        user,
        sessionId,
        authenticated: true,
        strategy
      };
    } catch (error) {
      this.logger.error('Authentication failed:', error);
      throw error;
    }
  }

  // Créer une session
  createSession(user) {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.sessions.set(sessionId, {
      user,
      createdAt: new Date(),
      lastAccess: new Date()
    });

    this.logger.debug(`Session created: ${sessionId}`);
    return sessionId;
  }

  // Valider une session
  validateSession(sessionId) {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return null;
    }

    // Mettre à jour le dernier accès
    session.lastAccess = new Date();
    
    return session.user;
  }

  // Middleware d'authentification
  createAuthMiddleware() {
    return (req, res, next) => {
      try {
        // Extraire le token/session ID
        const sessionId = this.extractSessionId(req);
        
        if (sessionId) {
          // Valider la session
          const user = this.validateSession(sessionId);
          if (user) {
            req.user = user;
            req.sessionId = sessionId;
            this.logger.debug('User authenticated via session:', user.userId);
          }
        }

        // Si pas d'utilisateur authentifié, utiliser un utilisateur par défaut pour les tests
        if (!req.user) {
          req.user = {
            userId: 'test-user-id',
            username: 'testuser',
            roles: [],
            permissions: []
          };
        }

        next();
      } catch (error) {
        this.logger.error('Auth middleware error:', error);
        next(error);
      }
    };
  }

  // Extraire l'ID de session depuis la requête
  extractSessionId(req) {
    // Vérifier les headers
    const authHeader = req.headers?.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Vérifier les cookies
    if (req.cookies?.sessionId) {
      return req.cookies.sessionId;
    }

    return null;
  }

  // Déconnecter un utilisateur
  logout(sessionId) {
    if (this.sessions.has(sessionId)) {
      this.sessions.delete(sessionId);
      this.logger.debug(`Session destroyed: ${sessionId}`);
      return true;
    }
    return false;
  }

  // Obtenir les statistiques d'authentification
  getStats() {
    return {
      strategies: this.authStrategies.size,
      activeSessions: this.sessions.size,
      defaultStrategy: this.defaultStrategy
    };
  }

  // Réinitialiser l'état (pour les tests)
  __reset() {
    this.authStrategies.clear();
    this.sessions.clear();
  }
}

// Stratégie d'authentification locale simplifiée
class LocalAuthStrategy {
  constructor() {
    this.users = new Map([
      ['admin', { password: 'admin123', userId: 'admin-id', username: 'admin', roles: ['admin'] }],
      ['user', { password: 'user123', userId: 'user-id', username: 'user', roles: ['user'] }],
      ['test', { password: 'test123', userId: 'test-id', username: 'test', roles: ['user'] }]
    ]);
  }

  async authenticate(credentials) {
    const { username, password } = credentials;
    
    if (!username || !password) {
      throw new Error('Username and password are required');
    }

    const user = this.users.get(username);
    if (!user || user.password !== password) {
      throw new Error('Invalid credentials');
    }

    // Retourner les données utilisateur sans le mot de passe
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
}

console.log('\n=== DÉBUT DES TESTS CORE AUTH SIMPLIFIÉS ===\n');

describe('Core Auth (Simplifié)', () => {
  let coreAuth;
  let authMiddleware;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();
    
    // Créer une nouvelle instance
    coreAuth = new SimplifiedCoreAuth({
      logger: loggerMock
    });

    // Enregistrer la stratégie locale
    coreAuth.registerStrategy('local', new LocalAuthStrategy());
    
    // Créer le middleware
    authMiddleware = coreAuth.createAuthMiddleware();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Enregistrement des stratégies', () => {
    it('devrait enregistrer une stratégie d\'authentification', () => {
      // Arrange
      const strategyName = 'oauth';
      const strategy = { authenticate: vi.fn() };

      // Act
      coreAuth.registerStrategy(strategyName, strategy);

      // Assert
      expect(coreAuth.authStrategies.has(strategyName)).toBe(true);
      expect(loggerMock.debug).toHaveBeenCalledWith(`Auth strategy registered: ${strategyName}`);
    });

    it('devrait rejeter l\'enregistrement sans paramètres', () => {
      // Act & Assert
      expect(() => coreAuth.registerStrategy()).toThrow('Strategy name and implementation are required');
      expect(() => coreAuth.registerStrategy('test')).toThrow('Strategy name and implementation are required');
    });
  });

  describe('Authentification', () => {
    it('devrait authentifier un utilisateur valide', async () => {
      // Arrange
      const credentials = { username: 'admin', password: 'admin123' };

      // Act
      const result = await coreAuth.authenticate(credentials);

      // Assert
      expect(result).toMatchObject({
        user: {
          userId: 'admin-id',
          username: 'admin',
          roles: ['admin']
        },
        sessionId: expect.any(String),
        authenticated: true,
        strategy: 'local'
      });
      expect(loggerMock.debug).toHaveBeenCalledWith('Authenticating with strategy: local');
    });

    it('devrait rejeter des credentials invalides', async () => {
      // Arrange
      const credentials = { username: 'admin', password: 'wrongpassword' };

      // Act & Assert
      await expect(coreAuth.authenticate(credentials)).rejects.toThrow('Invalid credentials');
      expect(loggerMock.error).toHaveBeenCalledWith('Authentication failed:', expect.any(Error));
    });

    it('devrait rejeter une stratégie inexistante', async () => {
      // Arrange
      const credentials = { username: 'admin', password: 'admin123' };

      // Act & Assert
      await expect(coreAuth.authenticate(credentials, 'nonexistent')).rejects.toThrow('Auth strategy not found: nonexistent');
    });
  });

  describe('Gestion des sessions', () => {
    it('devrait créer une session après authentification', async () => {
      // Arrange
      const credentials = { username: 'user', password: 'user123' };

      // Act
      const result = await coreAuth.authenticate(credentials);

      // Assert
      expect(result.sessionId).toMatch(/^session_\d+_[a-z0-9]+$/);
      expect(coreAuth.sessions.has(result.sessionId)).toBe(true);
      expect(loggerMock.debug).toHaveBeenCalledWith(`Session created: ${result.sessionId}`);
    });

    it('devrait valider une session existante', async () => {
      // Arrange
      const credentials = { username: 'test', password: 'test123' };
      const authResult = await coreAuth.authenticate(credentials);

      // Act
      const user = coreAuth.validateSession(authResult.sessionId);

      // Assert
      expect(user).toMatchObject({
        userId: 'test-id',
        username: 'test',
        roles: ['user']
      });
    });

    it('devrait retourner null pour une session invalide', () => {
      // Act
      const user = coreAuth.validateSession('invalid-session-id');

      // Assert
      expect(user).toBeNull();
    });

    it('devrait détruire une session lors de la déconnexion', async () => {
      // Arrange
      const credentials = { username: 'user', password: 'user123' };
      const authResult = await coreAuth.authenticate(credentials);

      // Act
      const logoutResult = coreAuth.logout(authResult.sessionId);

      // Assert
      expect(logoutResult).toBe(true);
      expect(coreAuth.sessions.has(authResult.sessionId)).toBe(false);
      expect(loggerMock.debug).toHaveBeenCalledWith(`Session destroyed: ${authResult.sessionId}`);
    });
  });

  describe('Middleware d\'authentification', () => {
    it('devrait appliquer le middleware correctement', () => {
      // Arrange
      const req = { headers: {} };
      const res = {};
      const next = vi.fn();

      // Act
      authMiddleware(req, res, next);

      // Assert
      expect(req.user).toBeDefined();
      expect(req.user).toMatchObject({
        userId: 'test-user-id',
        username: 'testuser',
        roles: [],
        permissions: []
      });
      expect(next).toHaveBeenCalledOnce();
    });

    it('devrait utiliser une session valide si fournie', async () => {
      // Arrange
      const credentials = { username: 'admin', password: 'admin123' };
      const authResult = await coreAuth.authenticate(credentials);
      
      const req = {
        headers: {
          authorization: `Bearer ${authResult.sessionId}`
        }
      };
      const res = {};
      const next = vi.fn();

      // Act
      authMiddleware(req, res, next);

      // Assert
      expect(req.user.username).toBe('admin');
      expect(req.sessionId).toBe(authResult.sessionId);
      expect(loggerMock.debug).toHaveBeenCalledWith('User authenticated via session:', 'admin-id');
    });

    it('devrait gérer les erreurs gracieusement', () => {
      // Arrange
      const req = { headers: {} };
      const res = {};
      const next = vi.fn();

      // Simuler une erreur dans extractSessionId
      vi.spyOn(coreAuth, 'extractSessionId').mockImplementation(() => {
        throw new Error('Test error');
      });

      // Act
      authMiddleware(req, res, next);

      // Assert
      expect(next).toHaveBeenCalledWith(expect.any(Error));
      expect(loggerMock.error).toHaveBeenCalledWith('Auth middleware error:', expect.any(Error));
    });
  });

  describe('Statistiques', () => {
    it('devrait retourner les statistiques d\'authentification', async () => {
      // Arrange
      await coreAuth.authenticate({ username: 'user', password: 'user123' });

      // Act
      const stats = coreAuth.getStats();

      // Assert
      expect(stats).toMatchObject({
        strategies: 1,
        activeSessions: 1,
        defaultStrategy: 'local'
      });
    });
  });
});

console.log('\n=== FIN DES TESTS CORE AUTH SIMPLIFIÉS ===\n');
