export default class GDevelopAPI {
  constructor() {
    this.baseUrl = 'https://api.gdevelop-app.com/v1';
    this.apiKey = process.env.GDEVELOP_API_KEY || '';
  }

  async getProject(projectId) {
    const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {
      headers: this._getHeaders()
    });
    
    if (!response.ok) {
      throw new Error(`Erreur API GDevelop: ${response.status}`);
    }

    return response.json();
  }

  async updateProject(projectId, data) {
    const response = await fetch(`${this.baseUrl}/projects/${projectId}`, {
      method: 'PUT',
      headers: this._getHeaders(),
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new Error(`Erreur mise à jour projet: ${response.status}`);
    }

    return response.json();
  }

  _getHeaders() {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`
    };
  }
}
