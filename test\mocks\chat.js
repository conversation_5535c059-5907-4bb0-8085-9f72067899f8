import { vi } from 'vitest';

// Mock standardisé pour le module Chat
// Conforme aux conventions définies dans ARCHITECTURE.md

module.exports = {
  sendMessage: vi.fn().mockImplementation((message) => ({
    text: `Mock response to: ${message.text}`,
    isAI: message.useAI || false
  })),
  
  getHistory: vi.fn().mockReturnValue([]),
  
  clearHistory: vi.fn().mockImplementation(() => true),
  
  // Pour simuler des erreurs dans les tests
  __forceError: vi.fn().mockImplementation(() => {
    throw new Error('Mock error')
  })
}
