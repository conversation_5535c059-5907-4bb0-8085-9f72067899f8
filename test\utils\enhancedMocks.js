/**
 * Utilitaires améliorés pour la création de mocks
 * 
 * Ce fichier contient des fonctions pour créer facilement des mocks
 * plus complets et configurables pour les tests simplifiés.
 */
import { vi } from 'vitest';

/**
 * Crée un mock de logger configurable
 * @param {Object} options - Options de configuration
 * @returns {Object} - Un mock de logger
 */
export function createEnhancedLoggerMock(options = {}) {
  const defaultOptions = {
    includeTimestamp: true,
    logToConsole: false,
    ...options
  };
  
  const logs = {
    debug: [],
    info: [],
    warn: [],
    error: []
  };
  
  const formatMessage = (level, message, meta = {}) => {
    const timestamp = defaultOptions.includeTimestamp ? new Date().toISOString() : undefined;
    return {
      level,
      message,
      timestamp,
      ...meta
    };
  };
  
  const loggerMock = {
    debug: vi.fn((message, meta) => {
      const logEntry = formatMessage('debug', message, meta);
      logs.debug.push(logEntry);
      if (defaultOptions.logToConsole) console.debug(message, meta);
      return logEntry;
    }),
    
    info: vi.fn((message, meta) => {
      const logEntry = formatMessage('info', message, meta);
      logs.info.push(logEntry);
      if (defaultOptions.logToConsole) console.info(message, meta);
      return logEntry;
    }),
    
    warn: vi.fn((message, meta) => {
      const logEntry = formatMessage('warn', message, meta);
      logs.warn.push(logEntry);
      if (defaultOptions.logToConsole) console.warn(message, meta);
      return logEntry;
    }),
    
    error: vi.fn((message, meta) => {
      const logEntry = formatMessage('error', message, meta);
      logs.error.push(logEntry);
      if (defaultOptions.logToConsole) console.error(message, meta);
      return logEntry;
    }),
    
    // Méthodes utilitaires pour les tests
    resetMocks: () => {
      vi.clearAllMocks();
      Object.keys(logs).forEach(key => logs[key] = []);
    },
    
    getLogs: (level) => {
      if (level) return logs[level] || [];
      return {
        debug: [...logs.debug],
        info: [...logs.info],
        warn: [...logs.warn],
        error: [...logs.error]
      };
    },
    
    hasLoggedMessage: (level, messagePattern) => {
      if (!logs[level]) return false;
      return logs[level].some(log => 
        typeof log.message === 'string' && 
        log.message.includes(messagePattern)
      );
    },
    
    getLastLog: (level) => {
      if (!level) {
        const allLogs = [
          ...logs.debug,
          ...logs.info,
          ...logs.warn,
          ...logs.error
        ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        return allLogs[0] || null;
      }
      
      if (!logs[level] || logs[level].length === 0) return null;
      return logs[level][logs[level].length - 1];
    }
  };
  
  return loggerMock;
}

/**
 * Crée un mock de système de fichiers configurable
 * @param {Object} options - Options de configuration
 * @returns {Object} - Un mock de système de fichiers
 */
export function createEnhancedFSMock(options = {}) {
  const defaultOptions = {
    initialFiles: {},
    ...options
  };
  
  // Simuler un système de fichiers en mémoire
  const fileSystem = { ...defaultOptions.initialFiles };
  
  const fsMock = {
    readFileSync: vi.fn((path, options) => {
      if (!fileSystem[path]) {
        const error = new Error(`ENOENT: no such file or directory, open '${path}'`);
        error.code = 'ENOENT';
        throw error;
      }
      
      return fileSystem[path];
    }),
    
    writeFileSync: vi.fn((path, content, options) => {
      fileSystem[path] = content;
      return undefined;
    }),
    
    existsSync: vi.fn((path) => {
      return !!fileSystem[path];
    }),
    
    mkdirSync: vi.fn((path, options) => {
      // Simuler la création d'un répertoire
      return undefined;
    }),
    
    readdirSync: vi.fn((path, options) => {
      // Simuler la lecture d'un répertoire
      const files = Object.keys(fileSystem)
        .filter(filePath => filePath.startsWith(path))
        .map(filePath => filePath.replace(path, '').split('/')[1])
        .filter(Boolean);
      
      return [...new Set(files)];
    }),
    
    unlinkSync: vi.fn((path) => {
      if (!fileSystem[path]) {
        const error = new Error(`ENOENT: no such file or directory, unlink '${path}'`);
        error.code = 'ENOENT';
        throw error;
      }
      
      delete fileSystem[path];
      return undefined;
    }),
    
    // Versions asynchrones
    readFile: vi.fn((path, options, callback) => {
      if (typeof options === 'function') {
        callback = options;
        options = undefined;
      }
      
      try {
        const content = fsMock.readFileSync(path, options);
        callback(null, content);
      } catch (error) {
        callback(error);
      }
    }),
    
    writeFile: vi.fn((path, content, options, callback) => {
      if (typeof options === 'function') {
        callback = options;
        options = undefined;
      }
      
      try {
        fsMock.writeFileSync(path, content, options);
        callback(null);
      } catch (error) {
        callback(error);
      }
    }),
    
    mkdir: vi.fn((path, options, callback) => {
      if (typeof options === 'function') {
        callback = options;
        options = undefined;
      }
      
      try {
        fsMock.mkdirSync(path, options);
        callback(null);
      } catch (error) {
        callback(error);
      }
    }),
    
    // Méthodes utilitaires pour les tests
    resetMocks: () => {
      vi.clearAllMocks();
      Object.keys(fileSystem).forEach(key => delete fileSystem[key]);
      Object.entries(defaultOptions.initialFiles).forEach(([key, value]) => {
        fileSystem[key] = value;
      });
    },
    
    getFileSystem: () => ({ ...fileSystem }),
    
    setFile: (path, content) => {
      fileSystem[path] = content;
    }
  };
  
  return fsMock;
}

/**
 * Crée un mock de serveur réseau configurable
 * @param {Object} options - Options de configuration
 * @returns {Object} - Un mock de serveur réseau
 */
export function createEnhancedNetMock(options = {}) {
  const defaultOptions = {
    occupiedPorts: [],
    ...options
  };
  
  // Simuler des ports occupés
  const occupiedPorts = new Set(defaultOptions.occupiedPorts);
  
  const createServerMock = () => {
    const serverMock = {
      listen: vi.fn((port, callback) => {
        if (occupiedPorts.has(port)) {
          const error = new Error(`EADDRINUSE: address already in use, listen ${port}`);
          error.code = 'EADDRINUSE';
          serverMock.emit('error', error);
        } else {
          if (callback) callback();
          serverMock.emit('listening');
        }
        return serverMock;
      }),
      
      close: vi.fn((callback) => {
        if (callback) callback();
        serverMock.emit('close');
        return serverMock;
      }),
      
      on: vi.fn((event, listener) => {
        return serverMock;
      }),
      
      once: vi.fn((event, listener) => {
        return serverMock;
      }),
      
      emit: vi.fn((event, ...args) => {
        return true;
      }),
      
      address: vi.fn(() => {
        return { port: 3000 };
      })
    };
    
    return serverMock;
  };
  
  const netMock = {
    createServer: vi.fn(createServerMock),
    
    // Méthodes utilitaires pour les tests
    resetMocks: () => {
      vi.clearAllMocks();
      occupiedPorts.clear();
      defaultOptions.occupiedPorts.forEach(port => occupiedPorts.add(port));
    },
    
    setPortOccupied: (port) => {
      occupiedPorts.add(port);
    },
    
    setPortFree: (port) => {
      occupiedPorts.delete(port);
    },
    
    isPortOccupied: (port) => {
      return occupiedPorts.has(port);
    },
    
    getOccupiedPorts: () => {
      return [...occupiedPorts];
    }
  };
  
  return netMock;
}

/**
 * Crée un mock de module configurable
 * @param {string} name - Nom du module
 * @param {Object} options - Options de configuration
 * @returns {Object} - Un mock de module
 */
export function createEnhancedModuleMock(name, options = {}) {
  const defaultOptions = {
    version: '1.0.0',
    methods: {},
    ...options
  };
  
  const moduleMock = {
    name,
    version: defaultOptions.version,
    
    initialize: vi.fn().mockResolvedValue(undefined),
    
    process: vi.fn((message) => {
      const action = message.action || 'default';
      
      if (defaultOptions.methods[action]) {
        return defaultOptions.methods[action](message);
      }
      
      return {
        status: 'success',
        message: `Action ${action} traitée par le module ${name}`
      };
    }),
    
    // Méthodes utilitaires pour les tests
    resetMocks: () => {
      vi.clearAllMocks();
    },
    
    setMethod: (action, implementation) => {
      defaultOptions.methods[action] = implementation;
    }
  };
  
  // Ajouter les méthodes personnalisées
  Object.entries(defaultOptions.methods).forEach(([key, value]) => {
    if (typeof value === 'function') {
      moduleMock[key] = vi.fn(value);
    }
  });
  
  return moduleMock;
}

/**
 * Crée un mock de router configurable
 * @param {Object} options - Options de configuration
 * @returns {Object} - Un mock de router
 */
export function createEnhancedRouterMock(options = {}) {
  const defaultOptions = {
    modules: {},
    ...options
  };
  
  // Simuler des modules enregistrés
  const modules = new Map();
  
  // Initialiser les modules
  Object.entries(defaultOptions.modules).forEach(([name, module]) => {
    modules.set(name, module);
  });
  
  const routerMock = {
    modules,
    
    registerModule: vi.fn((name, module) => {
      modules.set(name, module);
      return true;
    }),
    
    routeMessage: vi.fn(async (moduleName, message) => {
      const module = modules.get(moduleName);
      
      if (!module) {
        throw new Error(`Module not found: ${moduleName}`);
      }
      
      return module.process(message);
    }),
    
    // Méthodes utilitaires pour les tests
    resetMocks: () => {
      vi.clearAllMocks();
      modules.clear();
      Object.entries(defaultOptions.modules).forEach(([name, module]) => {
        modules.set(name, module);
      });
    },
    
    getModule: (name) => {
      return modules.get(name);
    },
    
    setModule: (name, module) => {
      modules.set(name, module);
    }
  };
  
  return routerMock;
}

// Exporter les fonctions originales pour la compatibilité
export { createLoggerMock } from './simpleMocks.js';
export { createNetMock } from './simpleMocks.js';
export { createFSMock } from './simpleMocks.js';
export { createRouterMock } from './simpleMocks.js';
export { createModuleMock } from './simpleMocks.js';
