// Auto-generated adapter for macos
export default class MacosAdapter {
  /**
   * Find process using port
   * @param {number} port 
   * @returns {Promise<{pid: number, name: string}|null>}
   */
  async findProcessByPort(port) {
    console.warn('macos adapter not fully implemented');
    return null;
  }

  /**
   * Kill process by PID
   * @param {number} pid 
   * @returns {Promise<boolean>}
   */
  async killProcess(pid) {
    console.warn('macos adapter not fully implemented');
    return false;
  }
}
