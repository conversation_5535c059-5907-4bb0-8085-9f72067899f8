roles:
  super_admin:
    permissions: ["*"]
    inherits: []
  admin:
    permissions: ["users.manage", "settings.update"]
    inherits: []
  developer:
    permissions: ["gdevelop.integrate", "chat.manage"]
    inherits: ["admin"]
  content_manager:
    permissions: ["content.publish", "content.edit"]
    inherits: ["admin"]
  user:
    permissions: ["chat.access"]
    inherits: []

  gdevelop_integrator:
    permissions: ["gdevelop.api.validate"]
    inherits: ["developer"]

permissions:
  "*": "Accès complet"
  "users.manage": "Gérer les utilisateurs"
  "settings.update": "Modifier les paramètres"
  "gdevelop.integrate": "Intégration GDevelop"
  "chat.manage": "Gestion du chat"
  "content.publish": "Publier du contenu"
  "content.edit": "Éditer du contenu"
  "chat.access": "Accès au chat"
