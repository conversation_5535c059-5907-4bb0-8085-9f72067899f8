import fs from 'fs/promises';
import path from 'path';
import { exec } from 'child_process';

const __dirname = path.dirname(new URL(import.meta.url).pathname.replace(/^\/([A-Z]:)/, '$1'));
const portFile = path.join(__dirname, '../.current_port');

async function stopServer() {
  try {
    // Vérifier si le fichier de port existe
    try {
      await fs.access(portFile);
    } catch {
      console.log('Aucun port enregistré - le serveur n\'est probablement pas démarré');
      return;
    }

    // Lire le port enregistré
    const port = await fs.readFile(portFile, 'utf-8');
    console.log(`Arrêt du serveur sur le port ${port}...`);

    // Essayer d'arrêter proprement via API
    await new Promise((resolve) => {
      exec(`curl -X POST http://localhost:${port}/api/shutdown`, (err) => {
        if (err) {
          console.error('Erreur arrêt propre, kill forcé...');
          exec(`taskkill /IM node.exe /F`);
        }
        resolve();
      });
    });

    // Supprimer le fichier de port
    await fs.unlink(portFile);
    console.log('✅ Serveur arrêté avec succès');
  } catch (err) {
    if (err.code === 'ENOENT') {
      console.log('ℹ️ Aucun serveur enregistré - déjà arrêté ?');
    } else {
      console.error('❌ Erreur arrêt serveur:', err.message);
    }
    process.exit(1);
  }
}

// Gestion des signaux pour un arrêt propre
process.on('SIGINT', async () => {
  console.log('\nRéception SIGINT - Arrêt en cours...');
  await stopServer();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\nRéception SIGTERM - Arrêt en cours...');
  await stopServer();
  process.exit(0);
});

stopServer();
