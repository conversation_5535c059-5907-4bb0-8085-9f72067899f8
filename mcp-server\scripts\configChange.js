import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const LOG_FILE = path.join(__dirname, '../config-changes.log');
const CONFIG_FILE = path.join(__dirname, '../.vscode/mcp.json');

function logAction(action, details = {}) {
  const timestamp = new Date().toISOString();
  const entry = {
    timestamp,
    action,
    details
  };
  fs.appendFileSync(LOG_FILE, JSON.stringify(entry) + '\n');
}

function requestChange() {
  logAction('CHANGE_REQUESTED');
  console.log('Demande de modification envoyée. En attente de validation...');
}

function approveChange() {
  if (!fs.existsSync(CONFIG_FILE + '.pending')) {
    console.error('Aucune modification en attente de validation');
    process.exit(1);
  }
  
  logAction('CHANGE_APPROVED');
  fs.renameSync(CONFIG_FILE + '.pending', CONFIG_FILE);
  console.log('Modification approuvée et appliquée');
}

function showLogs() {
  if (!fs.existsSync(LOG_FILE)) {
    console.log('Aucun historique de modifications');
    return;
  }
  
  const logs = fs.readFileSync(LOG_FILE, 'utf8')
    .split('\n')
    .filter(Boolean)
    .map(JSON.parse);
  
  console.table(logs);
}

const [,, action] = process.argv;

switch (action) {
  case 'request':
    requestChange();
    break;
  case 'approve':
    approveChange();
    break;
  case 'log':
    showLogs();
    break;
  default:
    console.error('Action non reconnue');
    process.exit(1);
}
