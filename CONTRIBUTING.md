# Guide de Contribution GDevAI

## 1. Prérequis
- Node.js v20+
- npm v9+
- Compt<PERSON>
- Accès au repository (en lecture minimum)

## 2. Bonnes Pratiques

### 2.1 Développement
- Respecter les conventions de code (ESLint/Prettier)
- Maintenir 100% de couverture de test
- Documenter toute nouvelle fonctionnalité
- Vérifier les permissions RBAC pour les nouvelles routes

### 2.2 Checklist de Validation
```markdown
- [ ] Tests unitaires passants
- [ ] Tests d'intégration passants  
- [ ] Couverture de test maintenue
- [ ] Documentation mise à jour
- [ ] Compatibilité avec GDevelop vérifiée
```

## 3. Workflow de Contribution

```mermaid
graph TD
    A[Fork du dépôt] --> B[Création branche]
    B --> C[Implémentation]
    C --> D[Tests]
    D --> E[Pull Request]
    E --> F{Review}
    F -->|Approuvé| G[Merge]
    F -->|Modifications| C
```

## 4. Structure des Commits
Format : `type(scope): description`

Exemples :
```
feat(ai): ajout support OpenAI GPT-4
fix(auth): correction bug expiration token
docs(api): mise à jour swagger
```

## 5. Dépannage

### Problème : Tests RBAC échouent
Solution :
```bash
npm run test:rbac -- --updateSnapshot
```

### Problème : Conflits de dépendances
Solution :
```bash
npm ci
npm run fix-deps
```

## 6. Outils Utiles
- `npm run lint` : Vérification du code
- `npm run format` : Formatage automatique
- `npm test:watch` : Tests en mode watch
- `npm run coverage` : Rapport de couverture

## 7. Contact
Pour toute question : 
- Issues GitHub
- Slack #dev-gdevai
- Email : <EMAIL>
