import net from 'net';
import { exec } from 'child_process';
import { logger } from '#logger';

const PORTS_TO_CHECK = process.argv.includes('--ports') 
  ? process.argv[process.argv.indexOf('--ports') + 1].split(',').map(Number)
  : [3002, 3007]; // Ports par défaut

async function isPortInUse(port) {
  return new Promise((resolve) => {
    const server = net.createServer()
      .once('error', () => resolve(true))
      .once('listening', () => {
        server.close();
        resolve(false);
      })
      .listen(port);
  });
}

async function killProcessOnPort(port) {
  return new Promise((resolve, reject) => {
    exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
      if (error) return reject(error);
      
      const lines = stdout.trim().split('\n');
      const pids = new Set();
      
      lines.forEach(line => {
        const match = line.trim().split(/\s+/);
        if (match.length > 4) pids.add(match[4]);
      });

      if (pids.size === 0) {
        logger.info(`Aucun processus trouvé sur le port ${port}`);
        return resolve();
      }

      pids.forEach(pid => {
        exec(`taskkill /PID ${pid} /F`, (err) => {
          if (err) {
            logger.error(`Échec de la fermeture du processus ${pid}`, err);
            return reject(err);
          }
          logger.info(`Processus ${pid} sur le port ${port} terminé`);
        });
      });
      resolve();
    });
  });
}

export async function checkPorts() {
  try {
    for (const port of PORTS_TO_CHECK) {
      if (await isPortInUse(port)) {
        logger.warn(`Port ${port} déjà utilisé - tentative de libération...`);
        await killProcessOnPort(port);
      }
    }
    logger.info('Vérification des ports terminée');
    return true;
  } catch (error) {
    logger.error('Erreur lors de la gestion des ports', error);
    return false;
  }
}

// Exécution directe si appelé en CLI
if (process.argv.includes('--run')) {
  checkPorts().then(success => {
    process.exit(success ? 0 : 1);
  });
}
