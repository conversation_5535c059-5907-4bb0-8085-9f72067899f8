import { vi, describe, it, expect, beforeEach } from 'vitest';
import { createChatServiceMocks } from './mocks/chatServiceMocks.js';
import logger from '#logger';

// Import ChatService after mocks are set up
import ChatService from '@mcp-server/services/chatService.js';

// Setup global logger mock
vi.mock('#logger', async (importOriginal) => {
  const actual = await importOriginal()
  return {
    ...actual,
    default: {
      info: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      child: () => ({
        info: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn()
      })
    }
  }
});

// Initialize mocks
const {
  rbac: mockRbac,
  logger: mockLogger,
  storage: mockStorage,
  aiService: mockAI
} = createChatServiceMocks();

// Mock dependencies
vi.mock('@mcp-server/services/aiService.js', () => ({
  default: vi.fn(() => mockAI)
}));

vi.mock('@mcp-server/services/MessageStorage.js', () => ({
  default: vi.fn(() => mockStorage)
}));

describe('ChatService', () => {
  let chatService;

  beforeEach(() => {
    vi.resetAllMocks();

    // Reset mocks to default behavior
    mockRbac.hasPermission.mockImplementation((user, permission) => {
      return permission === 'chat.access';
    });

    chatService = new ChatService(
      mockAI,
      mockLogger,
      mockStorage,
      mockRbac
    );
  });

  it('should process message with standard response format', async () => {
    // Verify permission check
    const response = await chatService.processMessage('test', { username: 'testuser' });
    expect(mockRbac.hasPermission).toHaveBeenCalledWith({ username: 'testuser' }, 'chat.access');

    expect(response).toMatchObject({
      data: {
        text: 'mock response', // Utiliser la valeur exacte du mock
        contextId: expect.any(String)
      },
      metadata: {
        timestamp: expect.any(Number),
        isAI: true,
        source: 'chatService'
      }
    });
    expect(mockAI.generateText).toHaveBeenCalled();
  });

  it('should save and load context correctly', async () => {
    await chatService.processMessage('test');
    expect(mockStorage.addMessage).toHaveBeenCalled();
  });

  it('should respect MAX_HISTORY limit', async () => {
    // Simuler un mock qui respecte la limite d'historique
    const originalGetHistory = mockStorage.getHistory;
    const originalRemoveOldest = mockStorage.removeOldestMessages;

    // Remplacer les mocks pour ce test spécifique
    mockStorage.getHistory.mockImplementation(() => Array(25).fill({ text: 'msg' }));
    mockStorage.removeOldestMessages.mockImplementation((count) => {
      // Simuler la suppression des messages
      mockStorage.messages = mockStorage.messages.slice(-20);
      return Promise.resolve();
    });

    await chatService.processMessage('test');

    // Vérifier que removeOldestMessages a été appelé
    expect(mockStorage.removeOldestMessages).toHaveBeenCalled();

    // Restaurer les mocks originaux
    mockStorage.getHistory = originalGetHistory;
    mockStorage.removeOldestMessages = originalRemoveOldest;
  });

  it('should return error in standard format when failing', async () => {
    // Force AI to fail instead of storage
    mockAI.generateText.mockRejectedValueOnce(new Error('Test error'));

    const response = await chatService.processMessage('test');

    expect(response).toMatchObject({
      data: {
        error: {
          code: 'CHAT_001',
          message: 'Test error',
          details: expect.any(String)
        }
      },
      metadata: {
        failed: true,
        isAI: false,
        source: 'chatService'
      }
    });
  });

  it('should reject when missing permissions', async () => {
    mockRbac.hasPermission.mockReturnValue(false);
    const response = await chatService.processMessage('test', { username: 'testuser' });

    expect(response).toMatchObject({
      data: {
        error: {
          code: 'CHAT_001',
          message: 'Permission denied',
          details: expect.any(String)
        }
      },
      metadata: {
        failed: true,
        isAI: false,
        source: 'chatService'
      }
    });
    expect(mockAI.generateText).not.toHaveBeenCalled();
    expect(mockRbac.hasPermission).toHaveBeenCalledWith(
      { username: 'testuser' },
      'chat.access'
    );
    expect(mockLogger.error).toHaveBeenCalledWith(
      'Permission denied for user testuser',
      expect.objectContaining({
        code: 'CHAT_001'
      })
    );
  });

  it('should log API errors correctly', async () => {
    const testError = new Error('API Error');
    mockAI.generateText.mockRejectedValue(testError);

    await chatService.processMessage('test');

    expect(mockLogger.error).toHaveBeenCalledWith(
      'Message processing failed:',
      testError
    );
  });

  it('should maintain consistent response format for success', async () => {
    const response = await chatService.processMessage('test');

    // Verify standard response structure
    expect(response).toHaveProperty('data');
    expect(response).toHaveProperty('metadata');

    // Verify metadata properties
    expect(response.metadata).toMatchObject({
      timestamp: expect.any(Number),
      isAI: true,
      source: 'chatService',
      failed: false
    });

    // Verify data properties
    expect(response.data).toMatchObject({
      text: expect.any(String),
      contextId: expect.any(String)
    });
  });
});
