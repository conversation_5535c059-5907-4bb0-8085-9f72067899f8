/**
 * Tests ultra-simplifiés pour exampleService
 *
 * Ce fichier contient des tests extrêmement simples pour exampleService,
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from '../utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Classe simplifiée pour exampleService
class ExampleService {
  constructor(options = {}) {
    this.options = {
      // Options par défaut
      ...options
    };

    this.logger = options.logger || loggerMock;
    // Autres propriétés
  }

  // Méthodes simplifiées
  async exampleMethod(param) {
    this.logger.info(`Méthode exemple appelée avec ${param}`);
    return { status: 'success', param };
  }
}

console.log('\n=== DÉBUT DES TESTS EXAMPLESERVICE SIMPLIFIÉS ===\n');

describe('ExampleService (Simplifié)', () => {
  let instance;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();

    // Créer une nouvelle instance
    instance = new ExampleService({ logger: loggerMock });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('devrait appeler la méthode exemple', async () => {
    // Arrange
    const param = 'test';

    // Act
    const result = await instance.exampleMethod(param);

    // Assert
    expect(result).toEqual({ status: 'success', param: 'test' });
    expect(loggerMock.info).toHaveBeenCalledWith(expect.stringContaining('Méthode exemple appelée avec test'));
  });

  // TODO: Ajouter d'autres tests spécifiques à exampleService
});

console.log('\n=== FIN DES TESTS EXAMPLESERVICE SIMPLIFIÉS ===\n');
