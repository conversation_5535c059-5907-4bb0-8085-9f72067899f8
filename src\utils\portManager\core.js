import { EventEmitter } from 'events';
import logger from '../logger.js';

class PortManagerCore extends EventEmitter {
  constructor() {
    super();
    this.adapters = new Map();
    this.plugins = new Map();
    this.logger = logger.child({ module: 'PortManager' });
  }

  registerAdapter(name, adapter) {
    if (this.adapters.has(name)) {
      throw new Error(`Adapter ${name} already registered`);
    }
    this.adapters.set(name, adapter);
    this.logger.debug(`Registered adapter: ${name}`);
  }

  registerPlugin(name, plugin) {
    this.plugins.set(name, plugin);
    this.emit('pluginRegistered', name);
    this.logger.debug(`Registered plugin: ${name}`);
  }

  async findFreePort(options = {}) {
    this.emit('beforeFindPort', options);
    const [adapter] = this.adapters.values(); // Prend le premier adapter
    const port = await adapter.findFreePort();
    this.emit('afterFindPort', port);
    return port;
  }

  async killProcessOnPort(port) {
    this.emit('beforeKillPort', port);
    const [adapter] = this.adapters.values();
    await adapter.killProcessOnPort(port);
    this.emit('afterKillPort', port);
  }
}

export default PortManagerCore;
