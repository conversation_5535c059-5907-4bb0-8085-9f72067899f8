/**
 * Tests ultra-simplifiés pour GDevelop Module
 *
 * Ce fichier contient des tests extrêmement simples pour le module GDevelop,
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock, createRouterMock } from './utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Mock simple du module GDevelop
const createGDevelopModuleMock = () => ({
  process: vi.fn(),
  name: 'gdevelop',
  version: '1.0.0'
});

// Classe GDevelop Module simplifiée pour les tests
class SimplifiedGDevelopModule {
  constructor(options = {}) {
    this.logger = options.logger || loggerMock;
    this.name = 'gdevelop';
    this.version = '1.0.0';
  }

  async process(message) {
    const { action, projectId, data } = message;

    this.logger.debug(`Processing GDevelop action: ${action}`, { projectId });

    try {
      switch (action) {
        case 'getProject':
          return this.getProject(projectId);

        case 'updateProject':
          return this.updateProject(projectId, data);

        case 'getEvents':
          return this.getEvents(projectId);

        case 'syncResources':
          return this.syncResources(projectId);

        case 'getChanges':
          return this.getChanges(projectId);

        default:
          throw new Error(`Action non supportée: ${action}`);
      }
    } catch (error) {
      this.logger.error('Erreur module GDevelop:', error);
      throw error;
    }
  }

  async getProject(projectId) {
    if (!projectId) {
      throw new Error('Project ID is required');
    }

    return {
      status: 'success',
      project: {
        id: projectId,
        name: `Mock project ${projectId}`,
        version: '1.0.0',
        createdAt: new Date().toISOString()
      }
    };
  }

  async updateProject(projectId, data) {
    if (!projectId) {
      throw new Error('Project ID is required');
    }

    if (!data || Object.keys(data).length === 0) {
      throw new Error('Données de projet invalides');
    }

    return {
      status: 'success',
      projectId,
      updatedData: data,
      updatedAt: new Date().toISOString()
    };
  }

  async getEvents(projectId) {
    if (!projectId) {
      throw new Error('Project ID is required');
    }

    return {
      status: 'success',
      events: [
        { name: 'Start Scene', type: 'standard', id: 'event1' },
        { name: 'Game Over', type: 'standard', id: 'event2' }
      ]
    };
  }

  async syncResources(projectId) {
    if (!projectId) {
      throw new Error('Project ID is required');
    }

    return {
      status: 'success',
      resources: ['sprite1.png', 'background.jpg', 'sound1.wav'],
      syncedAt: new Date().toISOString()
    };
  }

  async getChanges(projectId) {
    if (!projectId) {
      throw new Error('Project ID is required');
    }

    return {
      status: 'success',
      changes: [
        {
          timestamp: new Date().toISOString(),
          changes: { title: 'Updated Game', version: '1.1.0' }
        }
      ]
    };
  }
}

console.log('\n=== DÉBUT DES TESTS GDEVELOP MODULE SIMPLIFIÉS ===\n');

describe('GDevelop Module (Simplifié)', () => {
  let gdevelopModule;
  let routerMock;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();

    // Créer une nouvelle instance
    gdevelopModule = new SimplifiedGDevelopModule({
      logger: loggerMock
    });

    // Créer un router mock avec le module GDevelop
    routerMock = createRouterMock();
    routerMock.modules.set('gdevelop', gdevelopModule);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Enregistrement du module', () => {
    it('devrait enregistrer le module GDevelop', () => {
      // Assert
      expect(routerMock.modules.has('gdevelop')).toBe(true);
      expect(routerMock.modules.get('gdevelop')).toBe(gdevelopModule);
    });

    it('devrait avoir les propriétés de base', () => {
      // Assert
      expect(gdevelopModule.name).toBe('gdevelop');
      expect(gdevelopModule.version).toBe('1.0.0');
    });
  });

  describe('Action getProject', () => {
    it('devrait récupérer un projet avec succès', async () => {
      // Arrange
      const projectId = 'test123';

      // Act
      const result = await gdevelopModule.process({
        action: 'getProject',
        projectId
      });

      // Assert
      expect(result.status).toBe('success');
      expect(result.project).toMatchObject({
        id: projectId,
        name: `Mock project ${projectId}`,
        version: '1.0.0',
        createdAt: expect.any(String)
      });
      expect(loggerMock.debug).toHaveBeenCalledWith(
        'Processing GDevelop action: getProject',
        { projectId }
      );
    });

    it('devrait rejeter si le projectId est manquant', async () => {
      // Act & Assert
      await expect(gdevelopModule.process({
        action: 'getProject'
      })).rejects.toThrow('Project ID is required');
    });
  });

  describe('Action updateProject', () => {
    it('devrait mettre à jour un projet avec succès', async () => {
      // Arrange
      const projectId = 'test123';
      const data = { title: 'Updated Game', description: 'New description' };

      // Act
      const result = await gdevelopModule.process({
        action: 'updateProject',
        projectId,
        data
      });

      // Assert
      expect(result.status).toBe('success');
      expect(result.projectId).toBe(projectId);
      expect(result.updatedData).toEqual(data);
      expect(result.updatedAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    });

    it('devrait rejeter avec des données invalides', async () => {
      // Act & Assert
      await expect(gdevelopModule.process({
        action: 'updateProject',
        projectId: 'test123',
        data: {}
      })).rejects.toThrow('Données de projet invalides');
    });
  });

  describe('Action getEvents', () => {
    it('devrait récupérer les événements avec succès', async () => {
      // Arrange
      const projectId = 'test123';

      // Act
      const result = await gdevelopModule.process({
        action: 'getEvents',
        projectId
      });

      // Assert
      expect(result.status).toBe('success');
      expect(result.events).toHaveLength(2);
      expect(result.events[0]).toMatchObject({
        name: 'Start Scene',
        type: 'standard',
        id: 'event1'
      });
    });
  });

  describe('Action syncResources', () => {
    it('devrait synchroniser les ressources avec succès', async () => {
      // Arrange
      const projectId = 'test123';

      // Act
      const result = await gdevelopModule.process({
        action: 'syncResources',
        projectId
      });

      // Assert
      expect(result.status).toBe('success');
      expect(result.resources).toEqual(['sprite1.png', 'background.jpg', 'sound1.wav']);
      expect(result.syncedAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
    });
  });

  describe('Action getChanges', () => {
    it('devrait récupérer les changements avec succès', async () => {
      // Arrange
      const projectId = 'test123';

      // Act
      const result = await gdevelopModule.process({
        action: 'getChanges',
        projectId
      });

      // Assert
      expect(result.status).toBe('success');
      expect(result.changes).toHaveLength(1);
      expect(result.changes[0]).toMatchObject({
        timestamp: expect.any(String),
        changes: { title: 'Updated Game', version: '1.1.0' }
      });
    });
  });

  describe('Gestion des erreurs', () => {
    it('devrait gérer les actions non supportées', async () => {
      // Act & Assert
      await expect(gdevelopModule.process({
        action: 'invalidAction',
        projectId: 'test123'
      })).rejects.toThrow('Action non supportée: invalidAction');

      expect(loggerMock.error).toHaveBeenCalledWith(
        'Erreur module GDevelop:',
        expect.any(Error)
      );
    });

    it('devrait logger les erreurs correctement', async () => {
      // Arrange
      const error = new Error('Test error');

      // Act
      try {
        await gdevelopModule.process({
          action: 'invalidAction',
          projectId: 'test123'
        });
      } catch (e) {
        // Expected error
      }

      // Assert
      expect(loggerMock.error).toHaveBeenCalledWith(
        'Erreur module GDevelop:',
        expect.any(Error)
      );
    });
  });
});

console.log('\n=== FIN DES TESTS GDEVELOP MODULE SIMPLIFIÉS ===\n');
