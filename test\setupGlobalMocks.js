/**
 * Configuration globale des mocks pour les tests
 * Ce fichier est chargé automatiquement par Vitest avant l'exécution des tests
 */
import { vi } from 'vitest';
import universalLogger, { resetLoggerMocks, logger, createLogger } from './__mocks__/universal-logger.js';

// Mock global du logger - utilise le mock universel
vi.mock('#logger', () => {
  return {
    default: universalLogger,
    logger: universalLogger,
    createLogger: universalLogger.createLogger,
    resetLoggerMocks: universalLogger.resetLoggerMocks,
    logRequest: universalLogger.logRequest,
    logError: universalLogger.logError
  };
});

// Mock pour les modules qui utilisent le logger directement
vi.mock('pino', () => {
  return {
    default: vi.fn().mockImplementation(() => universalLogger),
    pino: vi.fn().mockImplementation(() => universalLogger)
  };
});

// Mock pour winston (utilisé dans certains modules)
vi.mock('winston', () => {
  return {
    default: {
      createLogger: vi.fn().mockImplementation(() => universalLogger),
      format: {
        combine: vi.fn(),
        timestamp: vi.fn(),
        printf: vi.fn(),
        colorize: vi.fn(),
        json: vi.fn(),
        errors: vi.fn(),
        splat: vi.fn()
      },
      transports: {
        Console: vi.fn(),
        File: vi.fn()
      }
    },
    createLogger: vi.fn().mockImplementation(() => universalLogger),
    format: {
      combine: vi.fn(),
      timestamp: vi.fn(),
      printf: vi.fn(),
      colorize: vi.fn(),
      json: vi.fn(),
      errors: vi.fn(),
      splat: vi.fn()
    },
    transports: {
      Console: vi.fn(),
      File: vi.fn()
    }
  };
});

// Mock pour winston-daily-rotate-file
vi.mock('winston-daily-rotate-file', () => {
  return {
    default: vi.fn(),
    __esModule: true
  };
});

// Mock pour GDevelopAPI
vi.mock('../core/services/gdevelopAPI.js', () => {
  const GDevelopAPIMock = require('../test/__mocks__/gdevelopAPI.js').default;
  return {
    default: GDevelopAPIMock
  };
});

// Mock pour Core
vi.mock('../core/index.js', () => {
  const MockCore = require('../test/__mocks__/core.js').default;
  return {
    default: MockCore
  };
});

// Mock pour Router
vi.mock('../core/router.js', () => {
  const MockRouter = require('../test/__mocks__/router.js').default;
  return {
    default: MockRouter
  };
});

// Mock pour les modules qui utilisent process.exit
vi.mock('process', () => {
  const actual = { ...process };
  return {
    ...actual,
    exit: vi.fn()
  };
});

// Réinitialisation des mocks avant chaque test
beforeEach(() => {
  // Réinitialiser le logger
  resetLoggerMocks();

  // Réinitialiser process.exit
  if (process.exit && typeof process.exit.mockClear === 'function') {
    process.exit.mockClear();
  }

  // Réinitialiser les autres mocks
  vi.clearAllMocks();
});

// Nettoyage après tous les tests
afterAll(() => {
  vi.restoreAllMocks();
});

// Ajouter une variable globale pour indiquer que les tests sont en cours
global.__TEST__ = true;

console.log('Mocks globaux configurés');
