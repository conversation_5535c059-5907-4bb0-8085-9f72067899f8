import { defineConfig } from 'vitest/config'
import * as path from 'path'

console.log('[CONFIG] Loading vitest configuration...')

export default defineConfig({
  test: {
    globals: true,
    environment: 'node',
    env: {
      NODE_ENV: 'test',
      DOTENV_CONFIG_PATH: './.env.test'
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json-summary', 'lcov', 'html'],
      reportsDirectory: './html/coverage',
      clean: true,
      all: true,
      include: [
        'mcp-server/**/*.js',
        'core/**/*.js',
        'src/**/*.{js,ts}',
        'scripts/**/*.js'
      ],
      exclude: [
        '**/node_modules/**',
        '**/test/**',
        '**/config/**',
        '**/public/**'
      ],
      thresholds: {
        lines: 80,
        functions: 70,
        branches: 70,
        statements: 80
      }
    },
    setupFiles: [
      './test/setupMocks.js',
      './scripts/test-setup.js',
      './test/setup.ts',
      './tests/setup.ts'
    ],
    logHeapUsage: true,
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true
      }
    },
    hookTimeout: 30000,
    testTimeout: 10000,
    isolate: true,
    sequence: {
      shuffle: false
    },
    cache: false,
    include: [
      'test/**/*.test.{js,ts}',
      'test/**/*.spec.{js,ts}',
      'tests/**/*.test.{js,ts}',
      'tests/**/*.spec.{js,ts}'
    ]
  },
  resolve: {
    alias: {
      '#logger': path.resolve(__dirname, './src/utils/logger'),
      '@mcp-server': path.resolve(__dirname, './mcp-server'),
      '@': path.resolve(__dirname, './src'),
      '#mocks': path.resolve(__dirname, './test/mocks'),
      '#gdevelop': path.resolve(__dirname, './core/modules/gdevelop'),
      '#core': path.resolve(__dirname, './core'),
      '#router': path.resolve(__dirname, './core/router'),
      '@backend': path.resolve(__dirname, './src/backend'),
      '@frontend': path.resolve(__dirname, './src/frontend/src'),
      '@utils': path.resolve(__dirname, './src/utils'),
      '@integrations': path.resolve(__dirname, './integrations')
    }
  },
  esbuild: {
    target: 'node20'
  }
})
