import crypto from 'crypto';
import fs from 'fs';
import keytar from 'keytar';
import { execSync } from 'child_process';
import readline from 'readline';

// Configuration
const VAULT_FILE = '.secure-vault';
const BACKUP_CONFIG = 'config/backup.json';

function encryptWithSystemKey(data) {
  // Solution cross-platform avec keytar
  const hexData = data.toString('hex');
  keytar.setPassword('GDevAI', 'system-key', hexData);
  return Buffer.from(hexData);
}

async function setupSecureVault() {
  // 1. Demander mot de passe maître
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const password = await new Promise(resolve => 
    rl.question('Créez un mot de passe maître pour le coffre sécurisé: ', resolve)
  );
  rl.close();
  
  // 2. <PERSON><PERSON><PERSON>rer sel et clé
  const salt = crypto.randomBytes(16);
  const iterations = 100000;
  const key = crypto.pbkdf2Sync(password, salt, iterations, 32, 'sha256');

  // 3. <PERSON>er la clé sécurisée
  const encryptedKey = encryptWithSystemKey(key);
  
  fs.writeFileSync(VAULT_FILE, JSON.stringify({
    salt: salt.toString('hex'),
    iterations,
    encryptedKey: encryptedKey.toString('hex')
  }));

  // 4. Configurer backup
  fs.writeFileSync(BACKUP_CONFIG, JSON.stringify({
    maxChanges: 100 * 1024 * 1024 // 100MB par défaut
  }));

  // 5. Installer tâches planifiées
  if (process.platform === 'win32') {
    execSync(`schtasks /create /tn "GDevAI Backup" /sc daily /st 02:00 /ru ${process.env.USERNAME} /rl HIGHEST /tr "powershell -Command Start-Process npm -ArgumentList 'run backup' -WindowStyle Hidden" /f`);
  } else {
    execSync(`echo "0 2 * * * cd ${__dirname} && npm run backup" | crontab -`);
  }

  console.log('✅ Configuration sécurisée terminée');
}

setupSecureVault().catch(err => {
  console.error('Erreur lors de la configuration:', err);
  process.exit(1);
});
