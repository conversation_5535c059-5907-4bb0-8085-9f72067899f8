import { vi, describe, it, expect, beforeEach, afterEach, afterAll } from 'vitest';
import MockMCPServer from './mocks/mcpServer.js';

// Mock des modules natifs
vi.mock('net');
vi.mock('child_process');
vi.mock('fs/promises');
vi.mock('fs');

// Mock de la configuration
vi.mock('../mcp-server/config/default.json', () => ({
  default: {
    server: {
      port: null // Pas de port configuré pour les tests
    },
    resources: {
      frontend: 'public'
    },
    tools: {
      chat: { enabled: false },
      deepseek: { enabled: false }
    }
  }
}), { virtual: true });

console.log('\n=== DÉBUT DES TESTS PORT MANAGEMENT SIMPLIFIÉS ===\n');

describe('Port Management Tests Simplifiés', () => {
  let mockServer;
  
  beforeEach(() => {
    // Réinitialiser les mocks
    vi.clearAllMocks();
    
    // Créer une nouvelle instance du serveur mocké
    mockServer = new MockMCPServer();
  });
  
  afterEach(() => {
    // Nettoyer après chaque test
    mockServer.reset();
  });
  
  afterAll(() => {
    // Nettoyer après tous les tests
    vi.restoreAllMocks();
    console.log('\n=== FIN DES TESTS PORT MANAGEMENT SIMPLIFIÉS ===\n');
  });
  
  it('should check if port is free', async () => {
    // Test de la méthode checkPort
    const isOccupied = await mockServer.checkPort(7000);
    
    // Le port 7000 devrait être libre selon notre mock
    expect(isOccupied).toBe(false);
    
    // Vérifier que la méthode a été appelée
    expect(mockServer.checkPortSpy).toHaveBeenCalledWith(7000);
  });
  
  it('should check if port is occupied', async () => {
    // Test de la méthode checkPort avec un port occupé
    const isOccupied = await mockServer.checkPort(7001);
    
    // Le port 7001 devrait être occupé selon notre mock
    expect(isOccupied).toBe(true);
    
    // Vérifier que la méthode a été appelée
    expect(mockServer.checkPortSpy).toHaveBeenCalledWith(7001);
  });
  
  it('should handle port killing', async () => {
    // Test de la méthode forceKill
    await mockServer.forceKill(7000);
    
    // Vérifier que la méthode a été appelée
    expect(mockServer.forceKillSpy).toHaveBeenCalledWith(7000);
  });
  
  it('should save port correctly', async () => {
    // Test de la méthode savePort
    await mockServer.savePort(8000);
    
    // Vérifier que la méthode a été appelée
    expect(mockServer.savePortSpy).toHaveBeenCalledWith(8000);
    
    // Vérifier que le port a été mis à jour
    expect(mockServer.port).toBe(8000);
  });
  
  it('should start server on available port', async () => {
    // Test de la méthode start
    const server = await mockServer.start();
    
    // Vérifier que la méthode a été appelée
    expect(mockServer.startSpy).toHaveBeenCalled();
    
    // Vérifier que le serveur a été démarré
    expect(server).not.toBeNull();
  });
  
  it('should find available port', async () => {
    // Test de la méthode loadOrFindPort
    const port = await mockServer.loadOrFindPort();
    
    // Vérifier que le port est valide
    expect(port).toBeGreaterThanOrEqual(7000);
    expect(port).toBeLessThan(7003);
  });
});
