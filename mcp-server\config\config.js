// Configuration dynamique du serveur MCP
const config = {
  server: {
    port: 7000,  // Port forcé
    host: process.env.HOST || 'localhost',
    portOptions: {
      alternates: [50008, 50009],  // Ports de fallback
      retry: {
        attempts: 3,
        delay: 1000
      }
    }
  },
  resources: {
    frontend: process.env.FRONTEND_PATH || 'public'
  },
  tools: {
    chat: {
      enabled: process.env.CHAT_ENABLED !== 'false'
    },
    deepseek: {
      enabled: process.env.DEEPSEEK_ENABLED !== 'false'
    }
  },
  logging: {
    level: process.env.LOG_LEVEL || 'debug',
    dir: process.env.LOG_DIR || 'logs',
    filename: 'mcp-server.log',
    encoding: 'utf8',
    maxFiles: 7, 
    maxSize: '10m'
  }
};

// Validation de la configuration
if (typeof config.server.port !== 'number') {
  throw new Error('Port must be a number');
}

export default config;
