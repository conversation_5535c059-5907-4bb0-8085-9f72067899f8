// Auto-generated adapter for linux
export default class LinuxAdapter {
  /**
   * Find process using port
   * @param {number} port 
   * @returns {Promise<{pid: number, name: string}|null>}
   */
  async findProcessByPort(port) {
    console.warn('linux adapter not fully implemented');
    return null;
  }

  /**
   * Kill process by PID
   * @param {number} pid 
   * @returns {Promise<boolean>}
   */
  async killProcess(pid) {
    console.warn('linux adapter not fully implemented');
    return false;
  }
}
