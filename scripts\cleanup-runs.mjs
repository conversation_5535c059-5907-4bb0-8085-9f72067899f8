import { execSync } from 'child_process';

try {
  console.log('Fetching all workflow runs...');
  const runs = JSON.parse(execSync('gh run list --limit 50 --json databaseId,workflowName,status,conclusion').toString());
  
  // Target both CI and CD pipelines
  const failedRuns = runs.filter(r => 
    r.status === 'completed' && 
    r.conclusion === 'failure' &&
    (r.workflowName === 'CI Pipeline' || r.workflowName === 'CD Pipeline')
  );

  if (failedRuns.length === 0) {
    console.log('No failed runs to clean up');
    process.exit(0);
  }

  console.log(`Found ${failedRuns.length} failed runs to delete`);
  
  for (const run of failedRuns) {
    try {
      console.log(`Deleting run ${run.databaseId}...`);
      execSync(`gh api -X DELETE "repos/{owner}/{repo}/actions/runs/${run.databaseId}"`);
      console.log(`Successfully deleted run ${run.databaseId}`);
    } catch (err) {
      console.error(`Error deleting run ${run.databaseId}:`, err.message);
    }
  }

  console.log('Cleanup completed');
} catch (error) {
  console.error('Error during cleanup:', error.message);
  process.exit(1);
}
