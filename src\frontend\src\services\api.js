export const apiService = {
  getGDevelopProjects: (user) => {
    if (!user?.permissions?.includes('gdevelop.integrate')) {
      return Promise.reject(new Error('Permission required: gdevelop.integrate'));
    }
    return Promise.resolve([]);
  },
  
  getGDevelopProject: (id, user) => {
    if (!user?.permissions?.includes('gdevelop.integrate')) {
      return Promise.reject(new Error('Permission required: gdevelop.integrate'));
    }
    return Promise.resolve({ id });
  },
  
  syncGDevelopProject: (id, user) => {
    if (!user?.permissions?.includes('gdevelop.integrate')) {
      return Promise.reject(new Error('Permission required: gdevelop.integrate'));
    }
    return Promise.resolve({ status: 'success' });
  },
  
  sendMessage: async (message, user) => {
    if (!user?.permissions?.includes('chat.access')) {
      return Promise.reject(new Error('Permission required: chat.access'));
    }

    try {
      const response = await fetch('/core/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${user.token}`
        },
        body: JSON.stringify({
          action: 'sendMessage',
          message,
          user
        })
      });

      if (response.status === 403) {
        throw new Error('Access denied - insufficient permissions');
      }

      return await response.json();
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }
};
