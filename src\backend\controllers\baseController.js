import { formatResponse, formatError } from '../../utils/responseFormatter.js'

export class BaseController {
  constructor() {
    this.response = {
      /**
       * Envoie une réponse standardisée
       * @param {Response} res - Objet réponse Express
       * @param {*} data - Données à retourner
       * @param {Object} [metadata={}] - Métadonnées supplémentaires
       */
      send: (res, data, metadata) => {
        res.json(formatResponse(data, metadata))
      },

      /**
       * Gère les erreurs standardisées
       * @param {Response} res - Objet réponse Express
       * @param {Error} error - Erreur à formater
       * @param {string} [code='INTERNAL_ERROR'] - Code d'erreur
       * @param {number} [status=500] - Statut HTTP
       */
      error: (res, error, code = 'INTERNAL_ERROR', status = 500) => {
        res.status(status).json(formatError(error, code))
      }
    }
  }

  /**
   * Méthode utilitaire pour wrapper les contrôleurs async
   * @param {Function} fn - Fonction contrôleur async
   */
  asyncHandler(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next))
        .catch(err => this.response.error(res, err))
    }
  }
}
