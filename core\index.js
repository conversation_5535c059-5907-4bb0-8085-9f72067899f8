// Modules externes
import express from 'express';
import { createLogger, format, transports } from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';

// Node.js core
import { createServer } from 'node:net';
import path from 'path';
import fs from 'fs';
import { execSync } from 'child_process';

// Utilitaires internes
import { getAIStats } from '../src/utils/aiMetrics.js';
import { authMiddleware, generateToken } from '../src/utils/auth.js';

// Contrôleurs
import metricsController from '../src/backend/controllers/metricsController.js';

// Modules internes
import Router from './router.js';
import ChatModule from './modules/chat.js';
import GDevelopModule from './modules/gdevelop.js';
import AIModule from './modules/ai.js';

// Configure logger
  const configureLogger = (config) => {
    if (!config) {
      console.warn('Logger configuration is missing, using defaults');
      config = {
        level: 'info',
        console: { enabled: true },
        file: { enabled: false }
      };
    }
    const logConfig = config;
  
    const logger = createLogger({
      level: logConfig.level,
      format: format.combine(
        format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        format.errors({ stack: true }),
        format.splat(),
        format.json()
      ),
      transports: [
        new transports.Console({
          silent: !logConfig.console.enabled,
          format: format.combine(
            format.colorize(),
            format.printf(({ timestamp, level, message, stack }) => {
              const statusIcon = level === 'error' ? '🔴' : 
                               message.includes('server') || message.includes('port') ? '🟢' : '';
              return `${statusIcon} [${timestamp}] ${level}: ${message}${stack ? '\n' + stack : ''}`;
            })
          )
        })
      ]
    });

    return logger;
  };

  const configureFileTransport = (logger, fileConfig) => {
    if (fileConfig.enabled) {
      try {
        logger.debug('Attempting to configure file transport at:', fileConfig.path);
        
        const fileTransport = new transports.File({
          filename: fileConfig.path,
          level: fileConfig.level || 'debug'
        });
        
        fileTransport.on('error', (error) => {
          logger.error('File transport error:', error);
        });
        
        logger.add(fileTransport);
        logger.debug('File transport successfully configured');
        logger.debug(`File transport configured at: ${fileConfig.path}`);
      } catch (error) {
        logger.error('Failed to configure file transport:', error);
      }
    }
  };

class Core {
  constructor() {
    this.app = express();
    this.app.use(express.static('html'));
    this.router = new Router();
    this.server = null;
    // Initialiser un logger temporaire avec la config par défaut
    this.logger = configureLogger({
      level: 'debug',
      console: { enabled: true }
    });
  }

  async checkPort(port) {
    try {
      const output = execSync(`netstat -ano | findstr :${port}`).toString();
      const pids = [...new Set(output.match(/\d+$/gm))];
      
      if (pids.length === 0) {
        this.logger.debug(`Port ${port} disponible`);
        return false;
      }

      // Vérifie si c'est un processus GDevAI
      const processInfo = execSync(`tasklist /FI "PID eq ${pids[0]}"`).toString();
      if (processInfo.includes('node.exe') || processInfo.includes('GDevAI')) {
        this.logger.debug(`Processus GDevAI trouvé sur port ${port}, tentative de kill...`);
        execSync(`taskkill /PID ${pids[0]} /F`);
        return false; // Réessayer après kill
      }
      
      this.logger.debug(`Port ${port} occupé par processus externe`);
      return true;
    } catch (err) {
      this.logger.error(`Erreur vérification port: ${err.message}`);
      return true; // Considérer comme occupé en cas d'erreur
    }
  }

  killProcessOnPort(port) {
    try {
      this.logger.debug(`Finding process on port ${port}...`);
      const findCmd = this.router.config?.server?.processManagement?.portFinderCommand?.replace('{port}', port) || `netstat -ano | findstr :${port}`;
      this.logger.debug(`Executing: ${findCmd}`);
      const result = execSync(findCmd).toString();
      this.logger.debug(`Result: ${result}`);
      
      if (!result.trim()) {
        this.logger.debug('No process found');
        return false;
      }

      const pid = result.trim().split(/\s+/).pop();
      this.logger.debug(`Found PID: ${pid}`);

      // Essayer d'abord avec taskkill standard
      try {
        const killCmd = `taskkill /F /T /PID ${pid}`;
        this.logger.debug(`Executing: ${killCmd}`);
        execSync(killCmd);
        this.logger.debug('Process killed successfully');
        return true;
      } catch (e) {
      this.logger.warn('Standard kill failed, trying alternative method...');
        
        // Méthode alternative: utiliser net stop si c'est un service
        try {
          const serviceName = execSync(`tasklist /svc /FI "PID eq ${pid}"`).toString();
          if (serviceName.includes(':')) {
            const service = serviceName.split(':')[1].trim().split(/\s+/)[0];
            execSync(`net stop "${service}" /y`);
            this.logger.debug(`Service ${service} stopped successfully`);
            return true;
          }
        } catch (e) {
        this.logger.warn('Service stop failed:', e.message);
        }

        // Si tout échoue, forcer la libération du port
        try {
          execSync(`netsh int ip reset`);
          execSync(`netsh winsock reset`);
          this.logger.debug('Network stack reset, port should be free');
          return true;
        } catch (e) {
          this.logger.error('All kill methods failed:', e.message);
          return false;
        }
      }
    } catch (e) {
    this.logger.error('Error killing process:', e.message);
      return false;
    }
  }

  async startServer(port) {
    // Autoriser le port 0 pour l'auto-assignation
    if (isNaN(port) || (port !== 0 && (port < 1 || port > 65535))) {
      throw new Error('Invalid port number');
    }

    // Utiliser port 0 pour allocation automatique
    if (port === 0 || this.router.config?.server?.portCheck?.enabled) {
      return new Promise((resolve) => {
        this.server = this.app.listen(0, () => {
          const assignedPort = this.server.address().port;
          this.logger.info(`Server started on auto-assigned port ${assignedPort}`);
          resolve(this.server);
        });
      });
    }
    
    // Ancienne approche pour compatibilité
    return new Promise((resolve) => {
      this.server = this.app.listen(port, () => {
        this.logger.info(`Server started on port ${port}`);
        resolve(this.server);
      });
    });
  }

  async loadModules() {
    // Load and register configured modules
    if (this.router.config.modules.chat.enabled) {
      const chatModule = new ChatModule(this.logger);
      this.router.registerModule('chat', chatModule);
      this.logger.info('Chat module loaded');
    }
    if (this.router.config.modules.gdevelop.enabled) {
      const gdevelopModule = new GDevelopModule(this.logger);
      this.router.registerModule('gdevelop', gdevelopModule);
      this.logger.info('GDevelop module loaded');
    }
    // Vérifier si la configuration des modules existe
    if (this.router.config.modules?.ai?.enabled) {
      try {
        const aiModule = await AIModule.create();
        this.router.registerModule('ai', aiModule);
        this.logger.info('AI module loaded');
      } catch (error) {
        this.logger.error('Failed to load AI module:', error);
      }
    }
  }

  async cleanup() {
    if (!this.server) return;
    
    try {
      // Get current port before closing
      const port = this.server.address()?.port;
      
      // Double close strategy with timeout
      await Promise.race([
        new Promise((resolve) => {
          // First close attempt
          this.server.close(() => {
            this.logger.info('Server stopped gracefully');
            resolve();
          });
          
          // Second close attempt after delay
          setTimeout(() => {
            this.server.close(() => {
              this.logger.warn('Secondary close completed');
              resolve();
            });
          }, 1000);
        }),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Server close timeout')), 2000)
        )
      ]);
      
    } catch (err) {
      this.logger.error('Cleanup error:', err.message);
    } finally {
      // Force kill any remaining process on the port
      const port = this.server.address()?.port;
      if (port) {
        this.killProcessOnPort(port);
      }
      
      // Nullify server reference
      this.server = null;
      this.logger.debug('Server reference cleared');
    }
  }

  async initialize() {
    // Load configuration
    try {
      await this.router.loadConfig();
      if (!this.router.config || !this.router.config.server) {
        throw new Error('Configuration invalide ou incomplète');
      }
      // Debug log
      this.logger.debug('Configuration loaded:', {
        server: this.router.config.server,
        modules: Object.keys(this.router.config.modules)
      });
    } catch (error) {
      this.logger.error('Échec du chargement de la configuration:', error);
      throw error;
    }
    
    // Reconfigure logger with final config
    if (this.router.config?.logging) {
      configureFileTransport(this.logger, {
        path: this.router.config.logging.file.path,
        enabled: this.router.config.logging.file.enabled,
        level: this.router.config.logging.level
      });
    }
    
    // Load modules
    await this.loadModules();

    // Setup Express middleware
    this.app.use(express.json());
    this.app.use((req, res, next) => {
      this.logger.debug(`Incoming request: ${req.method} ${req.path}`);
      res.header('Access-Control-Allow-Origin', 'http://localhost:57777');
      res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      next();
    });

    // Metrics endpoints (public)
    this.app.get('/metrics', metricsController.getMetrics.bind(metricsController));
    this.app.post('/metrics/archive', metricsController.triggerArchive.bind(metricsController));

    // Register routes
    this.app.post('/core/:module', async (req, res) => {
      try {
        const { module } = req.params;
        const result = await this.router.routeMessage(module, req.body);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // GDevelop endpoint
    this.app.post('/core/gdevelop', async (req, res) => {
      try {
        const result = await this.router.routeMessage('gdevelop', req.body);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Chat endpoint
    this.app.post('/core/chat', async (req, res) => {
      try {
        const result = await this.router.routeMessage('chat', req.body);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Legacy route for backward compatibility
    this.app.post('/api/chat', async (req, res) => {
      try {
        const result = await this.router.routeMessage('chat', req.body);
        res.json(result);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Add root route
    this.app.get('/', (req, res) => {
      res.send(`
        <!DOCTYPE html>
        <html lang="fr">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>GDevAI Server</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            h1 { color: #2c3e50; }
            ul { padding-left: 20px; }
            li { margin-bottom: 8px; }
            a { color: #3498db; text-decoration: none; }
            a:hover { text-decoration: underline; }
            .container { max-width: 800px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>GDevAI Core Server</h1>
            <p>Bienvenue sur le serveur GDevAI. Voici les endpoints disponibles :</p>
            <ul>
              <li><a href="/test-chat.html">Page de test du chat</a></li>
              <li><a href="/core/test">Endpoint de test</a> (génère un token JWT)</li>
              <li>POST <a href="/core/chat">/core/chat</a> - Endpoint principal de chat</li>
              <li>POST /api/chat - Endpoint legacy de chat</li>
              <li>GET <a href="/ai/metrics">/ai/metrics</a> - Métriques du dashboard</li>
            </ul>
          </div>
        </body>
        </html>
      `);
    });

    // Public test endpoint
    this.app.get('/core/test', (req, res) => {
      if (!req.headers.authorization) {
        const exampleToken = generateToken('test-user', ['tester'], ['read']);
        return res.json({ 
          status: 'unauthenticated', 
          message: 'No auth token provided',
          example_token: exampleToken,
          instructions: 'Use this in Authorization header as "Bearer [token]"'
        });
      }
      res.json({ 
        status: 'ok', 
        message: 'Test endpoint working',
        user: req.user 
      });
    });

    // Apply auth middleware to all /core routes except /core/test
    this.app.use('/core', (req, res, next) => {
      if (req.path === '/test') {
        return next();
      }
      authMiddleware(req, res, next);
    });

    // Temporary test token endpoint (remove in production)
    this.app.post('/test-token', (req, res) => {
      this.logger.debug('Entering /test-token endpoint');
      const testPayload = { 
        userId: 'test-user',
        role: 'tester'
      };
      try {
        this.logger.debug('Attempting token generation with payload:', testPayload);
        const token = generateToken(testPayload);
        this.logger.debug('Token generated successfully');
        res.json({ token });
      } catch (err) {
        this.logger.error('Token generation failed:', err.stack);
        res.status(500).json({ 
          error: 'Token generation failed',
          details: err.message
        });
      }
    });

    // Dashboard metrics endpoint
    this.app.get('/ai/metrics', async (req, res) => {
      try {
        const metrics = await getAIStats();
        res.json(metrics);
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Log final de statut
    this.app.use((req, res, next) => {
      res.on('finish', () => {
        if (res.statusCode === 200) {
          this.logger.info('Authentification OK');
        } else if (res.statusCode === 401) {
          this.logger.info('Authentification ÉCHEC - Token invalide');
        } else if (res.statusCode >= 400) {
          this.logger.info('Erreur de requête - Code: ' + res.statusCode);
        }
      });
      next();
    });

    // Start server with auto port allocation
    return new Promise((resolve) => {
      this.server = this.app.listen(0, () => {
        const port = this.server.address().port;
        this.logger.info(`Core server running on auto-assigned port ${port}`);
        this.logger.info(`Test endpoint available at: http://localhost:${port}/core/test`);
        this.logger.info(`Chat endpoint available at: http://localhost:${port}/core/chat`);
        resolve(this.server);
      });
    });
  }
}

// Start the core
new Core().initialize();

export default Core;
