# Guide Complet des Tests avec Vitest

## Tests d'intégration Core
Les tests vérifient le bon fonctionnement du serveur principal en utilisant l'auto-allocation de ports (port 0) pour éviter les conflits. 

### Points clés :
- Utilisation systématique de l'auto-allocation de ports
- Nettoyage automatique des ressources après chaque test
- Timeout réduit à 10s pour des tests rapides
- Isolation complète entre les tests

### Exemple de test :
```javascript
test('should initialize without errors', async () => {
  await expect(core.initialize()).resolves.not.toThrow();
  
  // Vérifier que le serveur est démarré
  expect(core.server).toBeDefined();
  const port = core.server.address().port;
  expect(port).toBeGreaterThan(0);
  
  // Nettoyage
  await core.cleanup();
}, 10000);
```

[Le reste du contenu original...]
