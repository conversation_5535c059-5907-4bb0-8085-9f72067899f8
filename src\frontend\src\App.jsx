import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import './App.css';
import Chat from './pages/Chat';
import Home from './pages/Home';
import GDevelopPanel from './components/features/GDevelop';

function App() {
  return (
    <Router>
      <div className="app-container">
        <nav className="main-nav">
          <Link to="/" className="nav-link">Accueil</Link>
          <Link to="/chat" className="nav-link">Chat</Link>
          <Link to="/gdevelop" className="nav-link">GDevelop</Link>
        </nav>

        <main className="main-content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/chat" element={<Chat />} />
            <Route path="/gdevelop" element={<GDevelopPanel />} />
          </Routes>
        </main>
      </div>
    </Router>
  );
}

export default App
