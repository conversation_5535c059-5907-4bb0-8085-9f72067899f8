import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const backupDir = path.join(__dirname, '..', 'backups');
const testDir = path.join(__dirname, '..', 'test_restore');

// 1. Sélectionner la dernière sauvegarde
const latestBackup = fs.readdirSync(backupDir)
  .filter(f => f.startsWith('backup_') && f.endsWith('.zip'))
  .sort()
  .pop();

if (!latestBackup) {
  console.error('❌ Aucune sauvegarde trouvée');
  process.exit(1);
}

console.log(`🔍 Test de restauration avec: ${latestBackup}`);

// 2. Créer un environnement de test
if (fs.existsSync(testDir)) {
  fs.rmSync(testDir, { recursive: true });
}
fs.mkdirSync(testDir);

// 3. Extraire la sauvegarde
try {
  execSync(`powershell Expand-Archive -Path "${path.join(backupDir, latestBackup)}" -DestinationPath "${testDir}"`);
  console.log('✅ Extraction réussie');
  
  // 4. Vérifier la structure de base
  const projectDir = path.join(testDir, 'GDevAI');
  const requiredFiles = ['package.json', 'src', 'config'];
  const missing = requiredFiles.filter(f => !fs.existsSync(path.join(projectDir, f)));
  
  if (missing.length > 0) {
    console.error(`❌ Fichiers manquants: ${missing.join(', ')}`);
    process.exit(1);
  }
  
  console.log('✅ Structure de fichiers validée');
  console.log('\n🔥 Test de restauration réussi');
  process.exit(0);
} catch (error) {
  console.error('❌ Erreur lors de la restauration:', error.message);
  process.exit(1);
}
