import PortManagerCore from './core.js';
import WindowsAdapter from './adapters/windows.js';
import LinuxAdapter from './adapters/linux.js';

const defaultAdapters = {
  windows: WindowsAdapter,
  linux: LinuxAdapter
};

export function createPortManager(config = {}) {
  const instance = new PortManagerCore();
  
  // Register default adapters based on platform
  const platform = process.platform;
  if (defaultAdapters[platform]) {
    instance.registerAdapter(platform, new defaultAdapters[platform]());
  } else {
    throw new Error(`Unsupported platform: ${platform}`);
  }

  return instance;
}

export default createPortManager();
