# Guide d'Installation GDevAI

## 1. Prérequis Système
- Node.js v20+
- npm v9+
- Git
- Ports disponibles : 3000 (frontend), 3007 (backend), 7000 (MCP)

## 2. Installation Initiale

### 2.1 Clonage du Dépôt
```bash
git clone https://github.com/votre-org/GDevAI.git
cd GDevAI
```

### 2.2 Installation des Dépendances
```bash
npm run setup-local
```

## 3. Configuration

### 3.1 Configuration MCP
Créer/modifier `mcp-server/.env` :
```ini
MCP_PORT=7000
MODULES_DIR=./mcp-modules
LOG_LEVEL=debug
```

### 3.2 Fichier .env
Créer un fichier `.env` à la racine :
```ini
NODE_ENV=development
PORT=3007
MCP_PORT=7000
JWT_SECRET=votre_secret_secure
```

### 3.2 Configuration RBAC
Modifier `config/roles.yaml` pour définir les permissions.

## 4. Lancement des Services

### 4.1 Tous les services
```bash
npm start
```

### 4.2 Individuellement
```bash
npm run frontend  # Port 3000
npm run backend   # Port 3007
npm run mcp       # Port 7000
```

## 5. Vérification

### 5.1 Tests
```bash
npm test
```

### 5.2 Vérification des Services
```bash
curl http://localhost:3000/health
curl http://localhost:3007/health
curl http://localhost:7000/health-check
```

## 6. Déploiement

### 6.1 Options
- **Local** : `npm start`
- **Docker** : `docker-compose up --build`
- **Cloud** : Voir fichier `DEPLOYMENT.md`

### 6.2 CI/CD
Workflow GitHub Actions pré-configuré :
- Tests automatiques sur chaque push
- Déploiement staging automatique
- Déploiement production après validation

## 7. Dépannage

### 7.1 Problèmes Courants
- **Ports occupés** : `npm run kill-ports`
- **Erreurs RBAC** : `npm run validate-rbac`
- **Problèmes de dépendances** : `npm ci`

### 7.2 Logs
```bash
# Frontend
tail -f logs/frontend.log

# Backend  
tail -f logs/backend.log

# MCP
tail -f mcp-server/logs/server.log
```

## 8. Mise à Jour
```bash
git pull origin main
npm install
npm run build
```

## 9. Gestion des Dépendances

### 9.1 Vérification des Dépendances Inutilisées
```bash
npm install -g depcheck
depcheck
```

### 9.2 Nettoyage Sécurisé
```bash
npm prune
```

### 9.3 Mise à Jour Sélective
```bash
npm outdated
npm update [package] --save-exact
```

### 9.4 Bonnes Pratiques
- Toujours vérifier les dépendances avant suppression
- Conserver une version exacte (--save-exact) pour les packages critiques
- Tester systématiquement après modifications
