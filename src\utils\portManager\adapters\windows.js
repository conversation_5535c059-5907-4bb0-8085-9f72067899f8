import { execSync } from 'child_process';
import logger from '../../logger.js';

export default class WindowsAdapter {
  constructor() {
    this.logger = logger.child({ adapter: 'windows' });
  }

  async findFreePort() {
    try {
      // Implémentation spécifique à Windows
      const port = await this.scanAvailablePort();
      this.logger.debug(`Port disponible trouvé: ${port}`);
      return port;
    } catch (error) {
      this.logger.error(`Erreur recherche port: ${error.message}`);
      throw error;
    }
  }

  async killProcessOnPort(port) {
    try {
      const pid = this.getPidFromPort(port);
      if (pid) {
        execSync(`taskkill /PID ${pid} /F`);
        this.logger.info(`Process ${pid} tué sur port ${port}`);
      }
    } catch (error) {
      this.logger.warn(`Échec kill process port ${port}: ${error.message}`);
    }
  }

  getPidFromPort(port) {
    try {
      const result = execSync(`netstat -ano | findstr :${port}`).toString();
      const match = result.match(/\s+(\d+)\s*$/);
      return match ? match[1] : null;
    } catch {
      return null;
    }
  }
}
