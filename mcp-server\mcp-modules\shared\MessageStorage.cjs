class MessageStorage {
    constructor() {
        this.messages = [];
        this.maxHistory = 100;
    }

    addMessage(sender, text) {
        const message = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            sender,
            text
        };
        this.messages.push(message);
        
        if (this.messages.length > this.maxHistory) {
            this.messages.shift();
        }
        
        return message;
    }

    getHistory(limit = 20) {
        return this.messages.slice(-limit);
    }

    removeOldestMessages(count) {
        if (!this.messages || !Array.isArray(this.messages)) return;
        this.messages = this.messages.slice(count);
    }

    removeMessage(id) {
        this.messages = this.messages.filter(msg => msg.id !== id);
    }

    clear() {
        this.messages = [];
    }
}

module.exports = MessageStorage;
