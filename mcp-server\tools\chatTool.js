import ChatService from '../services/chatService.js';

export default (router) => {
    const chatService = new ChatService();
    
    // Route test
    router.get('/test', (req, res) => res.json({status: 'OK'}));

    // Route chat
    router.post('/chat', async (req, res) => {
        try {
            const { message } = req.body;
            const response = await chatService.processMessage(message);
            res.json({ 
                text: response,
                sender: 'assistant'
            });
        } catch (error) {
            res.status(500).json({ error: error.message });
        }
    });

    console.log('Outil Chat chargé');
};
