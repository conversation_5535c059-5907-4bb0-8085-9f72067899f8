<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Endpoint Chat</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #response { margin-top: 20px; padding: 10px; border: 1px solid #ddd; }
    </style>
</head>
<body>
    <h1>Tester le endpoint /core/chat</h1>
    
    <div>
        <button id="getTokenBtn">Obtenir un token de test</button>
        <div id="tokenInfo" style="margin: 10px 0; display: none;">
            <p>Token copié dans le presse-papiers</p>
        </div>
    </div>

    <form id="chatForm">
        <div>
            <label for="message">Message:</label><br>
            <textarea id="message" rows="4" cols="50" placeholder="Votre message..."></textarea>
        </div>
        <button type="submit">Envoyer</button>
    </form>

    <div id="response"></div>

    <script>
        // Obtenir un token de test
        document.getElementById('getTokenBtn').addEventListener('click', async () => {
            const response = await fetch('/core/test');
            const data = await response.json();
            navigator.clipboard.writeText(data.example_token);
            
            const tokenInfo = document.getElementById('tokenInfo');
            tokenInfo.style.display = 'block';
            setTimeout(() => tokenInfo.style.display = 'none', 3000);
        });

        // Envoyer une requête au endpoint chat
        document.getElementById('chatForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const message = document.getElementById('message').value;
            const responseDiv = document.getElementById('response');
            responseDiv.innerHTML = 'Envoi en cours...';

            try {
                const tokenResponse = await fetch('/core/test');
                const tokenData = await tokenResponse.json();
                
                const response = await fetch('/core/chat', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${tokenData.example_token}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ message })
                });
                
                const result = await response.json();
                responseDiv.innerHTML = `<pre>${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                responseDiv.innerHTML = `Erreur: ${error.message}`;
            }
        });
    </script>
</body>
</html>
