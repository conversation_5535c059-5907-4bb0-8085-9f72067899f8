import { useEffect, useState } from 'react';
import api from './api';

export function useMetrics() {
  const [metrics, setMetrics] = useState({
    responseTimes: { avg: 0, trend: 'neutral' },
    errorRates: { percentage: 0, trend: 'neutral' },
    tokenUsage: { total: 0, trend: 'neutral' }
  });

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await api.get('/metrics');
        setMetrics(response.data);
      } catch (error) {
        console.error('Error fetching metrics:', error);
      }
    };

    fetchMetrics();
    const interval = setInterval(fetchMetrics, 30000);
    
    return () => clearInterval(interval);
  }, []);

  return metrics;
}
