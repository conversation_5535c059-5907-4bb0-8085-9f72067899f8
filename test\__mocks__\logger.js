/**
 * Mock standardisé pour le logger
 * À utiliser dans tous les tests
 */
import { vi } from 'vitest';

// Créer un mock de base pour le logger
const createLoggerMock = () => ({
  info: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
  child: vi.fn().mockImplementation(() => createLoggerMock())
});

// Instance principale du logger
const logger = createLoggerMock();

// Fonction pour réinitialiser les mocks
export const resetLoggerMocks = () => {
  logger.info.mockClear();
  logger.error.mockClear();
  logger.warn.mockClear();
  logger.debug.mockClear();
  logger.child.mockClear();
};

// Fonction pour créer le logger
export const createLogger = vi.fn().mockImplementation(() => logger);

// Export par défaut pour être compatible avec les imports de type: import logger from '#logger'
const defaultExport = logger;
defaultExport.createLogger = createLogger;
defaultExport.resetLoggerMocks = resetLoggerMocks;
defaultExport.logger = logger;

export default defaultExport;

// Export nommé pour être compatible avec les imports de type: import { logger } from '#logger'
export const loggerInstance = logger;
