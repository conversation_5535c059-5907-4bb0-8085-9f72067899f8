import { describe, it, expect, vi, beforeEach, afterEach, afterAll } from 'vitest';
import { __setPortOccupied, __clearAllPorts } from '../test/__mocks__/net';
import { __setProcessOnPort, __clearAllProcesses } from '../test/__mocks__/child_process';
import universalLogger from './__mocks__/universal-logger.js';
import { createPortManager } from './__mocks__/portManager.js';

// Mock du logger
vi.mock('#logger', () => ({
  default: universalLogger,
  logger: universalLogger,
  createLogger: vi.fn(() => universalLogger)
}));

// Mock des modules natifs
vi.mock('net', () => import('../test/__mocks__/net').then(mod => mod.default));
vi.mock('child_process', () => import('../test/__mocks__/child_process').then(mod => mod.default));

// Mock du module portManager
vi.mock('../src/utils/portManager/index.js', () => ({
  createPortManager,
  default: createPortManager()
}));

describe('PortManager', () => {
  let portManager;

  beforeEach(() => {
    // Réinitialiser les mocks
    __clearAllPorts();
    __clearAllProcesses();

    // Créer une nouvelle instance du gestionnaire de ports
    portManager = createPortManager();
  });

  afterEach(() => {
    // Nettoyer après chaque test
    __clearAllPorts();
    __clearAllProcesses();
  });

  afterAll(() => {
    // Nettoyer après tous les tests
    vi.restoreAllMocks();
  });

  it('should initialize with platform adapter', () => {
    expect(portManager).toHaveProperty('adapters');
    expect(portManager.adapters.size).toBeGreaterThan(0);
  });

  it('should find free port', async () => {
    const port = await portManager.findFreePort();
    expect(port).toBeGreaterThan(0);
    expect(port).toBeLessThan(65535);
  });

  it('should handle port killing', async () => {
    const mockPort = 3000;
    const mockPid = 12345;

    // Simuler un processus sur le port
    __setProcessOnPort(mockPort, mockPid);

    // Tenter de tuer le processus
    await expect(portManager.killProcessOnPort(mockPort))
      .resolves.not.toThrow();
  });

  it('should detect occupied port', async () => {
    const mockPort = 4000;

    // Simuler un port occupé
    __setPortOccupied(mockPort);

    // Vérifier que le port est détecté comme occupé
    const adapter = Array.from(portManager.adapters.values())[0];
    const isOccupied = await adapter.isPortOccupied(mockPort);

    expect(isOccupied).toBe(true);
  });

  it('should detect free port', async () => {
    const mockPort = 5000;

    // S'assurer que le port est libre
    __clearAllPorts();

    // Vérifier que le port est détecté comme libre
    const adapter = Array.from(portManager.adapters.values())[0];
    const isOccupied = await adapter.isPortOccupied(mockPort);

    expect(isOccupied).toBe(false);
  });
});
