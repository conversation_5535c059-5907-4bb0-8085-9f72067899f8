import OpenAI from 'openai';
import dotenv from 'dotenv';
dotenv.config({ path: 'd:/Test/GDevAI/mcp-server/.env' });

// Logger temporaire pour les tests
const logger = {
  info: console.log,
  error: console.error,
  debug: console.debug,
  warn: console.warn
};

class DeepseekService {
  constructor() {
    this.logger = logger;
    this.chatClient = new OpenAI({
      baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1',
      apiKey: process.env.CHAT_API_KEY
    });
    this.agentClient = new OpenAI({
      baseURL: process.env.DEEPSEEK_BASE_URL || 'https://api.deepseek.com/v1', 
      apiKey: process.env.DEEPSEEK_API_KEY
    });
    this.model = process.env.DEEPSEEK_MODEL || 'deepseek-chat';
  }

  async chatCompletion(messages, options = {}, isAgentRequest = false) {
    if (!Array.isArray(messages)) {
      throw new Error('Invalid message format: expected an array of messages');
    }
    if (messages.some(m => !m.role || !m.content)) {
      throw new Error('Each message must have role and content properties');
    }
    
    try {
      const client = isAgentRequest ? this.agentClient : this.chatClient;
      const response = await client.chat.completions.create({
        model: this.model,
        messages,
        max_tokens: options.maxTokens || 1000,
        temperature: options.temperature || 0.7
      });
      return response.choices[0].message.content;
    } catch (error) {
      this.logger.error('Deepseek API Error:', error);
      throw error;
    }
  }

  async analyzeCode(code, language) {
    return this.chatCompletion([
      {
        role: 'system',
        content: `You are a code analyzer specialized in ${language}. Provide detailed analysis and suggestions.`
      },
      {
        role: 'user',
        content: code
      }
    ], {}, true);
  }
}

export default DeepseekService;
