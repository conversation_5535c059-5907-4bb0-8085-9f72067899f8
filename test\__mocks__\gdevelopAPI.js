class GDevelopAPIMock {
  static getProject = vi.fn((id) => Promise.resolve({
    id,
    name: `Mock project ${id}`,
    version: '1.0.0'
  }));
  
  static updateProject = vi.fn(() => Promise.resolve(true));
  
  static getEvents = vi.fn(() => Promise.resolve([]));
  
  static syncResources = vi.fn(() => Promise.resolve({
    success: true,
    count: 0
  }));
  
  static __forceError = vi.fn(() => {
    throw new Error('Mock error');
  });
}

export default GDevelopAPIMock;
