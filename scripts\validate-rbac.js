#!/usr/bin/env node
import { validateRBAC } from '../src/utils/rbac.js';
import { readFileSync } from 'fs';
import path from 'path';

const rolesPath = path.join(process.cwd(), 'config', 'roles.yaml');
const rolesConfig = readFileSync(rolesPath, 'utf8');

try {
  await validateRBAC(rolesConfig);
  console.log('✅ RBAC validation successful');
  process.exit(0);
} catch (error) {
  console.error('❌ RBAC validation failed:', error.message);
  process.exit(1);
}
