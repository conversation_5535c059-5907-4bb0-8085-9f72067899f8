# Documentation des Endpoints API

## Gestion des Ports - MCPServer
`http://localhost:7000`

### Classe MCPServer
Gestion centralisée des ports et du serveur

**Méthodes principales**:
- `start()`: D<PERSON>marre le serveur sur un port disponible
- `checkPort(port)`: Vérifie si un port est disponible
- `forceKill(port)`: Libère un port occupé
- `savePort(port)`: Sauvegarde le port utilisé

**Gestion des ports**:
- Vérification automatique des ports 7000-7010
- Fallback sur d'autres ports si nécessaire
- Validation stricte des numéros de port

**Exemple d'utilisation**:
```javascript
import MCPServer from '../mcp-server/server.js';

const server = new MCPServer();
server.start().catch(err => {
  console.error('Erreur démarrage serveur:', err);
});
```

**Codes d'erreur**:
- `EADDRINUSE`: Port déjà utilisé
- `EINVAL`: Numéro de port invalide
- `ENOPORTS`: Aucun port disponible

## Module Chat
`http://localhost:7000/mcp/module/chat`

### GET /status
Vérifie le statut du module

### POST /send
Envoie un nouveau message

### GET /history
Récupère l'historique des messages

## Module Backup
`http://localhost:7000/mcp/module/backup`

### POST /run
Lance une sauvegarde manuelle

**Paramètres**:
- force (bool): Ignorer la vérification des 24h

**Exemple**:
```bash
curl -X POST http://localhost:7000/mcp/module/backup/run \
  -H "Content-Type: application/json" \
  -d '{"force":false}'
```

### GET /status
Vérifie l'état des sauvegardes

**Réponse**:
```json
{
  "lastBackup": "2025-05-12T02:00:00.000Z",
  "nextBackup": "2025-05-13T02:00:00.000Z", 
  "storageUsed": "450MB"
}
```

### GET /logs
Récupère les logs de sauvegarde

**Paramètres**:
- limit (number): Nombre max de logs à retourner
