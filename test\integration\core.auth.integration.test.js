import Core from '../../core';
import Router from '../../core/router';

vi.mock('../core/config/default.json', () => ({
  default: {
    server: {
      port: 5003,
      portCheck: {
        enabled: true,
        alternatePorts: [5003, 5004, 5005]
      }
    },
    logging: {
      level: 'debug',
      file: { enabled: false }
    },
    modules: {
      chat: { enabled: true },
      gdevelop: { enabled: true },
      ai: { enabled: true }
    }
  }
}), { virtual: true });

describe('Core Integration', () => {
  let core;

  beforeAll(async () => {
    core = new Core();
    core.router = new Router();
    
    // Mock router.loadConfig to use our custom config
    core.router.loadConfig = vi.fn().mockImplementation(async () => {
      core.router.config = {
        server: {
          port: 0, // Auto-assign port
          portCheck: {
            enabled: true,
            alternatePorts: [0] // Only use auto-assigned ports
          },
          processManagement: {
            portFinderCommand: 'netstat -ano | findstr :{port}',
            killCommand: 'taskkill /F /PID {pid}',
            killPrevious: true
          }
        },
        logging: {
          level: 'debug',
          file: { enabled: false }
        },
        modules: {
          chat: { enabled: true },
          gdevelop: { enabled: true },
          ai: { enabled: true }
        }
      };
      return Promise.resolve();
    });

    // Force config load before tests
    await core.router.loadConfig();
  });

  test('should initialize without errors', async () => {
    await expect(core.initialize()).resolves.not.toThrow();
    
    // Verify server is running
    expect(core.server).toBeDefined();
    const port = core.server.address().port;
    expect(port).toBeGreaterThan(0);
    
    // Cleanup
    await core.cleanup();
  }, 10000); // Reduced timeout since we're using auto-assigned ports
});
