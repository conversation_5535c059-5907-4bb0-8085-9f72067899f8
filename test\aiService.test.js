import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import AIService from '../src/backend/services/aiService.js';
import { createTestContext } from './utils/testContext.js';

describe('AIService', () => {
  let aiService;
  let testContext;
  let logger;

  beforeEach(async () => {
    // Créer un contexte de test isolé
    testContext = createTestContext();

    // Configurer le logger mock
    logger = testContext.mockLogger();

    // Réinitialiser tous les mocks
    vi.clearAllMocks();

    // Créer et initialiser le service
    aiService = new AIService({ logger });
    await aiService.initialize(); // Initialisation explicite
  });

  afterEach(() => {
    // Nettoyer le contexte de test
    testContext.cleanup();
  });

  describe('generateText', () => {
    it('should log the prompt and return generated text', async () => {
      const result = await aiService.generateText('Test prompt', { key: 'value' });

      expect(logger.info).toHaveBeenCalledWith('Génération de texte - Prompt: Test prompt');
      expect(result).toMatchObject({
        text: expect.stringContaining('Test prompt'),
        context: {
          key: 'value',
          timestamp: expect.any(String)
        },
        isAI: true
      });
    });

    it('should throw error for invalid prompt', async () => {
      await expect(aiService.generateText('', {}))
        .rejects.toThrow('Prompt invalide');
      expect(logger.error).toHaveBeenCalled();
    });
  });

  describe('analyzeCode', () => {
    it('should log the language and return analysis', async () => {
      const result = await aiService.analyzeCode('const x = 5;', 'javascript');

      expect(logger.info).toHaveBeenCalledWith('Analyse de code - Langage: javascript');
      expect(result).toEqual({
        analysis: "Analyse du code javascript",
        suggestions: [
          "Utiliser des noms de variables plus descriptifs",
          "Vérifier les cas limites"
        ],
        stats: {
          lines: 1,
          complexity: 'medium'
        }
      });
    });

    it('should validate GDevelop event sheets', async () => {
      const analysis = await aiService.analyzeCode('{ "events": [...] }', 'gdevelop');
      expect(analysis.suggestions).toBeInstanceOf(Array);
      expect(logger.info).toHaveBeenCalledWith('Analyse de code - Langage: gdevelop');
    });
  });
});
