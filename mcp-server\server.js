import path from 'path';
import { fileURLToPath } from 'url';
import { createServer } from 'net';
import fs from 'fs';
import { exec } from 'child_process';
import express from 'express';
import dotenv from 'dotenv';
import config from './config/config.js';
import { createRequire } from 'module';
const require = createRequire(import.meta.url);

const ModuleLoader = (await import('./moduleLoader.cjs')).default;

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

dotenv.config({ path: path.join(__dirname, '.env') });

function log(msg) {
  console.log(`[${new Date().toISOString()}] ${msg}`);
}

class MCPServer {
  constructor() {
    this.app = express();
    this.port = null;
    this.portFile = path.join(__dirname, '.current_port');
    this.moduleLoader = new ModuleLoader();
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.static(path.join(__dirname, config.resources.frontend)));
  }

  setupRoutes() {
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, config.resources.frontend, 'index.html'));
    });

    this.app.get('/health-check', (req, res) => {
      res.json({ status: 'OK', timestamp: new Date().toISOString() });
    });

    // Chargement dynamique des modules
    this.moduleLoader.loadModules().then(() => {
      const modules = this.moduleLoader.getAllModules();
      modules.forEach(module => {
        this.app.use(`/mcp/module/${module.name.toLowerCase()}`, module.routes);
        log(`Route configurée: /mcp/module/${module.name.toLowerCase()}`);
      });
    });
  }

  async start() {
    this.port = await this.loadOrFindPort();
    const server = this.app.listen(this.port, () => {
      log(`🚀 Serveur démarré sur le port ${this.port}`);
    });

    server.on('error', async err => {
      if (err.code === 'EADDRINUSE') {
        log(`❌ Port ${this.port} déjà utilisé, tentative avec le port suivant...`);
        this.port++;
        await this.savePort(this.port);
        this.start();
      } else {
        log(`❌ Erreur serveur: ${err.message}`);
        process.exit(1);
      }
    });
  }

  // Méthodes existantes pour la gestion des ports...
  async loadOrFindPort() {
    // Priorité au port configuré
    if (config.server.port) {
      await this.forceKill(config.server.port);
      return config.server.port;
    }
    
    try {
      const savedPort = parseInt(await fs.promises.readFile(this.portFile, 'utf-8'));
      log(`⏮️ Port précédent détecté: ${savedPort}`);
      await this.forceKill(savedPort);
      return savedPort;
    } catch {
      log('🔎 Aucun port sauvegardé, recherche en cours...');
      for (let port = 7000; port <= 7010; port++) {
        const used = await this.checkPort(port);
        if (!used) {
          await this.savePort(port);
          return port;
        }
      }
      throw new Error('Aucun port disponible');
    }
  }

  async checkPort(port) {
    // Validation du port
    if (isNaN(port) || port < 0 || port > 65535) {
      throw new Error(`Port invalide: ${port}`);
    }

    return new Promise((resolve, reject) => {
      let tester;
      try {
        tester = createServer()
          .once('error', () => {
            tester?.close();
            resolve(true);
          })
          .once('listening', () => {
            tester?.close();
            resolve(false);
          })
          .listen(port);
      } catch (err) {
        reject(err);
      }
    });
  }

  async forceKill(port) {
    log(`[FORCE KILL] Tentative libération du port ${port}`);
    return this.checkPort(port).then(portUsed => {
      if (!portUsed) {
        log(`[FORCE KILL] Port ${port} non utilisé, aucune action nécessaire`);
        return Promise.resolve();
      }
      
      const command = `Get-NetTCPConnection -LocalPort ${port} | Select-Object -ExpandProperty OwningProcess | ForEach-Object { Stop-Process -Id $_ -Force }`;
      return new Promise(resolve => {
        exec(`powershell -Command "${command}"`, (err, stdout, stderr) => {
          if (err && !err.message.includes('Aucun objet MSFT_NetTCPConnection')) {
            log(`[FORCE KILL] Erreur PowerShell: ${err.message}`);
          }
          if (stderr && !stderr.includes('Aucun objet MSFT_NetTCPConnection')) {
            log(`[FORCE KILL] STDERR: ${stderr}`);
          }
          log(`[FORCE KILL] Résultat: ${stdout.trim() || 'Aucun processus trouvé'}`);
          setTimeout(resolve, 500);
        });
      });
    });
  }

  async savePort(port) {
    await fs.promises.writeFile(this.portFile, port.toString());
  }
}

const server = new MCPServer();
server.start();

export default MCPServer;
