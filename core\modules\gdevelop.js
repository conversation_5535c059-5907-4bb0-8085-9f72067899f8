import { validateProject, sanitizeInput, formatGDevelopError } from '../../integrations/gdevelop/utils.js';
import GDevelopAPI from '../../integrations/gdevelop/api.js';

class GDevelopModule {
  constructor(logger) {
    this.logger = logger;
    this.gdevelopAPI = new GDevelopAPI();
    this.api = {
      getProject: (id) => this.gdevelopAPI.getProject(id),
      updateProject: (id, data) => this.gdevelopAPI.updateProject(id, data),
      getEvents: (id) => this.gdevelopAPI.getEvents(id),
      getResources: (id) => this.gdevelopAPI.getResources(id)
    };
    this.projects = new Map();
    this.events = new Map();
    this.resources = new Map();
    this.changeHistory = new Map();
  }

  async process(message) {
    try {
      message = sanitizeInput(message);
      if (!message.projectId || !message.action) {
        throw new Error('Requête GDevelop invalide - projectId et action requis');
      }

      switch(message.action) {
        case 'getProject':
          return await this.handleGetProject(message);
        case 'updateProject':
          return await this.handleUpdateProject(message);
        case 'getEvents':
          return await this.handleGetEvents(message);
        case 'syncResources':
          return await this.handleSyncResources(message);
        case 'getChanges':
          return await this.handleGetChanges(message);
        default:
          throw new Error(`Action non supportée: ${message.action}`);
      }
    } catch (error) {
      if (this.logger) {
        this.logger.error('Erreur module GDevelop:', error);
      } else {
        console.error('Erreur module GDevelop (logger non initialisé):', error);
      }
      throw formatGDevelopError(error);
    }
  }

  async handleGetProject(message) {
    const project = await this.api.getProject(message.projectId);
    this.projects.set(message.projectId, project);
    return { 
      status: 'success',
      project 
    };
  }

  async handleUpdateProject(message) {
    if (!validateProject(message.data)) {
      throw new Error('Données de projet invalides');
    }
    
    const updated = await this.api.updateProject(message.projectId, message.data);

    const history = this.changeHistory.get(message.projectId) || [];
    history.push({
      timestamp: new Date().toISOString(),
      changes: message.data
    });
    this.changeHistory.set(message.projectId, history);
    
    return {
      status: 'success',
      updatedAt: new Date().toISOString()
    };
  }

  async handleGetEvents(message) {
    const events = await this.api.getEvents(message.projectId);
    this.events.set(message.projectId, events);
    return {
      status: 'success',
      events
    };
  }

  async handleSyncResources(message) {
    const resources = await this.api.getResources(message.projectId);
    this.resources.set(message.projectId, resources);
    return {
      status: 'success',
      resources,
      syncedAt: new Date().toISOString()
    };
  }

  async handleGetChanges(message) {
    const changes = this.changeHistory.get(message.projectId) || [];
    return {
      status: 'success',
      changes
    };
  }
}

export default GDevelopModule;
