import React, { useState, useEffect } from 'react';
import { Line, Bar } from 'react-chartjs-2';
import { Chart, registerables } from 'chart.js';
import api from '../../../services/api';
import './Dashboard.css';

Chart.register(...registerables);

const Dashboard = () => {
  const [metrics, setMetrics] = useState({
    responseTimes: [],
    successRates: [],
    tokenUsage: []
  });

  useEffect(() => {
    const fetchMetrics = async () => {
      try {
        const response = await api.get('/ai/metrics');
        setMetrics(response.data);
      } catch (error) {
        console.error('Error fetching metrics:', error);
      }
    };

    fetchMetrics();
    const interval = setInterval(fetchMetrics, 30000);
    return () => clearInterval(interval);
  }, []);

  const responseTimeData = {
    labels: metrics.responseTimes.map((_, i) => i+1),
    datasets: [{
      label: 'Temps de réponse (ms)',
      data: metrics.responseTimes,
      borderColor: 'rgb(75, 192, 192)',
      tension: 0.1
    }]
  };

  const successRateData = {
    labels: ['Deepseek', 'OpenAI'],
    datasets: [{
      label: 'Taux de succès',
      data: metrics.successRates,
      backgroundColor: [
        'rgba(54, 162, 235, 0.5)',
        'rgba(255, 99, 132, 0.5)'
      ]
    }]
  };

  return (
    <div className="dashboard">
      <h2>Dashboard IA</h2>
      <div className="chart-container">
        <Line data={responseTimeData} />
        <Bar data={successRateData} />
      </div>
    </div>
  );
};

export default Dashboard;
