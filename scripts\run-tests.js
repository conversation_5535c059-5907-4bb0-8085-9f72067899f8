/**
 * Script pour exécuter les tests avec des options spécifiques
 */
import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const rootDir = path.resolve(__dirname, '..');

// Fonction pour exécuter un test spécifique
function runTest(testFile, options = {}) {
  const {
    verbose = false,
    bail = true,
    timeout = 10000
  } = options;

  const testPath = path.join(rootDir, 'test', testFile);

  console.log(`\n=== Exécution du test: ${testFile} ===\n`);

  try {
    const command = `node node_modules/vitest/vitest.mjs run ${testPath} ${verbose ? '--reporter=verbose' : ''}`;

    console.log(`Commande: ${command}\n`);

    execSync(command, {
      stdio: 'inherit',
      cwd: rootDir
    });

    console.log(`\n✅ Test réussi: ${testFile}\n`);
    return true;
  } catch (error) {
    console.error(`\n❌ Échec du test: ${testFile}\n`);
    if (error.stdout) console.log(error.stdout.toString());
    if (error.stderr) console.error(error.stderr.toString());
    return false;
  }
}

// Fonction pour exécuter tous les tests qui échouent
function runFailingTests() {
  const failingTests = [
    'gdevelop.test.js',
    'logging.test.js',
    'portManagement.test.js',
    'portManager.test.js',
    'mcp-health.test.js'  // Ajout du test mcp-health.test.js
  ];

  console.log('\n=== Exécution des tests qui échouent ===\n');

  let passedCount = 0;

  for (const testFile of failingTests) {
    const passed = runTest(testFile, { verbose: true });
    if (passed) passedCount++;
  }

  console.log(`\n=== Résumé ===\n`);
  console.log(`Tests réussis: ${passedCount}/${failingTests.length}`);
  console.log(`Tests échoués: ${failingTests.length - passedCount}`);
}

// Fonction pour exécuter un test spécifique
function runSpecificTest(testFile) {
  console.log(`\n=== Exécution du test: ${testFile} ===\n`);
  const passed = runTest(testFile, { verbose: true });
  console.log(`\n=== Résultat ===\n`);
  console.log(`Test ${passed ? 'réussi' : 'échoué'}: ${testFile}`);
}

// Exécuter le test mcp-health.test.js
runSpecificTest('mcp-health.test.js');
