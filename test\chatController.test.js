import { Chat<PERSON>ontroller } from '../src/backend/controllers/chatController.js'
import { createChatControllerMocks } from './mocks/chatControllerMocks.js'

describe('ChatController', () => {
  let mocks
  let chatController

  beforeEach(() => {
    mocks = createChatControllerMocks()
    chatController = new ChatController(mocks.chatService)
  })

  describe('handleMessage', () => {
    it('should return formatted response with message', async () => {
      await chatController.handleMessage(mocks.req, mocks.res)

      expect(mocks.chatService.processMessage).toHaveBeenCalledWith('Test message')

      const receivedResponse = mocks.res.json.mock.calls[0][0]

      // Vérification structurelle
      expect(receivedResponse).toEqual(expect.objectContaining({
        status: 'success',
        data: {
          text: 'Mock response',
          sender: 'assistant'
        },
        metadata: expect.objectContaining({
          interactionId: 'test-interaction-123',
          version: '1.0'
        })
      }))

      // Vérifications spécifiques
      expect(typeof receivedResponse.metadata.timestamp).toBe('number')
      expect(receivedResponse.metadata.timestamp).toBeGreaterThan(0)
    })

    it('should handle errors with proper format', async () => {
      const error = new Error('Test error')
      mocks.chatService.processMessage.mockRejectedValue(error)

      await chatController.handleMessage(mocks.req, mocks.res)

      expect(mocks.res.status).toHaveBeenCalledWith(400)
      // Vérifier la structure de base de la réponse d'erreur
      const errorResponse = mocks.res.json.mock.calls[0][0];

      expect(errorResponse).toMatchObject({
        status: 'error',
        error: {
          code: 'CHAT_PROCESSING_ERROR',
          message: 'Test error'
        },
        metadata: {
          timestamp: expect.any(Number)
        }
      });

      // La propriété details peut être undefined ou une chaîne selon l'environnement
      if (errorResponse.error.details !== undefined) {
        expect(typeof errorResponse.error.details).toBe('string');
      }
    })
  })
})
