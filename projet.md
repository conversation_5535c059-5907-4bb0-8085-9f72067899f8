# GDevAI - Assistant IA pour GDevelop

## Objectif Principal
<PERSON><PERSON>er une interface de chatbox intégrée à GDevelop permettant :
- De conseiller les utilisateurs sur le développement de jeux
- D'exécuter des actions automatisées dans l'éditeur
- D'améliorer le workflow de création via des suggestions intelligentes

## Architecture Technique

### Composants Frontend
1. **Extension GDevelop** :
   - Interface React intégrée
   - Système de chat interactif
   - Visualisation des modifications proposées

2. **Connectivité** :
   - Gestion des clés API utilisateur
   - Sélection du modèle IA (OpenAI, Anthropic, etc.)
   - Cache local pour les requêtes

### Composants Backend
1. **Adaptateur GDevelop** :
   - Analyse de la structure des projets
   - Génération de code GDevelop
   - Système de sandbox pour l'exécution

2. **Couche IA** :
   - Traitement des prompts en contexte
   - Validation des actions proposées
   - Apprentissage des préférences utilisateur

## Roadmap Technique

### Phase 1 : Infrastructure (24/04-30/04)
- [x] Authentification JWT
- [x] RBAC (Terminé 23/04) 
- [x] Logging centralisé (implémenté 23/04)
- [x] Tests unitaires critiques (implémentés 23/04)
- [ ] Monitoring de base

### Phase 2 : Fonctionnel (01/05-14/05)
- [x] Chat sécurisé (Contrôleur créé 23/04)
- [ ] Connecteur GDevelop :
  * Création d'objets
  * Modification d'events
- [ ] Système de feedback

### Phase 3 : Optimisation (15/05-)
- [ ] Performances
- [ ] Documentation avancée
- [ ] Support multi-modèles

## Journal de Progression

### 2025-04-23
- Implémentation RBAC terminée
- Merge dans la branche main  
- Création branche feature/phase2
- Contrôleur chat sécurisé implémenté

### 2025-04-15
- Définition des objectifs principaux
- Validation de l'approche technique
- Création de la documentation initiale

## Prochaines Étapes
1. Cloner le dépôt GDevelop
2. Analyser l'architecture existante
3. Identifier les hooks d'extension
4. Créer un POC minimal
