/**
 * Tests ultra-simplifiés pour GDevelop Module (Unit)
 *
 * Ce fichier contient des tests extrêmement simples pour le module GDevelop unitaire,
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from '../../utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Classe GDevelop Module Unit simplifiée pour les tests
class SimplifiedGDevelopModuleUnit {
  constructor(options = {}) {
    this.logger = options.logger || loggerMock;
    this.name = 'gdevelop';
    this.version = '1.0.0';
    this.projects = new Map();
    this.resources = new Map();
    this.events = new Map();
    this.scenes = new Map();
  }

  // Récupérer un projet
  async getProject(projectId) {
    if (!projectId) {
      throw new Error('Project ID is required');
    }

    this.logger.debug(`Getting project: ${projectId}`);

    // Simuler la récupération d'un projet avec plus de détails
    const project = this.projects.get(projectId) || {
      id: projectId,
      name: `Mock project ${projectId}`,
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      scenes: this.getProjectScenes(projectId),
      objects: this.getProjectObjects(projectId),
      events: this.getProjectEvents(projectId),
      resources: this.getProjectResources(projectId),
      settings: {
        width: 1920,
        height: 1080,
        fps: 60
      }
    };

    this.projects.set(projectId, project);
    return project;
  }

  // Mettre à jour un projet
  async updateProject(projectData) {
    if (!projectData || !projectData.id) {
      throw new Error('Project data with ID is required');
    }

    this.logger.debug(`Updating project: ${projectData.id}`);

    const existingProject = this.projects.get(projectData.id) || {};
    const updatedProject = {
      ...existingProject,
      ...projectData,
      updatedAt: new Date().toISOString()
    };

    this.projects.set(projectData.id, updatedProject);
    return true;
  }

  // Synchroniser les ressources
  async syncResources(projectId = null) {
    this.logger.debug(`Syncing resources for project: ${projectId || 'all'}`);

    const mockResources = [
      { name: 'sprite1.png', type: 'image', size: 1024 },
      { name: 'background.jpg', type: 'image', size: 2048 },
      { name: 'sound1.wav', type: 'audio', size: 512 },
      { name: 'music.mp3', type: 'audio', size: 4096 }
    ];

    if (projectId) {
      this.resources.set(projectId, mockResources);
    }

    return {
      success: true,
      count: mockResources.length,
      resources: mockResources,
      syncedAt: new Date().toISOString()
    };
  }

  // Récupérer les scènes d'un projet
  getProjectScenes(projectId) {
    return [
      { id: 'scene1', name: 'Main Menu', type: 'menu' },
      { id: 'scene2', name: 'Game Level 1', type: 'level' },
      { id: 'scene3', name: 'Game Over', type: 'ui' }
    ];
  }

  // Récupérer les objets d'un projet
  getProjectObjects(projectId) {
    return [
      { id: 'player', name: 'Player Character', type: 'sprite' },
      { id: 'enemy1', name: 'Basic Enemy', type: 'sprite' },
      { id: 'platform', name: 'Platform', type: 'tiled_sprite' },
      { id: 'coin', name: 'Collectible Coin', type: 'sprite' }
    ];
  }

  // Récupérer les événements d'un projet
  getProjectEvents(projectId) {
    return [
      { id: 'start_game', name: 'Start Game', type: 'standard', conditions: [], actions: [] },
      { id: 'player_jump', name: 'Player Jump', type: 'standard', conditions: ['key_pressed'], actions: ['apply_force'] },
      { id: 'collect_coin', name: 'Collect Coin', type: 'collision', conditions: ['collision'], actions: ['delete_object', 'add_score'] }
    ];
  }

  // Récupérer les ressources d'un projet
  getProjectResources(projectId) {
    return this.resources.get(projectId) || [];
  }

  // Créer un nouveau projet
  async createProject(projectData) {
    if (!projectData || !projectData.name) {
      throw new Error('Project name is required');
    }

    const projectId = `project_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newProject = {
      id: projectId,
      name: projectData.name,
      description: projectData.description || '',
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      scenes: [],
      objects: [],
      events: [],
      resources: [],
      settings: {
        width: projectData.width || 1920,
        height: projectData.height || 1080,
        fps: projectData.fps || 60
      }
    };

    this.projects.set(projectId, newProject);
    this.logger.debug(`Project created: ${projectId}`);
    
    return newProject;
  }

  // Supprimer un projet
  async deleteProject(projectId) {
    if (!projectId) {
      throw new Error('Project ID is required');
    }

    this.logger.debug(`Deleting project: ${projectId}`);

    const existed = this.projects.has(projectId);
    this.projects.delete(projectId);
    this.resources.delete(projectId);
    this.events.delete(projectId);
    this.scenes.delete(projectId);

    return {
      success: existed,
      deletedAt: new Date().toISOString()
    };
  }

  // Exporter un projet
  async exportProject(projectId, format = 'json') {
    const project = await this.getProject(projectId);
    
    this.logger.debug(`Exporting project ${projectId} as ${format}`);

    return {
      format,
      data: project,
      exportedAt: new Date().toISOString(),
      size: JSON.stringify(project).length
    };
  }

  // Importer un projet
  async importProject(projectData, options = {}) {
    if (!projectData) {
      throw new Error('Project data is required');
    }

    this.logger.debug('Importing project');

    const importedProject = {
      ...projectData,
      id: options.newId || projectData.id,
      importedAt: new Date().toISOString()
    };

    this.projects.set(importedProject.id, importedProject);
    
    return importedProject;
  }

  // Méthode pour forcer une erreur (pour les tests)
  __forceError(message = 'Mock error') {
    throw new Error(message);
  }

  // Méthode pour réinitialiser l'état (pour les tests)
  __reset() {
    this.projects.clear();
    this.resources.clear();
    this.events.clear();
    this.scenes.clear();
  }

  // Obtenir les statistiques du module
  getStats() {
    return {
      projects: this.projects.size,
      totalResources: Array.from(this.resources.values()).reduce((sum, resources) => sum + resources.length, 0),
      version: this.version
    };
  }
}

console.log('\n=== DÉBUT DES TESTS GDEVELOP MODULE UNIT SIMPLIFIÉS ===\n');

describe('GDevelop Module Unit (Simplifié)', () => {
  let gdevelopModule;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();
    
    // Créer une nouvelle instance
    gdevelopModule = new SimplifiedGDevelopModuleUnit({
      logger: loggerMock
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Gestion des projets', () => {
    it('devrait récupérer un projet avec tous les détails', async () => {
      // Arrange
      const projectId = 'test123';

      // Act
      const project = await gdevelopModule.getProject(projectId);

      // Assert
      expect(project).toMatchObject({
        id: projectId,
        name: `Mock project ${projectId}`,
        version: '1.0.0',
        createdAt: expect.any(String),
        scenes: expect.any(Array),
        objects: expect.any(Array),
        events: expect.any(Array),
        resources: expect.any(Array),
        settings: {
          width: 1920,
          height: 1080,
          fps: 60
        }
      });
      expect(project.scenes).toHaveLength(3);
      expect(project.objects).toHaveLength(4);
      expect(project.events).toHaveLength(3);
      expect(loggerMock.debug).toHaveBeenCalledWith(`Getting project: ${projectId}`);
    });

    it('devrait mettre à jour un projet correctement', async () => {
      // Arrange
      const projectData = {
        id: 'test123',
        name: 'Updated Project',
        description: 'New description'
      };

      // Act
      const result = await gdevelopModule.updateProject(projectData);

      // Assert
      expect(result).toBe(true);
      expect(loggerMock.debug).toHaveBeenCalledWith(`Updating project: ${projectData.id}`);

      // Vérifier que le projet a été mis à jour
      const updatedProject = await gdevelopModule.getProject(projectData.id);
      expect(updatedProject.name).toBe('Updated Project');
      expect(updatedProject.description).toBe('New description');
      expect(updatedProject.updatedAt).toBeDefined();
    });

    it('devrait créer un nouveau projet', async () => {
      // Arrange
      const projectData = {
        name: 'New Game Project',
        description: 'A new exciting game',
        width: 1280,
        height: 720,
        fps: 30
      };

      // Act
      const newProject = await gdevelopModule.createProject(projectData);

      // Assert
      expect(newProject).toMatchObject({
        id: expect.stringMatching(/^project_\d+_[a-z0-9]+$/),
        name: 'New Game Project',
        description: 'A new exciting game',
        version: '1.0.0',
        createdAt: expect.any(String),
        settings: {
          width: 1280,
          height: 720,
          fps: 30
        }
      });
      expect(loggerMock.debug).toHaveBeenCalledWith(`Project created: ${newProject.id}`);
    });

    it('devrait supprimer un projet', async () => {
      // Arrange
      const projectId = 'test123';
      await gdevelopModule.getProject(projectId); // Créer le projet d'abord

      // Act
      const result = await gdevelopModule.deleteProject(projectId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.deletedAt).toBeDefined();
      expect(loggerMock.debug).toHaveBeenCalledWith(`Deleting project: ${projectId}`);
    });
  });

  describe('Synchronisation des ressources', () => {
    it('devrait synchroniser les ressources correctement', async () => {
      // Act
      const result = await gdevelopModule.syncResources();

      // Assert
      expect(result).toMatchObject({
        success: true,
        count: 4,
        resources: expect.arrayContaining([
          expect.objectContaining({ name: 'sprite1.png', type: 'image' }),
          expect.objectContaining({ name: 'background.jpg', type: 'image' }),
          expect.objectContaining({ name: 'sound1.wav', type: 'audio' }),
          expect.objectContaining({ name: 'music.mp3', type: 'audio' })
        ]),
        syncedAt: expect.any(String)
      });
      expect(loggerMock.debug).toHaveBeenCalledWith('Syncing resources for project: all');
    });

    it('devrait synchroniser les ressources pour un projet spécifique', async () => {
      // Arrange
      const projectId = 'test123';

      // Act
      const result = await gdevelopModule.syncResources(projectId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.count).toBe(4);
      expect(loggerMock.debug).toHaveBeenCalledWith(`Syncing resources for project: ${projectId}`);
    });
  });

  describe('Import/Export', () => {
    it('devrait exporter un projet', async () => {
      // Arrange
      const projectId = 'test123';
      await gdevelopModule.getProject(projectId); // Créer le projet d'abord

      // Act
      const exportResult = await gdevelopModule.exportProject(projectId, 'json');

      // Assert
      expect(exportResult).toMatchObject({
        format: 'json',
        data: expect.objectContaining({ id: projectId }),
        exportedAt: expect.any(String),
        size: expect.any(Number)
      });
      expect(loggerMock.debug).toHaveBeenCalledWith(`Exporting project ${projectId} as json`);
    });

    it('devrait importer un projet', async () => {
      // Arrange
      const projectData = {
        id: 'imported123',
        name: 'Imported Project',
        version: '2.0.0'
      };

      // Act
      const importedProject = await gdevelopModule.importProject(projectData);

      // Assert
      expect(importedProject).toMatchObject({
        id: 'imported123',
        name: 'Imported Project',
        version: '2.0.0',
        importedAt: expect.any(String)
      });
      expect(loggerMock.debug).toHaveBeenCalledWith('Importing project');
    });
  });

  describe('Gestion des erreurs', () => {
    it('devrait forcer une erreur quand demandé', () => {
      // Act & Assert
      expect(() => gdevelopModule.__forceError()).toThrow('Mock error');
    });

    it('devrait forcer une erreur avec un message personnalisé', () => {
      // Arrange
      const customMessage = 'Custom unit test error';

      // Act & Assert
      expect(() => gdevelopModule.__forceError(customMessage)).toThrow(customMessage);
    });

    it('devrait rejeter la création de projet sans nom', async () => {
      // Act & Assert
      await expect(gdevelopModule.createProject({})).rejects.toThrow('Project name is required');
    });
  });

  describe('Statistiques', () => {
    it('devrait retourner les statistiques du module', async () => {
      // Arrange
      await gdevelopModule.createProject({ name: 'Test Project 1' });
      await gdevelopModule.createProject({ name: 'Test Project 2' });
      await gdevelopModule.syncResources('project1');

      // Act
      const stats = gdevelopModule.getStats();

      // Assert
      expect(stats).toMatchObject({
        projects: 2,
        totalResources: expect.any(Number),
        version: '1.0.0'
      });
    });
  });
});

console.log('\n=== FIN DES TESTS GDEVELOP MODULE UNIT SIMPLIFIÉS ===\n');
