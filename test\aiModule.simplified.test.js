/**
 * Tests ultra-simplifiés pour AIModule
 *
 * Ce fichier contient des tests extrêmement simples pour le module AI,
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from './utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Mock simple du service AI
const createAIServiceMock = () => ({
  generateText: vi.fn().mockResolvedValue('Mock AI response'),
  analyzeCode: vi.fn().mockResolvedValue({
    suggestions: ['Mock suggestion'],
    qualityScore: 0.8
  }),
  initialize: vi.fn().mockResolvedValue(true),
  init: vi.fn().mockResolvedValue(true)
});

// Mock simple des métriques
const createMetricsMock = () => ({
  trackRequest: vi.fn().mockReturnValue({
    success: true,
    responseTime: 100
  }),
  getMetrics: vi.fn().mockReturnValue({
    totalRequests: 10,
    averageResponseTime: 150
  })
});

// Classe AIModule simplifiée pour les tests
class SimplifiedAIModule {
  constructor(options = {}) {
    this.logger = options.logger || loggerMock;
    this.service = options.service || createAIServiceMock();
    this.metrics = options.metrics || createMetricsMock();
    this.initialized = false;
  }

  async initialize() {
    try {
      await this.service.initialize();
      this.initialized = true;
      this.logger.info('AI Module initialized successfully');
      return true;
    } catch (error) {
      this.logger.error('Failed to initialize AI Module:', error);
      return false;
    }
  }

  async generateText(prompt, options = {}) {
    if (!this.initialized) {
      throw new Error('AI Module not initialized');
    }

    const startTime = Date.now();
    
    try {
      const result = await this.service.generateText(prompt, options);
      
      // Enregistrer les métriques
      this.metrics.trackRequest({
        type: 'generateText',
        success: true,
        responseTime: Date.now() - startTime
      });

      return result;
    } catch (error) {
      this.metrics.trackRequest({
        type: 'generateText',
        success: false,
        responseTime: Date.now() - startTime,
        error: error.message
      });
      throw error;
    }
  }

  async analyzeCode(code, language = 'javascript') {
    if (!this.initialized) {
      throw new Error('AI Module not initialized');
    }

    const startTime = Date.now();
    
    try {
      const result = await this.service.analyzeCode(code, language);
      
      // Enregistrer les métriques
      this.metrics.trackRequest({
        type: 'analyzeCode',
        success: true,
        responseTime: Date.now() - startTime
      });

      return result;
    } catch (error) {
      this.metrics.trackRequest({
        type: 'analyzeCode',
        success: false,
        responseTime: Date.now() - startTime,
        error: error.message
      });
      throw error;
    }
  }

  getMetrics() {
    return this.metrics.getMetrics();
  }
}

console.log('\n=== DÉBUT DES TESTS AI MODULE SIMPLIFIÉS ===\n');

describe('AIModule (Simplifié)', () => {
  let aiModule;
  let serviceMock;
  let metricsMock;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();
    
    // Créer des mocks frais
    serviceMock = createAIServiceMock();
    metricsMock = createMetricsMock();
    
    // Créer une nouvelle instance
    aiModule = new SimplifiedAIModule({
      logger: loggerMock,
      service: serviceMock,
      metrics: metricsMock
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initialisation', () => {
    it('devrait initialiser le module avec succès', async () => {
      // Act
      const result = await aiModule.initialize();

      // Assert
      expect(result).toBe(true);
      expect(aiModule.initialized).toBe(true);
      expect(serviceMock.initialize).toHaveBeenCalledOnce();
      expect(loggerMock.info).toHaveBeenCalledWith('AI Module initialized successfully');
    });

    it('devrait gérer les erreurs d\'initialisation', async () => {
      // Arrange
      const error = new Error('Service initialization failed');
      serviceMock.initialize.mockRejectedValue(error);

      // Act
      const result = await aiModule.initialize();

      // Assert
      expect(result).toBe(false);
      expect(aiModule.initialized).toBe(false);
      expect(loggerMock.error).toHaveBeenCalledWith('Failed to initialize AI Module:', error);
    });
  });

  describe('Génération de texte', () => {
    beforeEach(async () => {
      await aiModule.initialize();
    });

    it('devrait générer du texte avec succès', async () => {
      // Arrange
      const prompt = 'Test prompt';
      const expectedResponse = 'Generated text response';
      serviceMock.generateText.mockResolvedValue(expectedResponse);

      // Act
      const result = await aiModule.generateText(prompt);

      // Assert
      expect(result).toBe(expectedResponse);
      expect(serviceMock.generateText).toHaveBeenCalledWith(prompt, {});
      expect(metricsMock.trackRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'generateText',
          success: true
        })
      );
    });

    it('devrait rejeter si le module n\'est pas initialisé', async () => {
      // Arrange
      aiModule.initialized = false;

      // Act & Assert
      await expect(aiModule.generateText('test')).rejects.toThrow('AI Module not initialized');
    });

    it('devrait gérer les erreurs de génération', async () => {
      // Arrange
      const error = new Error('Generation failed');
      serviceMock.generateText.mockRejectedValue(error);

      // Act & Assert
      await expect(aiModule.generateText('test')).rejects.toThrow('Generation failed');
      expect(metricsMock.trackRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'generateText',
          success: false,
          error: 'Generation failed'
        })
      );
    });
  });

  describe('Analyse de code', () => {
    beforeEach(async () => {
      await aiModule.initialize();
    });

    it('devrait analyser le code avec succès', async () => {
      // Arrange
      const code = 'function test() { return true; }';
      const language = 'javascript';
      const expectedAnalysis = {
        suggestions: ['Use const instead of function'],
        qualityScore: 0.9
      };
      serviceMock.analyzeCode.mockResolvedValue(expectedAnalysis);

      // Act
      const result = await aiModule.analyzeCode(code, language);

      // Assert
      expect(result).toEqual(expectedAnalysis);
      expect(serviceMock.analyzeCode).toHaveBeenCalledWith(code, language);
      expect(metricsMock.trackRequest).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'analyzeCode',
          success: true
        })
      );
    });

    it('devrait utiliser javascript comme langage par défaut', async () => {
      // Arrange
      const code = 'test code';

      // Act
      await aiModule.analyzeCode(code);

      // Assert
      expect(serviceMock.analyzeCode).toHaveBeenCalledWith(code, 'javascript');
    });

    it('devrait rejeter si le module n\'est pas initialisé', async () => {
      // Arrange
      aiModule.initialized = false;

      // Act & Assert
      await expect(aiModule.analyzeCode('test')).rejects.toThrow('AI Module not initialized');
    });
  });

  describe('Métriques', () => {
    it('devrait retourner les métriques', () => {
      // Arrange
      const expectedMetrics = {
        totalRequests: 5,
        averageResponseTime: 200
      };
      metricsMock.getMetrics.mockReturnValue(expectedMetrics);

      // Act
      const result = aiModule.getMetrics();

      // Assert
      expect(result).toEqual(expectedMetrics);
      expect(metricsMock.getMetrics).toHaveBeenCalledOnce();
    });
  });
});

console.log('\n=== FIN DES TESTS AI MODULE SIMPLIFIÉS ===\n');
