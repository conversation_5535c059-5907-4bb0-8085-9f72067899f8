import crypto from 'crypto';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const KEY_DIR = path.join(__dirname, '../config');
const PRIVATE_KEY_PATH = path.join(KEY_DIR, 'private.key');
const PUBLIC_KEY_PATH = path.join(KEY_DIR, 'public.key');

function generateKeys() {
  try {
    // C<PERSON>er le dossier config s'il n'existe pas
    if (!fs.existsSync(KEY_DIR)) {
      fs.mkdirSync(KEY_DIR, { recursive: true });
    }

    // Générer la paire de clés RSA
    const { privateKey, publicKey } = crypto.generateKeyPairSync('rsa', {
      modulusLength: 2048,
      publicKeyEncoding: {
        type: 'spki',
        format: 'pem'
      },
      privateKeyEncoding: {
        type: 'pkcs8',
        format: 'pem'
      }
    });

    // Sauvegarder les clés
    fs.writeFileSync(PRIVATE_KEY_PATH, privateKey);
    fs.writeFileSync(PUBLIC_KEY_PATH, publicKey);

    // Sécuriser les permissions (surtout sur Unix)
    if (process.platform !== 'win32') {
      fs.chmodSync(PRIVATE_KEY_PATH, 0o600); // -rw-------
      fs.chmodSync(PUBLIC_KEY_PATH, 0o644);  // -rw-r--r--
    }

    console.log('Clés RSA générées avec succès dans config/');
  } catch (err) {
    console.error('Erreur lors de la génération des clés:', err);
    process.exit(1);
  }
}

// Vérifier si les clés existent déjà
if (!fs.existsSync(PRIVATE_KEY_PATH) || !fs.existsSync(PUBLIC_KEY_PATH)) {
  generateKeys();
} else {
  console.log('Les clés RSA existent déjà dans config/');
}
