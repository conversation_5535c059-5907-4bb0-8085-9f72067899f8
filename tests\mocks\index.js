/**
 * Configuration centralisée des mocks pour le répertoire tests/
 * Ce fichier permet d'importer tous les mocks nécessaires depuis un seul endroit
 */

import { vi } from 'vitest';
import loggerMock from '../../test/mocks/logger';
import GDevelopAPIMock from '../../test/mocks/gdevelopAPI';
import fsMock from '../../test/mocks/fs';
import childProcessMock from '../../test/mocks/child_process';
import netMock from '../../test/mocks/net';

// Exporter tous les mocks pour faciliter l'importation
export {
  loggerMock,
  GDevelopAPIMock,
  fsMock,
  childProcessMock,
  netMock
};

/**
 * Fonction utilitaire pour configurer tous les mocks nécessaires
 * @param {Object} options - Options de configuration
 */
export function setupAllMocks(options = {}) {
  const defaultOptions = {
    mockLogger: true,
    mockGDevelopAPI: true,
    mockFS: true,
    mockNet: true,
    mockChildProcess: true,
    mockAIMetrics: true,
    mockAuth: true
  };

  const finalOptions = { ...defaultOptions, ...options };

  // Configuration du logger
  if (finalOptions.mockLogger) {
    vi.mock('#logger', () => ({
      default: loggerMock,
      logger: loggerMock,
      createLogger: vi.fn(() => loggerMock)
    }));
  }

  // Configuration de GDevelop API
  if (finalOptions.mockGDevelopAPI) {
    vi.mock('../../integrations/gdevelop/api', () => ({
      default: GDevelopAPIMock
    }));
  }

  // Configuration des modules natifs
  if (finalOptions.mockFS) {
    vi.mock('fs', () => ({
      ...fsMock,
      default: fsMock,
      promises: fsMock.promises,
      readFileSync: fsMock.readFileSync,
      writeFileSync: fsMock.writeFileSync,
      existsSync: fsMock.existsSync,
      unlinkSync: fsMock.unlinkSync
    }));
  }

  // Configuration de net
  if (finalOptions.mockNet) {
    vi.mock('net', () => ({
      ...netMock,
      default: netMock,
      createServer: netMock.createServer
    }));
  }

  // Configuration de child_process
  if (finalOptions.mockChildProcess) {
    vi.mock('child_process', () => ({
      ...childProcessMock,
      default: childProcessMock,
      exec: childProcessMock.exec,
      execSync: childProcessMock.execSync
    }));
  }

  // Configuration de AIMetrics
  if (finalOptions.mockAIMetrics) {
    vi.mock('../../src/utils/aiMetrics', () => ({
      default: {
        trackRequest: vi.fn(),
        getMetrics: vi.fn(() => ({ total: 0, success: 0, failure: 0 }))
      },
      trackRequest: vi.fn(),
      getMetrics: vi.fn(() => ({ total: 0, success: 0, failure: 0 }))
    }));
  }

  // Configuration de Auth
  if (finalOptions.mockAuth) {
    // Configurer le mock pour auth.js
    vi.mock('../../src/utils/auth', async (importOriginal) => {
      // Importer le module original pour conserver les autres exports
      const actual = await importOriginal();

      // Créer les mocks directement dans la fonction de mock
      const authMiddlewareMock = vi.fn((req, res, next) => {
        req.user = {
          userId: 'test-user-id',
          roles: [],
          permissions: []
        };
        return next();
      });

      // S'assurer que le mock a toutes les propriétés nécessaires
      Object.defineProperty(authMiddlewareMock, 'mockImplementationOnce', {
        enumerable: true,
        value: vi.fn().mockImplementation(fn => {
          return vi.fn().mockImplementationOnce(fn);
        })
      });

      // Retourner un objet combinant le module original et nos mocks
      return {
        ...actual,
        authMiddleware: authMiddlewareMock,
        generateToken: vi.fn(() => 'MOCK_TOKEN'),
        verifyToken: vi.fn(() => ({
          userId: 'test-user-id',
          roles: [],
          permissions: []
        }))
      };
    });

    console.log('Auth mocks configurés avec succès dans tests/');
  }

  console.log('Mocks configurés pour tests/');
}

/**
 * Fonction pour réinitialiser tous les mocks
 */
export function resetAllMocks() {
  vi.resetAllMocks();
  vi.clearAllMocks();

  // Réinitialiser les états internes des mocks
  fsMock.__clearFiles();
  netMock.__clearAllPorts();
  childProcessMock.__clearAllProcesses();

  console.log('Mocks réinitialisés');
}
