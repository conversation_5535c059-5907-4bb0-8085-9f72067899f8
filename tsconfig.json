{"compilerOptions": {"target": "ES2020", "module": "commonjs", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "types": ["vitest/globals"], "outDir": "./dist", "rootDir": ".", "baseUrl": ".", "paths": {"#logger": ["./src/utils/logger"], "@mcp-server": ["./mcp-server"], "@": ["./src"]}}, "include": ["**/*.ts", "**/*.js"], "exclude": ["node_modules"]}