class ConversationMemory {
  constructor(maxHistory = 3) {
    this.history = [];
    this.maxHistory = maxHistory;
  }

  addMessage(role, content) {
    this.history.push({ role, content });
    if (this.history.length > this.maxHistory) {
      this.history.shift();
    }
  }

  getContext() {
    return this.history.map(m => `${m.role}: ${m.content}`).join('\n');
  }

  clear() {
    this.history = [];
  }
}

export default ConversationMemory;
