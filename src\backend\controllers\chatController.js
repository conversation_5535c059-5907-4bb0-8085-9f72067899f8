import { BaseController } from './baseController.js'

export class ChatController extends BaseController {
  constructor(chatService) {
    super()
    this.chatService = chatService
  }

  async handleMessage(req, res) {
    try {
      const { message } = req.body
      const response = await this.chatService.processMessage(message)
      
      this.response.send(res, {
        text: response,
        sender: 'assistant'
      }, {
        interactionId: req.interactionId
      })
      
    } catch (error) {
      this.response.error(res, error, 'CHAT_PROCESSING_ERROR', 400)
    }
  }
}
