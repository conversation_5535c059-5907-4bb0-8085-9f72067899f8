/**
 * Tests ultra-simplifiés pour l'intégration de l'API GDevelop
 *
 * Ce fichier contient des tests extrêmement simples pour l'intégration de l'API GDevelop,
 * sans dépendances à Core ou à d'autres modules complexes.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from '../../../test/utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Classe simplifiée pour l'API GDevelop
class GDevelopAPI {
  constructor(options = {}) {
    this.logger = options.logger || loggerMock;
    this.projects = new Map([
      ['test123', {
        id: 'test123',
        title: 'Test Game',
        objects: [],
        events: [
          { name: 'Start Scene', actions: [] },
          { name: 'Game Over', actions: [] }
        ],
        resources: [
          { name: 'sprite1.png', type: 'image' },
          { name: 'sound1.mp3', type: 'audio' }
        ],
        changes: []
      }]
    ]);
  }

  // Obtenir un projet
  async getProject(projectId) {
    this.logger.info(`Récupération du projet ${projectId}`);
    
    const project = this.projects.get(projectId);
    if (!project) {
      this.logger.error(`Projet ${projectId} non trouvé`);
      throw new Error(`Project not found: ${projectId}`);
    }
    
    return project;
  }

  // Mettre à jour un projet
  async updateProject(projectId, data) {
    this.logger.info(`Mise à jour du projet ${projectId}`);
    
    const project = await this.getProject(projectId);
    
    // Vérifier que les données sont valides
    if (!data || Object.keys(data).length === 0) {
      this.logger.error(`Données de projet invalides`);
      throw new Error('Invalid project data');
    }
    
    // Mettre à jour le projet
    const updatedProject = {
      ...project,
      ...data,
      updatedAt: new Date().toISOString()
    };
    
    // Enregistrer les modifications
    updatedProject.changes.push({
      timestamp: new Date().toISOString(),
      type: 'update',
      data
    });
    
    // Sauvegarder le projet
    this.projects.set(projectId, updatedProject);
    
    return {
      status: 'success',
      updatedAt: updatedProject.updatedAt
    };
  }

  // Obtenir les événements d'un projet
  async getEvents(projectId) {
    this.logger.info(`Récupération des événements du projet ${projectId}`);
    
    const project = await this.getProject(projectId);
    
    return {
      status: 'success',
      events: project.events
    };
  }

  // Synchroniser les ressources d'un projet
  async syncResources(projectId) {
    this.logger.info(`Synchronisation des ressources du projet ${projectId}`);
    
    const project = await this.getProject(projectId);
    
    return {
      status: 'success',
      resources: project.resources,
      syncedAt: new Date().toISOString()
    };
  }

  // Obtenir les modifications d'un projet
  async getChanges(projectId) {
    this.logger.info(`Récupération des modifications du projet ${projectId}`);
    
    const project = await this.getProject(projectId);
    
    return {
      status: 'success',
      changes: project.changes
    };
  }
}

// Classe simplifiée pour le module GDevelop
class GDevelopModule {
  constructor(options = {}) {
    this.logger = options.logger || loggerMock;
    this.api = new GDevelopAPI({ logger: this.logger });
  }

  // Traiter un message
  async process(message) {
    this.logger.info(`Traitement du message: ${message.action}`);
    
    try {
      switch (message.action) {
        case 'getProject':
          const project = await this.api.getProject(message.projectId);
          return { status: 'success', project };
          
        case 'updateProject':
          return await this.api.updateProject(message.projectId, message.data);
          
        case 'getEvents':
          return await this.api.getEvents(message.projectId);
          
        case 'syncResources':
          return await this.api.syncResources(message.projectId);
          
        case 'getChanges':
          return await this.api.getChanges(message.projectId);
          
        default:
          this.logger.error(`Action non supportée: ${message.action}`);
          throw new Error(`Unsupported action: ${message.action}`);
      }
    } catch (error) {
      this.logger.error(`Erreur module GDevelop: ${error.message}`);
      throw error;
    }
  }
}

console.log('\n=== DÉBUT DES TESTS GDEVELOP API INTEGRATION SIMPLIFIÉS ===\n');

describe('GDevelop API Integration (Simplifiée)', () => {
  let gdevelopModule;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();
    
    // Créer une nouvelle instance du module GDevelop
    gdevelopModule = new GDevelopModule({ logger: loggerMock });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('devrait récupérer un projet', async () => {
    const result = await gdevelopModule.process({
      action: 'getProject',
      projectId: 'test123'
    });
    
    // Vérifier que le projet a été récupéré
    expect(result.status).toBe('success');
    expect(result.project.id).toBe('test123');
    expect(result.project.title).toBe('Test Game');
    
    // Vérifier que le logger a été appelé
    expect(loggerMock.info).toHaveBeenCalledWith(expect.stringContaining('Traitement du message: getProject'));
  });

  it('devrait mettre à jour un projet', async () => {
    const result = await gdevelopModule.process({
      action: 'updateProject',
      projectId: 'test123',
      data: { title: 'Updated Game' }
    });
    
    // Vérifier que le projet a été mis à jour
    expect(result.status).toBe('success');
    expect(result.updatedAt).toBeDefined();
    
    // Vérifier que le logger a été appelé
    expect(loggerMock.info).toHaveBeenCalledWith(expect.stringContaining('Traitement du message: updateProject'));
  });

  it('devrait gérer les données de projet invalides', async () => {
    // Vérifier que l'erreur est bien lancée
    await expect(
      gdevelopModule.process({
        action: 'updateProject',
        projectId: 'test123',
        data: {} // Données manquantes
      })
    ).rejects.toThrow('Invalid project data');
    
    // Vérifier que le logger a été appelé
    expect(loggerMock.error).toHaveBeenCalledWith(expect.stringContaining('Erreur module GDevelop'));
  });

  it('devrait récupérer les événements d\'un projet', async () => {
    const result = await gdevelopModule.process({
      action: 'getEvents',
      projectId: 'test123'
    });
    
    // Vérifier que les événements ont été récupérés
    expect(result.status).toBe('success');
    expect(result.events).toHaveLength(2);
    expect(result.events[0].name).toBe('Start Scene');
    
    // Vérifier que le logger a été appelé
    expect(loggerMock.info).toHaveBeenCalledWith(expect.stringContaining('Traitement du message: getEvents'));
  });

  it('devrait synchroniser les ressources d\'un projet', async () => {
    const result = await gdevelopModule.process({
      action: 'syncResources',
      projectId: 'test123'
    });
    
    // Vérifier que les ressources ont été synchronisées
    expect(result.status).toBe('success');
    expect(result.resources).toBeDefined();
    expect(result.syncedAt).toBeDefined();
    
    // Vérifier que le logger a été appelé
    expect(loggerMock.info).toHaveBeenCalledWith(expect.stringContaining('Traitement du message: syncResources'));
  });

  it('devrait récupérer les modifications d\'un projet', async () => {
    // D'abord faire une mise à jour pour générer des modifications
    await gdevelopModule.process({
      action: 'updateProject',
      projectId: 'test123',
      data: { title: 'Updated Game' }
    });
    
    const result = await gdevelopModule.process({
      action: 'getChanges',
      projectId: 'test123'
    });
    
    // Vérifier que les modifications ont été récupérées
    expect(result.status).toBe('success');
    expect(result.changes.length).toBeGreaterThan(0);
    expect(result.changes[0].timestamp).toBeDefined();
    
    // Vérifier que le logger a été appelé
    expect(loggerMock.info).toHaveBeenCalledWith(expect.stringContaining('Traitement du message: getChanges'));
  });

  it('devrait gérer les actions non supportées', async () => {
    // Vérifier que l'erreur est bien lancée
    await expect(
      gdevelopModule.process({
        action: 'invalidAction',
        projectId: 'test123'
      })
    ).rejects.toThrow('Unsupported action');
    
    // Vérifier que le logger a été appelé
    expect(loggerMock.error).toHaveBeenCalledWith(expect.stringContaining('Action non supportée'));
  });
});

console.log('\n=== FIN DES TESTS GDEVELOP API INTEGRATION SIMPLIFIÉS ===\n');
