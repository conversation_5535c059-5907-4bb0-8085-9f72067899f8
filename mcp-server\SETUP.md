# MCP Server v5.0 - Nouvelle Architecture

## 1. Installation Sécurisée
```bash
./install.bat --mcp-key <CLÉ_API>  # Nécessite une clé valide
```

## 2. Configuration

### 2.1 Sécurité MCP
```javascript
// config/config.js
mcp: {
  auth: {
    apiKeys: ["KEY1", "KEY2"], // Clés actives
    keyRotation: {
      interval: '7d',           // Rotation hebdomadaire
      historySize: 3            // Historique des clés
    }
  }
}
```

### 2.2 Monitoring
```bash
# Démarrer le dashboard
npm run monitor
```

## 3. Commandes MCP
| Commande | Description |
|----------|-------------|
| `npm run mcp:rotate-keys` | Rotation des clés API |
| `npm run mcp:stress-test` | Tests de charge k6 |

## 4. CI/CD
```mermaid
graph TD
  A[Commit] --> B{Scan <PERSON>}
  B -->|Passé| C[Déploiement Canary]
  C --> D{Monitoring 24h}
  D -->|Stable| E[Rollout Complet]
```

## 5. Configuration des Clés Deepseek

### Clés Requises
```env
# .env
CHAT_API_KEY=sk_chat_123
AGENT_API_KEY=sk_agent_456
```

### Utilisation
```javascript
// Dans mcp-server/config/config.js
deepseek: {
  chatKey: process.env.CHAT_API_KEY,
  agentKey: process.env.AGENT_API_KEY,
  baseURL: 'https://api.deepseek.com/v1'
}
```

## 6. Dépannage
**ERR_MCP_KEY_EXPIRED**  
Solution :
```bash
npm run mcp:refresh-keys -- --current-key=<CLÉ_EXPIRÉE>
