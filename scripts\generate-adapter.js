import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const ADAPTERS_DIR = path.join(__dirname, '../src/utils/portManager/adapters');

// Template pour les adapters manquants
const ADAPTER_TEMPLATE = (platform) => `// Auto-generated adapter for ${platform}
export default class ${platform.charAt(0).toUpperCase() + platform.slice(1)}Adapter {
  /**
   * Find process using port
   * @param {number} port 
   * @returns {Promise<{pid: number, name: string}|null>}
   */
  async findProcessByPort(port) {
    console.warn('${platform} adapter not fully implemented');
    return null;
  }

  /**
   * Kill process by PID
   * @param {number} pid 
   * @returns {Promise<boolean>}
   */
  async killProcess(pid) {
    console.warn('${platform} adapter not fully implemented');
    return false;
  }
}
`;

function createAdapter(platform) {
  const adapterPath = path.join(ADAPTERS_DIR, `${platform}.js`);
  
  if (!fs.existsSync(ADAPTERS_DIR)) {
    fs.mkdirSync(ADAPTERS_DIR, { recursive: true });
  }

  if (!fs.existsSync(adapterPath)) {
    fs.writeFileSync(adapterPath, ADAPTER_TEMPLATE(platform));
    console.log(`Created ${platform} adapter at ${path.relative(process.cwd(), adapterPath)}`);
    return true;
  }

  console.log(`${platform} adapter already exists`);
  return false;
}

// Crée les adapters manquants
createAdapter('linux');
createAdapter('macos'); // Pour anticiper d'autres plateformes

console.log('✅ Adapters generation complete');
