/**
 * Tests ultra-simplifiés pour ChatService
 *
 * Ce fichier contient des tests extrêmement simples pour le service de chat,
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from './utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Mock simple du service AI
const createAIServiceMock = () => ({
  generateText: vi.fn().mockResolvedValue('Mock AI response'),
  initialize: vi.fn().mockResolvedValue(true)
});

// Mock simple du stockage de messages
const createStorageMock = () => ({
  addMessage: vi.fn().mockResolvedValue(true),
  getHistory: vi.fn().mockResolvedValue([]),
  removeOldestMessages: vi.fn().mockResolvedValue(true),
  messages: []
});

// Mock simple du RBAC
const createRBACMock = () => ({
  hasPermission: vi.fn().mockReturnValue(true),
  checkPermission: vi.fn().mockReturnValue(true)
});

// Classe ChatService simplifiée pour les tests
class SimplifiedChatService {
  constructor(options = {}) {
    this.aiService = options.aiService || createAIServiceMock();
    this.logger = options.logger || loggerMock;
    this.storage = options.storage || createStorageMock();
    this.rbac = options.rbac || createRBACMock();
    this.MAX_HISTORY = 20;
  }

  async processMessage(message, user = null) {
    try {
      // Vérifier les permissions si un utilisateur est fourni
      if (user && !this.rbac.hasPermission(user, 'chat.access')) {
        this.logger.error(`Permission denied for user ${user.username}`, {
          code: 'CHAT_001'
        });
        
        return {
          data: {
            error: {
              code: 'CHAT_001',
              message: 'Permission denied',
              details: `User ${user.username} does not have chat access`
            }
          },
          metadata: {
            timestamp: Date.now(),
            failed: true,
            isAI: false,
            source: 'chatService'
          }
        };
      }

      // Vérifier la limite d'historique
      const history = await this.storage.getHistory();
      if (history.length >= this.MAX_HISTORY) {
        await this.storage.removeOldestMessages(history.length - this.MAX_HISTORY + 1);
      }

      // Générer la réponse AI
      const aiResponse = await this.aiService.generateText(message);
      
      // Créer l'ID de contexte
      const contextId = `ctx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // Sauvegarder le message
      await this.storage.addMessage({
        text: message,
        response: aiResponse,
        contextId,
        timestamp: Date.now()
      });

      return {
        data: {
          text: aiResponse,
          contextId
        },
        metadata: {
          timestamp: Date.now(),
          isAI: true,
          source: 'chatService',
          failed: false
        }
      };

    } catch (error) {
      this.logger.error('Message processing failed:', error);
      
      return {
        data: {
          error: {
            code: 'CHAT_001',
            message: error.message,
            details: `Failed to process message: ${message}`
          }
        },
        metadata: {
          timestamp: Date.now(),
          failed: true,
          isAI: false,
          source: 'chatService'
        }
      };
    }
  }

  async getHistory() {
    return await this.storage.getHistory();
  }

  async clearHistory() {
    return await this.storage.removeOldestMessages(Infinity);
  }
}

console.log('\n=== DÉBUT DES TESTS CHAT SERVICE SIMPLIFIÉS ===\n');

describe('ChatService (Simplifié)', () => {
  let chatService;
  let aiServiceMock;
  let storageMock;
  let rbacMock;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();
    
    // Créer des mocks frais
    aiServiceMock = createAIServiceMock();
    storageMock = createStorageMock();
    rbacMock = createRBACMock();
    
    // Créer une nouvelle instance
    chatService = new SimplifiedChatService({
      aiService: aiServiceMock,
      logger: loggerMock,
      storage: storageMock,
      rbac: rbacMock
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Traitement des messages', () => {
    it('devrait traiter un message avec succès', async () => {
      // Arrange
      const message = 'Hello, how are you?';
      const expectedResponse = 'I am doing well, thank you!';
      aiServiceMock.generateText.mockResolvedValue(expectedResponse);

      // Act
      const result = await chatService.processMessage(message);

      // Assert
      expect(result.data.text).toBe(expectedResponse);
      expect(result.data.contextId).toMatch(/^ctx_\d+_[a-z0-9]+$/);
      expect(result.metadata.isAI).toBe(true);
      expect(result.metadata.failed).toBe(false);
      expect(result.metadata.source).toBe('chatService');
      expect(aiServiceMock.generateText).toHaveBeenCalledWith(message);
      expect(storageMock.addMessage).toHaveBeenCalled();
    });

    it('devrait gérer les erreurs de génération AI', async () => {
      // Arrange
      const message = 'Test message';
      const error = new Error('AI service failed');
      aiServiceMock.generateText.mockRejectedValue(error);

      // Act
      const result = await chatService.processMessage(message);

      // Assert
      expect(result.data.error.code).toBe('CHAT_001');
      expect(result.data.error.message).toBe('AI service failed');
      expect(result.metadata.failed).toBe(true);
      expect(result.metadata.isAI).toBe(false);
      expect(loggerMock.error).toHaveBeenCalledWith('Message processing failed:', error);
    });

    it('devrait maintenir un format de réponse cohérent', async () => {
      // Act
      const result = await chatService.processMessage('test');

      // Assert
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('metadata');
      expect(result.metadata).toMatchObject({
        timestamp: expect.any(Number),
        isAI: true,
        source: 'chatService',
        failed: false
      });
      expect(result.data).toMatchObject({
        text: expect.any(String),
        contextId: expect.any(String)
      });
    });
  });

  describe('Gestion des permissions', () => {
    it('devrait autoriser les utilisateurs avec permissions', async () => {
      // Arrange
      const user = { username: 'testuser' };
      rbacMock.hasPermission.mockReturnValue(true);

      // Act
      const result = await chatService.processMessage('test', user);

      // Assert
      expect(rbacMock.hasPermission).toHaveBeenCalledWith(user, 'chat.access');
      expect(result.metadata.failed).toBe(false);
      expect(aiServiceMock.generateText).toHaveBeenCalled();
    });

    it('devrait rejeter les utilisateurs sans permissions', async () => {
      // Arrange
      const user = { username: 'unauthorizeduser' };
      rbacMock.hasPermission.mockReturnValue(false);

      // Act
      const result = await chatService.processMessage('test', user);

      // Assert
      expect(rbacMock.hasPermission).toHaveBeenCalledWith(user, 'chat.access');
      expect(result.data.error.code).toBe('CHAT_001');
      expect(result.data.error.message).toBe('Permission denied');
      expect(result.metadata.failed).toBe(true);
      expect(aiServiceMock.generateText).not.toHaveBeenCalled();
      expect(loggerMock.error).toHaveBeenCalledWith(
        'Permission denied for user unauthorizeduser',
        expect.objectContaining({ code: 'CHAT_001' })
      );
    });
  });

  describe('Gestion de l\'historique', () => {
    it('devrait sauvegarder les messages dans l\'historique', async () => {
      // Act
      await chatService.processMessage('test message');

      // Assert
      expect(storageMock.addMessage).toHaveBeenCalledWith(
        expect.objectContaining({
          text: 'test message',
          response: expect.any(String),
          contextId: expect.any(String),
          timestamp: expect.any(Number)
        })
      );
    });

    it('devrait respecter la limite MAX_HISTORY', async () => {
      // Arrange
      const longHistory = Array(25).fill({ text: 'old message' });
      storageMock.getHistory.mockResolvedValue(longHistory);

      // Act
      await chatService.processMessage('new message');

      // Assert
      expect(storageMock.removeOldestMessages).toHaveBeenCalledWith(6); // 25 - 20 + 1
    });

    it('devrait récupérer l\'historique', async () => {
      // Arrange
      const expectedHistory = [{ text: 'message1' }, { text: 'message2' }];
      storageMock.getHistory.mockResolvedValue(expectedHistory);

      // Act
      const result = await chatService.getHistory();

      // Assert
      expect(result).toEqual(expectedHistory);
      expect(storageMock.getHistory).toHaveBeenCalled();
    });

    it('devrait vider l\'historique', async () => {
      // Act
      await chatService.clearHistory();

      // Assert
      expect(storageMock.removeOldestMessages).toHaveBeenCalledWith(Infinity);
    });
  });
});

console.log('\n=== FIN DES TESTS CHAT SERVICE SIMPLIFIÉS ===\n');
