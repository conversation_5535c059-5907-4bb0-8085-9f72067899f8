import { vi } from 'vitest';

// Mock global du logger
vi.mock('#logger', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
    child: vi.fn(() => ({
      info: vi.fn(),
      error: vi.fn()
    }))
  }
}));
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Configuration globale des mocks
export function setupTestEnvironment() {
  // Mock du logger
  vi.mock('#logger', () => {
    const logger = {
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      child: () => logger
    };
    return { default: logger };
  });

  // Mock de GDevelopAPI
  vi.mock('../integrations/gdevelop/api.js', () => {
    const MockGDevelopAPI = class {
      getProject(id) {
        return Promise.resolve({ id, name: 'Mock Project' });
      }
    };
    return { 
      default: MockGDevelopAPI,
      GDevelopAPI: MockGDevelopAPI 
    };
  });

  // Mock des adapters de port
  vi.mock('../src/utils/portManager/adapters/linux.js', () => ({
    default: {
      findProcessByPort: vi.fn().mockResolvedValue(null),
      killProcess: vi.fn().mockResolvedValue(true)
    }
  }));

  console.log('✅ Test environment setup complete');
}

// Utilisation recommandée :
// Ajouter dans vitest.config.ts:
// setupFiles: ['./scripts/test-setup.js']
