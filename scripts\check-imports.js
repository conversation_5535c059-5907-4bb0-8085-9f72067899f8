import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Modules npm connus (peut être étendu)
const NPM_MODULES = new Set([
  'vite', '@vitejs/plugin-react', 'express', 'cors', 
  'jsonwebtoken', 'openai', 'react', 'vitest', 'axios',
  'dotenv', 'winston', 'winston-daily-rotate-file',
  'child_process', 'net', 'fs', 'path', 'url', 'crypto',
  'module', '@jest/globals'
]);

// Extensions autorisées
const ALLOWED_EXTENSIONS = new Set(['.js', '.ts', '.json', '.cjs', '.mjs', '.jsx']);

// Préfixes d'alias à ignorer
const IGNORED_PREFIXES = ['@/', '~/'];

function isNpmModule(importPath) {
  return NPM_MODULES.has(importPath) || 
         (!importPath.startsWith('.') && !importPath.startsWith('/'));
}

function shouldIgnoreImport(importPath, filePath) {
  const ext = path.extname(importPath);
  const dir = path.dirname(filePath);
  
  // Autoriser les extensions connues
  if (ext && ALLOWED_EXTENSIONS.has(ext)) {
    return true;
  }

  // Ignorer les alias de projet
  if (IGNORED_PREFIXES.some(prefix => importPath.startsWith(prefix))) {
    return true;
  }

  // Vérifier l'existence du fichier avec différentes extensions
  if (!ext) {
    // Essayer avec différentes extensions
    for (const ext of ['.js', '.ts', '.jsx']) {
      if (fs.existsSync(path.join(dir, `${importPath}${ext}`))) return true;
    }
    // Essayer avec différents index
    for (const indexFile of ['index.js', 'index.ts', 'index.mjs']) {
      if (fs.existsSync(path.join(dir, importPath, indexFile))) return true;
    }
  }

  return false;
}

async function scanProject(dir) {
  let stats = { files: 0, errors: 0 };
  const results = [];

  function checkFile(filePath) {
    stats.files++;
    const content = fs.readFileSync(filePath, 'utf8');
    let hasErrors = false;

    // Détection des imports ES
    const esImports = content.match(/from\s+['"]([^'"]+)['"]/g) || [];
    esImports.forEach(imp => {
      const importPath = imp.match(/from\s+['"]([^'"]+)['"]/)[1];
      if (!isNpmModule(importPath) && !shouldIgnoreImport(importPath, filePath)) {
        results.push({ 
          file: path.relative(process.cwd(), filePath),
          line: imp.trim(),
          type: 'import',
          error: getErrorType(importPath, filePath)
        });
        hasErrors = true;
      }
    });

    // Détection des require()
    const requires = content.match(/require\(['"]([^'"]+)['"]\)/g) || [];
    requires.forEach(req => {
      const importPath = req.match(/require\(['"]([^'"]+)['"]\)/)[1];
      if (!isNpmModule(importPath) && !shouldIgnoreImport(importPath, filePath)) {
        results.push({ 
          file: path.relative(process.cwd(), filePath),
          line: req.trim(),
          type: 'require',
          error: getErrorType(importPath, filePath)
        });
        hasErrors = true;
      }
    });

    if (hasErrors) stats.errors++;
  }

  function getErrorType(importPath, filePath) {
    const dir = path.dirname(filePath);
    
    // Vérifier si le fichier existe avec une autre extension
    for (const ext of ['.js', '.ts', '.jsx']) {
      if (fs.existsSync(path.join(dir, `${importPath}${ext}`))) {
        return 'WRONG_EXTENSION';
      }
    }

    // Vérifier les index files
    for (const indexFile of ['index.js', 'index.ts', 'index.mjs']) {
      if (fs.existsSync(path.join(dir, importPath, indexFile))) {
        return 'WRONG_INDEX_EXTENSION';
      }
    }

    return 'MISSING_FILE';
  }

  function walkDirectory(currentDir) {
    if (currentDir.includes('node_modules')) return;

    fs.readdirSync(currentDir).forEach(file => {
      const fullPath = path.join(currentDir, file);
      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        walkDirectory(fullPath);
      } else if (/\.(js|ts)$/.test(file)) {
        checkFile(fullPath);
      }
    });
  }

  walkDirectory(dir);
  return { results, stats };
}

// Exécution
const { results, stats } = await scanProject(process.cwd());

if (results.length > 0) {
  console.error('\x1b[31m%s\x1b[0m', `❌ ${stats.errors} fichiers avec imports problématiques:`);
  results.forEach((item, i) => {
    console.error(`${i+1}. ${item.file}\n   ${item.line} (${item.type})\n   Erreur: ${item.error}\n`);
  });
  process.exit(1);
} else {
  console.log('\x1b[32m%s\x1b[0m', `✅ ${stats.files} fichiers vérifiés - aucun problème détecté`);
  process.exit(0);
}
