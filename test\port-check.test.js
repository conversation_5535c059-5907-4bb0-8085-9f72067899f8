import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock du module net (doit être déclaré avant l'import)
vi.mock('net', () => {
  const mockServer = {
    once: vi.fn(),
    listen: vi.fn(),
    close: vi.fn()
  };

  return {
    createServer: vi.fn(() => mockServer)
  };
});

import { createServer } from 'net';

// Fonction checkPort isolée pour le test
async function checkPort(port) {
  return new Promise(resolve => {
    const tester = createServer()
      .once('error', () => resolve(true))
      .once('listening', () => {
        tester.close();
        resolve(false);
      })
      .listen(port);
  });
}

describe('Port Check Tests', () => {
  let mockServer;

  beforeEach(() => {
    // Réinitialiser les mocks
    vi.clearAllMocks();

    // Récupérer le mock server
    mockServer = createServer();

    // Configuration par défaut des mocks
    mockServer.once.mockImplementation((event, callback) => {
      return mockServer;
    });
    mockServer.listen.mockReturnValue(mockServer);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should detect free port', async () => {
    // Simuler un port libre (le callback 'listening' est appelé)
    mockServer.once.mockImplementation((event, callback) => {
      if (event === 'listening') {
        setTimeout(() => callback(), 10);
      }
      return mockServer;
    });

    const result = await checkPort(7000);

    // Vérifier que le résultat est false (port libre)
    expect(result).toBe(false);

    // Vérifier que les méthodes ont été appelées
    expect(createServer).toHaveBeenCalled();
    expect(mockServer.once).toHaveBeenCalledWith('error', expect.any(Function));
    expect(mockServer.once).toHaveBeenCalledWith('listening', expect.any(Function));
    expect(mockServer.listen).toHaveBeenCalledWith(7000);
  });

  it('should detect occupied port', async () => {
    // Simuler un port occupé (le callback 'error' est appelé)
    mockServer.once.mockImplementation((event, callback) => {
      if (event === 'error') {
        setTimeout(() => callback(new Error('EADDRINUSE')), 10);
      }
      return mockServer;
    });

    const result = await checkPort(7000);

    // Vérifier que le résultat est true (port occupé)
    expect(result).toBe(true);

    // Vérifier que les méthodes ont été appelées
    expect(createServer).toHaveBeenCalled();
    expect(mockServer.once).toHaveBeenCalledWith('error', expect.any(Function));
    expect(mockServer.once).toHaveBeenCalledWith('listening', expect.any(Function));
    expect(mockServer.listen).toHaveBeenCalledWith(7000);
  });
});
