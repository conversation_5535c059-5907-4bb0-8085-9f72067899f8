# Documentation des tests

Ce document présente les bonnes pratiques et conventions pour les tests dans le projet GDevAI.

## Table des Matières
1. [Structure des tests](#structure-des-tests)
2. [Types de tests](#types-de-tests)
3. [Mocks et stubs](#mocks-et-stubs)
4. [Assertions](#assertions)
5. [Couverture de code](#couverture-de-code)
6. [Déboguer les tests](#déboguer-les-tests)
7. [Intégration continue](#intégration-continue)
8. [Gestion des Ports](#gestion-des-ports)
9. [Tests Isolés vs Tests d'Intégration](#tests-isolés-vs-tests-dintégration)
10. [Bonnes Pratiques pour les Tests de Modules](#bonnes-pratiques-pour-les-tests-de-modules)
11. [Résolution des Problèmes Courants](#résolution-des-problèmes-courants)
12. [Exemples Concrets](#exemples-concrets)

## Structure des tests

### Organisation des fichiers

- `test/` - Répertoire principal des tests
  - `unit/` - Tests unitaires
  - `integration/` - Tests d'intégration
  - `e2e/` - Tests end-to-end
  - `mocks/` - Mocks réutilisables
  - `fixtures/` - Données de test
  - `utils/` - Utilitaires pour les tests

### Conventions de nommage

- Fichiers de test : `[nom-du-fichier-testé].test.js`
- Mocks : `[nom-du-module]Mocks.js`
- Fixtures : `[nom-du-module]Fixtures.js`

## Types de tests

### Tests unitaires

Testent des fonctions ou classes isolées avec des dépendances mockées.

```javascript
// Exemple de test unitaire
describe('MessageStorage', () => {
  test('should add message correctly', () => {
    const storage = new MessageStorage();
    const message = storage.addMessage('user', 'Hello');

    expect(message).toHaveProperty('id');
    expect(message.text).toBe('Hello');
  });
});
```

### Tests d'intégration

Testent l'interaction entre plusieurs composants.

```javascript
// Exemple de test d'intégration
describe('ChatService RBAC Integration', () => {
  test('should return error when permission denied', async () => {
    const rbac = new RBACService();
    const chatService = new ChatService(aiService, logger, storage, rbac);

    const response = await chatService.processMessage('test', { username: 'user' });

    expect(response.data.error.code).toBe('CHAT_001');
  });
});
```

### Tests end-to-end

Testent le système complet de bout en bout.

```javascript
// Exemple de test e2e
describe('Chat API', () => {
  test('should process message and return response', async () => {
    const response = await request(app)
      .post('/api/chat')
      .send({ message: 'Hello' })
      .set('Authorization', `Bearer ${token}`);

    expect(response.status).toBe(200);
    expect(response.body.data).toHaveProperty('text');
  });
});
```

## Mocks et stubs

### Principes Généraux
- Définir les mocks **avant** les imports pour éviter les problèmes de hoisting
- Utiliser `vi.mock()` pour les modules entiers
- Utiliser `vi.spyOn()` pour les méthodes spécifiques
- Réinitialiser les mocks après chaque test avec `vi.clearAllMocks()`

### Création de mocks

```javascript
// Exemple de création de mock
export const createChatServiceMocks = () => ({
  aiService: {
    generateText: vi.fn().mockResolvedValue('Mock response')
  },
  logger: {
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn(),
    error: vi.fn()
  }
});
```

### Utilisation des mocks

```javascript
// Exemple d'utilisation de mock
import { createChatServiceMocks } from './mocks/chatServiceMocks';

describe('ChatService', () => {
  let mocks;
  let chatService;

  beforeEach(() => {
    mocks = createChatServiceMocks();
    chatService = new ChatService(
      mocks.aiService,
      mocks.logger,
      new MessageStorage(),
      mocks.rbac
    );
  });

  // Tests...
});
```

### Mocks pour les Modules ES
Pour les modules ES, utiliser la syntaxe suivante :
```javascript
vi.mock('../path/to/module.js', () => {
  return {
    default: mockObject,
    namedExport: mockFunction
  };
});
```

### Mocks avec importOriginal
Pour conserver certaines fonctionnalités du module original :
```javascript
vi.mock('../path/to/module.js', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    functionToMock: vi.fn()
  };
});
```

## Assertions

### Bonnes pratiques

1. **Précision** : Testez un seul comportement par test
2. **Isolation** : Évitez les dépendances entre tests
3. **Lisibilité** : Nommez clairement vos tests
4. **Couverture** : Testez les cas normaux et les cas d'erreur

### Exemples d'assertions

```javascript
// Assertions de base
expect(result).toBe(expected);
expect(object).toHaveProperty('property');
expect(array).toContain(item);

// Assertions sur les objets
expect(response).toMatchObject({
  status: 'success',
  data: {
    text: expect.any(String)
  }
});

// Assertions sur les fonctions mockées
expect(mockFunction).toHaveBeenCalled();
expect(mockFunction).toHaveBeenCalledWith(arg1, arg2);
```

## Couverture de code

### Configuration

La couverture de code est configurée dans `vitest.config.js` :

```javascript
export default defineConfig({
  test: {
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html', 'json', 'lcov'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/**',
        'test/**',
        '**/*.test.js',
        '**/*.config.js',
      ],
    },
  },
});
```

### Exécution

Pour exécuter les tests avec couverture :

```bash
npm run test:coverage
```

### Objectifs de couverture

- Lignes : > 80%
- Branches : > 75%
- Fonctions : > 85%
- Statements : > 80%

## Déboguer les tests

### Logs de débogage

Utilisez le mode debug pour voir les logs détaillés :

```bash
npm run test:debug
```

### Tests isolés

Pour exécuter un seul fichier de test :

```bash
npx vitest run test/path/to/file.test.js
```

## Intégration continue

Les tests sont exécutés automatiquement lors des commits et des push via Husky :

```json
"husky": {
  "hooks": {
    "pre-commit": "lint-staged",
    "pre-push": "npm test"
  }
}
```

## Gestion des Ports

### Problèmes Courants
- **EADDRINUSE** : Port déjà utilisé par un autre processus
- **Conflits de Port** : Plusieurs tests essayant d'utiliser le même port
- **Ports Bloqués** : Ports qui restent bloqués après l'exécution des tests
- **Tests Parallèles** : Plusieurs tests s'exécutant en parallèle et tentant d'utiliser le même port

### Solutions Détaillées

#### 1. Utiliser des Ports Dynamiques

La meilleure approche consiste à laisser le système d'exploitation attribuer un port disponible automatiquement :

```javascript
// Utiliser un port disponible aléatoire
const server = app.listen(0, () => {
  const port = server.address().port;
  console.log(`Serveur démarré sur le port ${port}`);
  // Utiliser le port attribué pour les tests
});
```

Cette approche présente plusieurs avantages :
- Évite les conflits de port
- Fonctionne même si certains ports sont déjà utilisés
- Permet l'exécution parallèle des tests

#### 2. Mocker Complètement le Serveur

Pour les tests unitaires, il est souvent préférable de ne pas démarrer de serveur réel :

```javascript
// Remplacer la méthode startServer pour éviter de démarrer un vrai serveur
core.startServer = vi.fn().mockImplementation(() => {
  // Simuler un serveur sans réellement en démarrer un
  core.server = {
    address: () => ({ port: 3000 }),
    close: (callback) => {
      if (callback) callback();
    }
  };
  return core.server;
});
```

Avantages de cette approche :
- Tests beaucoup plus rapides
- Pas de problèmes de port
- Isolation complète des tests

#### 3. Utiliser des Mocks pour HTTP/HTTPS

Pour une isolation encore plus complète, mocker les modules HTTP/HTTPS :

```javascript
// Mock pour le module http
vi.mock('http', () => ({
  createServer: vi.fn(() => ({
    listen: vi.fn((port, callback) => {
      if (callback) callback();
      return {
        address: () => ({ port }),
        close: vi.fn(cb => cb && cb())
      };
    }),
    close: vi.fn(cb => cb && cb())
  }))
}));

// Mock pour le module https
vi.mock('https', () => ({
  createServer: vi.fn(() => ({
    listen: vi.fn((port, callback) => {
      if (callback) callback();
      return {
        address: () => ({ port }),
        close: vi.fn(cb => cb && cb())
      };
    }),
    close: vi.fn(cb => cb && cb())
  }))
}));
```

#### 4. Utiliser un Gestionnaire de Ports Dédié

Pour les tests qui nécessitent absolument des ports réels, créer un gestionnaire de ports :

```javascript
// Gestionnaire de ports pour les tests
class TestPortManager {
  constructor() {
    this.usedPorts = new Set();
    this.basePort = 8000;
  }

  // Obtenir un port disponible
  getPort() {
    let port = this.basePort;
    while (this.usedPorts.has(port)) {
      port++;
    }
    this.usedPorts.add(port);
    return port;
  }

  // Libérer un port après utilisation
  releasePort(port) {
    this.usedPorts.delete(port);
  }

  // Libérer tous les ports
  releaseAllPorts() {
    this.usedPorts.clear();
  }
}

// Utilisation
const portManager = new TestPortManager();
const port = portManager.getPort();
// Utiliser le port pour le test
// ...
portManager.releasePort(port);
```

#### 5. Nettoyage Approprié des Serveurs

Toujours s'assurer que les serveurs sont correctement fermés après les tests :

```javascript
let server;

beforeEach(() => {
  server = app.listen(0);
});

afterEach((done) => {
  if (server) {
    server.close(done);
  } else {
    done();
  }
});
```

## Tests Isolés vs Tests d'Intégration

### Tests Unitaires Isolés

#### Principes Fondamentaux
- **Objectif** : Tester une seule unité de code à la fois de manière isolée
- **Portée** : Limitée à une fonction, une classe ou un module spécifique
- **Dépendances** : Toutes les dépendances externes sont mockées
- **Rapidité** : Exécution très rapide (millisecondes)
- **Stabilité** : Très stables car indépendants de l'environnement externe

#### Avantages des Tests Isolés
1. **Rapidité d'exécution** : Les tests isolés s'exécutent très rapidement
2. **Facilité de débogage** : Quand un test échoue, l'origine du problème est facile à identifier
3. **Indépendance** : Chaque test est indépendant des autres
4. **Couverture ciblée** : Permet de tester des cas limites et des chemins d'exécution spécifiques
5. **Feedback immédiat** : Permet aux développeurs d'obtenir un retour rapide sur leurs modifications

#### Exemple Complet de Test Isolé pour un Module

```javascript
// Exemple de test isolé pour le module GDevelop
import { describe, beforeEach, afterEach, test, expect, vi } from 'vitest';

// Créer des mocks pour les dépendances
const mockGDevelopModule = {
  process: vi.fn()
};

const mockLogger = {
  info: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
  resetLoggerMocks: function() {
    this.info.mockReset();
    this.error.mockReset();
    this.warn.mockReset();
    this.debug.mockReset();
  }
};

// Mock du router qui utilise le module GDevelop
const mockRouter = {
  modules: new Map([['gdevelop', mockGDevelopModule]]),
  routeMessage: vi.fn()
};

describe('GDevelop Module Tests (Isolated)', () => {
  beforeEach(() => {
    // Réinitialiser les mocks avant chaque test
    vi.clearAllMocks();
    mockLogger.resetLoggerMocks();

    // Configurer le comportement du router
    mockRouter.routeMessage.mockImplementation((module, message) => {
      if (module === 'gdevelop') {
        return mockGDevelopModule.process(message);
      }
      throw new Error(`Module not found: ${module}`);
    });
  });

  afterEach(() => {
    // Nettoyer après chaque test
    vi.clearAllMocks();
  });

  test('should register GDevelop module', () => {
    expect(mockRouter.modules.has('gdevelop')).toBeTruthy();
  });

  test('should process getProject action', async () => {
    // Configurer le mock pour retourner un projet
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      project: {
        id: 'test123',
        name: 'Mock project test123',
        version: '1.0.0'
      }
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getProject',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.project).toEqual({
      id: 'test123',
      name: 'Mock project test123',
      version: '1.0.0'
    });

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'getProject',
      projectId: 'test123'
    });
  });

  test('should handle errors properly', async () => {
    // Configurer le mock pour lancer une erreur
    const error = new Error('Test error');
    mockGDevelopModule.process.mockRejectedValueOnce(error);

    // Appeler la fonction et vérifier qu'elle lance une erreur
    await expect(
      mockRouter.routeMessage('gdevelop', {
        action: 'invalidAction',
        projectId: 'test123'
      })
    ).rejects.toThrow('Test error');
  });
});
```

### Tests d'Intégration

#### Principes Fondamentaux
- **Objectif** : Tester l'interaction entre plusieurs composants
- **Portée** : Plusieurs modules, classes ou fonctions qui interagissent ensemble
- **Dépendances** : Seules les dépendances externes au système testé sont mockées
- **Rapidité** : Plus lents que les tests unitaires mais plus rapides que les tests E2E
- **Stabilité** : Moins stables que les tests unitaires mais plus que les tests E2E

#### Avantages des Tests d'Intégration
1. **Validation des interactions** : Vérifie que les composants fonctionnent correctement ensemble
2. **Détection des problèmes d'interface** : Identifie les problèmes aux frontières entre les composants
3. **Couverture plus large** : Teste des scénarios plus complets et réalistes
4. **Confiance accrue** : Donne plus de confiance dans le fonctionnement global du système
5. **Réduction des mocks** : Moins de mocks signifie des tests plus proches de la réalité

#### Exemple de Test d'Intégration

```javascript
// Exemple de test d'intégration pour le module GDevelop avec Core
import { describe, beforeAll, afterAll, beforeEach, test, expect, vi } from 'vitest';
import Core from '../core/index.js';
import Router from '../core/router.js';
import GDevelopAPI from '../integrations/gdevelop/api.js';

// Mock uniquement les appels API externes
vi.mock('../integrations/gdevelop/api.js', () => {
  return {
    default: class MockGDevelopAPI {
      async getProject(id) {
        return {
          id,
          name: `Project ${id}`,
          version: '1.0.0'
        };
      }

      async updateProject(id, data) {
        return {
          id,
          ...data,
          updatedAt: new Date().toISOString()
        };
      }
    }
  };
});

describe('GDevelop Module Integration', () => {
  let core;
  let router;

  beforeAll(async () => {
    // Initialiser Core avec un port dynamique pour éviter les conflits
    core = new Core({ port: 0 });
    await core.initialize();

    // Obtenir le router de Core
    router = core.router;
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterAll(async () => {
    // Nettoyer après tous les tests
    await core.cleanup();
  });

  test('should process getProject through Core and Router', async () => {
    const result = await router.routeMessage('gdevelop', {
      action: 'getProject',
      projectId: 'test123'
    });

    expect(result.status).toBe('success');
    expect(result.project).toBeDefined();
    expect(result.project.id).toBe('test123');
  });
});
```

### Quand Utiliser Chaque Type de Test

#### Utiliser des Tests Unitaires Isolés pour :
- Tester des algorithmes complexes
- Valider la logique métier
- Tester des cas limites et des chemins d'exécution spécifiques
- Obtenir un feedback rapide pendant le développement
- Tester des composants avec beaucoup de dépendances

#### Utiliser des Tests d'Intégration pour :
- Valider les interactions entre les composants
- Tester les flux de données à travers plusieurs modules
- Vérifier que les interfaces entre les composants fonctionnent correctement
- Tester des scénarios utilisateur de bout en bout
- Valider l'intégration avec des services externes (avec des mocks)

## Bonnes Pratiques pour les Tests de Modules

### Éviter les Dépendances à Core
- **Problème** : L'initialisation de Core dans les tests unitaires peut causer des conflits de port et ralentir les tests
- **Solution** : Utiliser des mocks pour simuler le comportement de Core
- **Avantages** : Tests plus rapides, plus stables et plus isolés

#### Exemple de Test Sans Dépendance à Core

```javascript
// Test sans dépendance à Core
import { describe, test, expect, vi } from 'vitest';

// Mock complet du module GDevelop
const mockGDevelopModule = {
  process: vi.fn(),
  initialize: vi.fn().mockResolvedValue(undefined)
};

describe('GDevelop Module Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should process getProject action', async () => {
    // Configurer le mock
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      project: {
        id: 'test123',
        name: 'Test Project'
      }
    });

    // Appeler directement la méthode du module
    const result = await mockGDevelopModule.process({
      action: 'getProject',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.project.id).toBe('test123');
    expect(mockGDevelopModule.process).toHaveBeenCalledTimes(1);
  });
});
```

### Structuration des Tests par Fonctionnalité

Une bonne organisation des tests améliore leur lisibilité et leur maintenabilité :

- **Regrouper par fonctionnalité** : Organiser les tests par fonctionnalité plutôt que par structure de code
- **Descriptions claires** : Utiliser des descriptions précises pour chaque groupe de tests
- **Hiérarchie logique** : Structurer les tests en groupes et sous-groupes logiques

#### Exemple de Structure Optimale

```javascript
import { describe, beforeEach, afterEach, test, expect, vi } from 'vitest';

describe('GDevelop Module', () => {
  // Configuration commune
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // Regroupement par fonctionnalité : Gestion des projets
  describe('Project Management', () => {
    test('should get project details', async () => {
      // Test pour getProject
      const mockGetProject = vi.fn().mockResolvedValue({
        id: 'test123',
        name: 'Test Project'
      });

      const result = await mockGetProject('test123');

      expect(result.id).toBe('test123');
      expect(result.name).toBe('Test Project');
    });

    test('should update project', async () => {
      // Test pour updateProject
      const mockUpdateProject = vi.fn().mockResolvedValue({
        id: 'test123',
        name: 'Updated Project',
        updatedAt: expect.any(String)
      });

      const result = await mockUpdateProject('test123', { name: 'Updated Project' });

      expect(result.name).toBe('Updated Project');
      expect(result).toHaveProperty('updatedAt');
    });
  });

  // Regroupement par fonctionnalité : Gestion des erreurs
  describe('Error Handling', () => {
    test('should handle invalid project data', async () => {
      // Test pour la gestion des erreurs
      const mockProcessInvalid = vi.fn().mockRejectedValue(
        new Error('Invalid project data')
      );

      await expect(mockProcessInvalid({})).rejects.toThrow('Invalid project data');
    });

    test('should handle network errors', async () => {
      // Test pour les erreurs réseau
      const mockProcessWithNetworkError = vi.fn().mockRejectedValue(
        new Error('Network error')
      );

      await expect(mockProcessWithNetworkError()).rejects.toThrow('Network error');
    });
  });
});
```

### Isolation Complète des Modules

Pour une isolation complète, il est recommandé de :

- **Mocker toutes les dépendances externes** : Fichiers, réseau, bases de données, etc.
- **Éviter les effets de bord** : S'assurer que chaque test est indépendant
- **Nettoyer après chaque test** : Utiliser `afterEach` pour réinitialiser l'état

## Résolution des Problèmes Courants

### Problèmes de Port (EADDRINUSE)

#### Symptômes
- Erreur `EADDRINUSE: address already in use :::50777`
- Tests qui échouent de manière aléatoire
- Tests qui fonctionnent individuellement mais échouent ensemble

#### Solutions
1. **Utiliser des ports dynamiques** :
   ```javascript
   const server = app.listen(0); // Le système attribue un port disponible
   const port = server.address().port;
   ```

2. **Mocker complètement les serveurs** :
   ```javascript
   vi.mock('http', () => ({
     createServer: vi.fn().mockReturnValue({
       listen: vi.fn((port, callback) => {
         callback && callback();
         return { address: () => ({ port }) };
       }),
       close: vi.fn(cb => cb && cb())
     })
   }));
   ```

3. **Nettoyer correctement après les tests** :
   ```javascript
   let server;

   beforeEach(() => {
     server = createServer();
   });

   afterEach((done) => {
     server && server.close(done);
   });
   ```

### Problèmes de Mocks

#### Symptômes
- Erreur `No "functionName" export is defined on the "path/to/module" mock`
- Erreur `Cannot access '__vi_import_X__' before initialization`
- Les mocks ne fonctionnent pas comme prévu

#### Solutions
1. **Définir les mocks avant les imports** :
   ```javascript
   // ✅ Correct
   import { vi } from 'vitest';

   vi.mock('../path/to/module', () => ({
     default: mockObject
   }));

   // Imports après les mocks
   import { someFunction } from '../path/to/module';
   ```

2. **Utiliser importOriginal pour les mocks partiels** :
   ```javascript
   vi.mock('../path/to/module', async (importOriginal) => {
     const actual = await importOriginal();
     return {
       ...actual,
       functionToMock: vi.fn()
     };
   });
   ```

3. **Vérifier les chemins d'importation** :
   - Utiliser des chemins relatifs corrects
   - Respecter les alias définis dans la configuration
   - Vérifier les extensions de fichier (.js, .cjs, etc.)

### Problèmes d'Asynchronisme

#### Symptômes
- Tests qui passent parfois et échouent d'autres fois
- Erreurs de timeout
- Promesses non résolues

#### Solutions
1. **Utiliser async/await correctement** :
   ```javascript
   // ✅ Correct
   test('async test', async () => {
     const result = await someAsyncFunction();
     expect(result).toBe(expected);
   });
   ```

2. **Augmenter les timeouts pour les tests lents** :
   ```javascript
   // Augmenter le timeout pour un test spécifique
   test('slow test', async () => {
     // ...
   }, 10000); // 10 secondes

   // Ou globalement
   vi.setConfig({ testTimeout: 10000 });
   ```

3. **Utiliser des mocks pour les opérations asynchrones** :
   ```javascript
   const mockAsyncOperation = vi.fn().mockResolvedValue('result');
   ```

## Exemples Concrets

### Exemple 1: Test Isolé pour GDevelop Module

Ce test montre comment tester le module GDevelop de manière complètement isolée :

```javascript
/**
 * Test pour le module GDevelop
 * Version complètement isolée sans dépendance à Core
 */
import { vi, describe, beforeEach, afterEach, test, expect } from 'vitest';
import universalLogger from './__mocks__/universal-logger.js';

// Mock pour le logger
vi.mock('#logger', () => {
  return {
    default: universalLogger,
    logger: universalLogger,
    createLogger: universalLogger.createLogger
  };
});

// Créer des mocks pour les modules nécessaires
const mockGDevelopModule = {
  process: vi.fn()
};

// Mock du router
const mockRouter = {
  modules: new Map([['gdevelop', mockGDevelopModule]]),
  routeMessage: vi.fn()
};

describe('GDevelop Module Tests', () => {
  beforeEach(() => {
    // Réinitialiser les mocks avant chaque test
    universalLogger.resetLoggerMocks();
    vi.clearAllMocks();

    // Configurer le mock du router
    mockRouter.routeMessage.mockImplementation((module, message) => {
      if (module === 'gdevelop') {
        return mockGDevelopModule.process(message);
      }
      throw new Error(`Module not found: ${module}`);
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  test('should process getProject action', async () => {
    // Configurer le mock pour retourner un projet
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      project: {
        id: 'test123',
        name: 'Mock project test123'
      }
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getProject',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.project.id).toBe('test123');

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'getProject',
      projectId: 'test123'
    });
  });
});
```

### Exemple 2: Gestion des Ports dans les Tests

Cet exemple montre comment gérer correctement les ports dans les tests :

```javascript
import { describe, beforeEach, afterEach, test, expect, vi } from 'vitest';
import { createServer } from 'http';
import { checkPortIsFree } from '../utils/portUtils';

// Mock pour net
vi.mock('net', () => ({
  createServer: vi.fn(() => ({
    listen: vi.fn((port, callback) => {
      callback && callback();
      return {
        address: () => ({ port }),
        close: vi.fn(cb => cb && cb())
      };
    }),
    close: vi.fn(cb => cb && cb())
  }))
}));

describe('Port Management Tests', () => {
  let server;

  afterEach((done) => {
    if (server) {
      server.close(() => {
        server = null;
        done();
      });
    } else {
      done();
    }
  });

  test('should use dynamic port allocation', (done) => {
    server = createServer();
    server.listen(0, () => {
      const port = server.address().port;
      expect(port).toBeGreaterThan(0);
      done();
    });
  });

  test('should detect if port is free', async () => {
    const isFree = await checkPortIsFree(8000);
    expect(typeof isFree).toBe('boolean');
  });
});
```
