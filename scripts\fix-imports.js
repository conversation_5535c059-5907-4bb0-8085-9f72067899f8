import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const PROJECT_ROOT = path.join(__dirname, '..');

// Extensions supportées
const ALLOWED_EXTENSIONS = new Set(['.js', '.ts', '.mjs', '.cjs']);

async function processFile(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Convertir require en import
  content = content.replace(
    /const (\w+) = require\(['"]([^'"]+)['"]\);/g,
    'import $1 from \'$2\';'
  );

  // Corriger les extensions manquantes
  content = content.replace(
    /from\s+['"]([^'"]+)(?<!\.(js|ts|mjs|cjs))['"]/g,
    (match, importPath) => {
      const fullPath = path.join(path.dirname(filePath), importPath);
      
      for (const ext of ALLOWED_EXTENSIONS) {
        if (fs.existsSync(`${fullPath}${ext}`)) {
          modified = true;
          return `from '${importPath}${ext}'`;
        }
      }

      // Vérifier les index files
      for (const ext of ALLOWED_EXTENSIONS) {
        const indexPath = path.join(fullPath, `index${ext}`);
        if (fs.existsSync(indexPath)) {
          modified = true;
          return `from '${importPath}/index${ext}'`;
        }
      }

      return match;
    }
  );

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`Fixed imports in ${path.relative(PROJECT_ROOT, filePath)}`);
  }
}

function walkDirectory(dir) {
  fs.readdirSync(dir).forEach(file => {
    const fullPath = path.join(dir, file);
    const stat = fs.statSync(fullPath);

    if (stat.isDirectory() && !fullPath.includes('node_modules')) {
      walkDirectory(fullPath);
    } else if (/\.(js|ts)$/.test(file)) {
      processFile(fullPath);
    }
  });
}

// Exécution
walkDirectory(PROJECT_ROOT);
console.log('✅ Import fixes complete');
