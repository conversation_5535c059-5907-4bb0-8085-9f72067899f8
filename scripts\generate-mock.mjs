#!/usr/bin/env node
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { analyzeExports } from './ast-analyzer.mjs';
import MockLogger from './utils/mockLogger.mjs';

class MockGenerator {
  constructor() {
    this.logger = new MockLogger('mock-generator');
    this.retryAttempts = 3;
    this.retryDelay = 1000;
  }

  async generate(modulePath) {
    try {
      this.validatePath(modulePath);
      const exports = await this.analyzeWithRetry(modulePath);
      const content = this.buildMockContent(exports, modulePath);
      await this.writeWithRetry(modulePath, content);
      this.logger.success(`Mock generated for ${modulePath}`);
      return true;
    } catch (error) {
      this.logger.error(error);
      return false;
    }
  }

  validatePath(modulePath) {
    if (!modulePath) {
      throw new Error('Module path is required');
    }
    if (!fs.existsSync(path.resolve(process.cwd(), modulePath))) {
      throw new Error(`File not found: ${modulePath}`);
    }
  }

  async analyzeWithRetry(modulePath, attempt = 1) {
    try {
      return analyzeExports(modulePath);
    } catch (error) {
      if (attempt >= this.retryAttempts) throw error;
      await new Promise(res => setTimeout(res, this.retryDelay));
      return this.analyzeWithRetry(modulePath, attempt + 1);
    }
  }

  buildMockContent({ defaultExport, namedExports }, modulePath) {
    let mockContent = `import { createESMock } from '../test/__mocks__/esm-template.mjs';\n\n`;
    
    const mockImplementation = {};
    namedExports.forEach(exportName => {
      mockImplementation[exportName] = `vi.fn()`;
    });

    mockContent += `const mockImplementation = ${JSON.stringify(mockImplementation, null, 2)
      .replace(/"vi.fn()"/g, 'vi.fn()')};\n\n`;

    if (defaultExport) {
      mockContent += `export default createESMock('${modulePath}', mockImplementation);\n`;
    } else {
      mockContent += `export default createESMock('${modulePath}', {\n  ...mockImplementation\n});\n`;
    }

    return mockContent;
  }

  async writeWithRetry(modulePath, content, attempt = 1) {
    try {
      const fileName = path.basename(modulePath).replace('.mjs', '.mock.mjs');
      const mockPath = path.join(process.cwd(), 'test/__mocks__', fileName);
      
      fs.mkdirSync(path.dirname(mockPath), { recursive: true });
      fs.writeFileSync(mockPath, content);
    } catch (error) {
      if (attempt >= this.retryAttempts) throw error;
      await new Promise(res => setTimeout(res, this.retryDelay));
      return this.writeWithRetry(modulePath, content, attempt + 1);
    }
  }
}

// Usage
const generator = new MockGenerator();
const modulePath = process.argv[2];

if (modulePath) {
  generator.generate(modulePath)
    .then(success => process.exit(success ? 0 : 1))
    .catch(() => process.exit(1));
} else {
  console.error('Usage: node generate-mock.mjs <module-path>');
  process.exit(1);
}
