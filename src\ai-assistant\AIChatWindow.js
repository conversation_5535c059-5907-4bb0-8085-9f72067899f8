import ConversationMemory from './ConversationMemory.js';
import { buildPrompt } from './promptTemplates.js';
import deepseekService from '../backend/services/deepseekService.js';

export default class AIChatWindow {
  constructor() {
    this.memory = new ConversationMemory();
    this.isProcessing = false;
  }

  async sendMessage(message) {
    if (this.isProcessing) return;
    this.isProcessing = true;

    try {
      // Ajoute le message utilisateur à l'historique
      this.memory.addMessage('user', message);

      // Construit le prompt avec le contexte
      const context = this.memory.getContext();
      const prompt = buildPrompt(message, context);

      // Envoie la requête à l'API Deepseek
      const response = await deepseekService.chatCompletion([
        {
          role: 'user',
          content: prompt
        }
      ]);

      // Ajoute la réponse à l'historique
      this.memory.addMessage('assistant', response);

      return response;
    } finally {
      this.isProcessing = false;
    }
  }

  clearHistory() {
    this.memory.clear();
  }
}
