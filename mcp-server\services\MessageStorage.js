class MessageStorage {
    constructor() {
        this.messages = [];
        this.maxHistory = 100; // Nombre max de messages conservés
    }

    addMessage(sender, text) {
        const message = {
            id: Date.now(),
            timestamp: new Date().toISOString(),
            sender,
            text
        };
        this.messages.push(message);

        // Vérifier si la limite d'historique est dépassée
        if (this.messages.length > this.maxHistory) {
            // Supprimer les messages les plus anciens pour respecter la limite
            this.messages = this.messages.slice(-this.maxHistory);
        }

        return message;
    }

    getHistory() {
        return this.messages;
    }

    removeOldestMessages(count) {
        if (count >= this.messages.length) {
            this.messages = [];
        } else {
            this.messages = this.messages.slice(count);
        }
    }

    clear() {
        this.messages = [];
    }
}

export default MessageStorage;
