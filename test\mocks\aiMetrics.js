import { vi } from 'vitest';

const DEBUG_MOCKS = process.env.DEBUG_MOCKS === 'true';

export const logAIMetrics = vi.fn().mockImplementation((params) => {
  if (DEBUG_MOCKS) console.log('[aiMetrics] logAIMetrics:', params);
  return params;
});

export const trackRequest = vi.fn().mockImplementation((params) => {
  if (DEBUG_MOCKS) console.log('[aiMetrics] trackRequest:', params);
  return {
    ...params,
    timestamp: new Date().toISOString()
  };
});

export const getAIStats = vi.fn().mockResolvedValue({
  avgResponseTime: 0,
  successRate: 0,
  totalTokens: 0
});

export default {
  logAIMetrics,
  trackRequest,
  getAIStats
};
