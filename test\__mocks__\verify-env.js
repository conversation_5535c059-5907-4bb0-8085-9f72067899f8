/**
 * Mock pour verify-env.js
 */
import { vi } from 'vitest';
import fs from 'fs';

export const verifyEnv = vi.fn().mockImplementation((vars = [], options = {}) => {
  const result = {};
  const strict = options.strict !== false;
  
  // Vérifier si les variables existent
  if (vars && vars.length > 0) {
    for (const varName of vars) {
      if (process.env[varName]) {
        result[varName] = process.env[varName];
      } else if (strict) {
        throw new Error(`Environment variable ${varName} is required`);
      }
    }
  }
  
  // Vérifier si le fichier de configuration existe
  if (options.configFile && !fs.existsSync(options.configFile)) {
    throw new Error(`Configuration file ${options.configFile} not found`);
  }
  
  // Vérifier si la clé privée existe
  if (options.keyFile && !fs.existsSync(options.keyFile)) {
    throw new Error(`Private key file ${options.keyFile} not found`);
  }
  
  return result;
});

export default {
  verifyEnv
};
