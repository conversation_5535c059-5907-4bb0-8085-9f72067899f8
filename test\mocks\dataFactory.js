// Factory de données de test minimaliste et contrôlée
export const mockDataFactory = {
  text: (prefix = 'msg') => `${prefix}_${Date.now()}_${Math.random().toString(36).slice(2, 6)}`,
  number: (min, max) => Math.floor(Math.random() * (max - min + 1)) + min,
  id: () => `id-${Math.random().toString(36).slice(2, 9)}`,
  errorCode: () => [400, 401, 403, 500][Math.floor(Math.random() * 4)],
  timestamp: () => new Date(Date.now() - Math.random() * 10000000).toISOString(),
  bool: () => Math.random() > 0.5
};
