# Guide d'API GDevAI

## Endpoints Principaux

### POST /core/chat
- **Description**: Endpoint principal pour les interactions chat
- **Format Requête**:
  ```json
  {
    "text": "Votre message"
  }
  ```
- **Exemple cURL**:
  ```bash
  curl -X POST http://localhost:3007/core/chat \
    -H "Content-Type: application/json" \
    -d "{\"text\":\"Votre message\"}"
  ```

## Authentification
Toutes les requêtes vers `/core/*` nécessitent un JWT valide dans le header:
```http
Authorization: Bearer <token>
```

## RBAC (Contrôle d'Accès Basé sur les Rôles)

### Configuration
Le système RBAC utilise le fichier `config/roles.json` qui définit :
- Les rôles et leurs permissions
- Les relations d'héritage entre rôles
- Les descriptions des permissions

Exemple de configuration :
```json
{
  "roles": {
    "admin": {
      "permissions": ["settings.update"],
      "inherits": []
    }
  }
}
```

### Utilisation
Dans les contrôleurs :
```javascript
router.get('/admin', checkPermission('settings.update'), adminController);
```

Vérification manuelle :
```javascript
if (hasPermission(user, 'content.edit')) {
  // Accès autorisé
}
```

### Bonnes Pratiques
- Limiter l'utilisation du wildcard `*`
- Préférer des permissions spécifiques
- Tester les héritages complexes

### Obtenir un token (exemple):
```bash
curl -X POST http://localhost:3007/auth/token \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"admin\",\"password\":\"secret\"}"
```

### Requête authentifiée:
```bash
curl -X POST http://localhost:3007/core/chat \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -d "{\"text\":\"Message protégé\"}"
```

## Bonnes Pratiques
1. Toujours utiliser des guillemets doubles pour les valeurs JSON
2. Échapper les guillemets dans les shells Windows
3. Vérifier que le serveur backend est démarré (port 3007)
4. Le frontend doit tourner sur le port 3002
5. Protéger les tokens JWT (ne pas les commiter)

## Configuration Initiale
Pour générer les clés RSA nécessaires à l'authentification (ES modules) :
```bash
node scripts/generateKeys.js
```

Le script :
1. Vérifie si les clés existent déjà dans config/
2. Génère une nouvelle paire de clés RSA 2048 bits si nécessaire
3. Sauvegarde les clés dans :
   - config/private.key (permissions restreintes)
   - config/public.key

Les clés sont utilisées pour :
- Signer les tokens JWT
- Vérifier les signatures
- Chiffrer les données sensibles

## Logging System (v0.4.2+)

### Bonnes Pratiques
- Utiliser le logger central (`src/utils/logger.js`)
- Niveaux recommandés :
  - `debug` : Développement seulement
  - `info` : Traces opérationnelles
  - `warn` : Problèmes non critiques
  - `error` : Erreurs systèmes

### Exemple d'implémentation
```javascript
// Dans un contrôleur
import { logger } from '../utils/logger';

logger.info('Requête reçue', { 
  endpoint: req.path,
  params: req.query 
});
```

## Gestion des Clés API

### Configuration des Clés Deepseek
- **CHAT_API_KEY** : Clé dédiée aux interactions chat utilisateur
- **AGENT_API_KEY** : Clé réservée aux fonctions autonomes/agents
- **Même endpoint** : `https://api.deepseek.com/v1`
- **Mêmes modèles accessibles** : `deepseek-chat` et `deepseek-reasoner`

### Bonnes Pratiques
```javascript
// Utilisation contextuelle
const client = request.type === 'AGENT' 
  ? new DeepseekAPI(process.env.AGENT_API_KEY)
  : new DeepseekAPI(process.env.CHAT_API_KEY);
```

### Configuration .env
```env
CHAT_API_KEY=sk_chat_123
AGENT_API_KEY=sk_agent_456
```

## Prochaines Améliorations
- [x] Ajouter l'authentification
- [x] Configurer la génération des clés
- [x] Système de logging unifié
- [ ] Documenter les erreurs possibles
- [ ] Exemples avancés de requêtes
