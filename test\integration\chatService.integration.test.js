import { describe, it, expect, vi, beforeEach } from 'vitest';
import ChatService from '../../mcp-server/services/chatService.js';
import MessageStorage from '../../mcp-server/services/MessageStorage.js';

// Mocks
const mockAIService = {
  generateText: vi.fn().mockResolvedValue('Mock AI response')
};

const mockLogger = {
  debug: vi.fn(),
  warn: vi.fn(),
  error: vi.fn()
};

const mockRBAC = {
  hasPermission: vi.fn()
};

describe('ChatService RBAC Integration', () => {
  let chatService;
  let storage;

  beforeEach(() => {
    vi.clearAllMocks();
    storage = new MessageStorage();
    chatService = new ChatService(
      mockAIService,
      mockLogger,
      storage,
      mockRBAC
    );
  });

  describe('Permission Checks', () => {
    const testUser = { username: 'testuser', roles: ['user'] };

    it('should return standard error when permission denied', async () => {
      mockRBAC.hasPermission.mockReturnValue(false);

      const response = await chatService.processMessage('test', testUser);

      // Verify error response structure
      expect(response.metadata.failed).toBe(true);
      expect(response.data.error.message).toBe('Permission denied');
      expect(response.data.error.code).toBe('CHAT_001');

      // Verify logging
      expect(mockLogger.error).toHaveBeenCalledWith(
        'Permission denied for user ' + testUser.username,
        { code: 'CHAT_001' }
      );
    });

    it('should allow access with proper permissions', async () => {
      mockRBAC.hasPermission.mockReturnValue(true);
      // S'assurer que le mock AIService est correctement configuré
      mockAIService.generateText.mockResolvedValue('Mock AI response');

      const result = await chatService.processMessage('test', testUser);

      expect(result).toBeDefined();
      expect(result.data.text).toMatch(/mock (ai )?response/i);
      // Vérifier que generateText a été appelé
      expect(mockAIService.generateText).toHaveBeenCalledWith('test');
      // Le warning ne devrait pas être appelé si la réponse AI est valide
      expect(mockLogger.warn).not.toHaveBeenCalledWith('Format de réponse AI invalide:', expect.anything());
    });

    it('should log RBAC denials', async () => {
      mockRBAC.hasPermission.mockReturnValue(false);

      try {
        await chatService.saveContext(testUser);
      } catch (e) {
        expect(mockLogger.warn).toHaveBeenCalledWith(
          `Accès refusé pour sauvegarde du contexte - user: ${testUser.username}`
        );
      }
    });
  });
});
