import React from 'react';
import { useMetrics } from '../../../services/metrics';

export default function MetricsDashboard() {
  const { responseTimes, errorRates, tokenUsage } = useMetrics();

  return (
    <div className="dashboard-metrics">
      <h2>Performance Metrics</h2>
      <div className="metrics-grid">
        <MetricCard 
          title="Response Time" 
          value={`${responseTimes.avg}ms`}
          trend={responseTimes.trend}
        />
        <MetricCard
          title="Error Rate"
          value={`${errorRates.percentage}%`}
          trend={errorRates.trend} 
        />
        <MetricCard
          title="Tokens Used"
          value={tokenUsage.total}
          trend={tokenUsage.trend}
        />
      </div>
    </div>
  );
}

function MetricCard({ title, value, trend }) {
  return (
    <div className="metric-card">
      <h3>{title}</h3>
      <div className="metric-value">{value}</div>
      <div className={`metric-trend ${trend}`}>{trend} arrow</div>
    </div>
  );
}
