import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const backupScript = path.join(__dirname, 'backup.js');
const logsDir = path.join(__dirname, '..', 'logs');

// Créer le dossier logs si nécessaire
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Commande pour créer la tâche planifiée (avec élévation de privilèges)
const createScheduledTask = () => {
  try {
    const command = `powershell -Command "Start-Process cmd -ArgumentList '/c schtasks /CREATE /SC DAILY /ST 02:00 /TN \"GDevAI Backup\" /TR \"node \\\"${backupScript}\\\" scheduled\" /RL HIGHEST /F' -Verb RunAs"`;
    execSync(command, { stdio: 'pipe' });
    console.log('✅ Tâche planifiée créée (02:00 quotidien)');
  } catch (error) {
    console.error('❌ Erreur création tâche:', error.message);
  }
};

// Commande pour créer la tâche au démarrage (avec élévation de privilèges)
const createStartupTask = () => {
  try {
    const command = `powershell -Command "Start-Process cmd -ArgumentList '/c schtasks /CREATE /SC ONSTART /TN \"GDevAI Backup Startup\" /TR \"node \\\"${backupScript}\\\" startup\" /RL HIGHEST /F' -Verb RunAs"`;
    execSync(command, { stdio: 'pipe' });
    console.log('✅ Tâche au démarrage créée');
  } catch (error) {
    console.error('❌ Erreur création tâche démarrage:', error.message);
  }
};

// Exécuter les configurations
createScheduledTask();
createStartupTask();
