import { vi, describe, it, expect, beforeEach } from 'vitest';

// Mock simple pour fs/promises
vi.mock('fs/promises', () => ({
  readFile: vi.fn(),
  writeFile: vi.fn(),
  access: vi.fn()
}));

// Mock simple pour fs
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
  writeFileSync: vi.fn()
}));

describe('Logging System', () => {
  const logPath = "C:\\Temp\\GdevaiLog\\core.log";

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should create log file when enabled', async () => {
    // Importer les mocks
    const { access } = await import('fs/promises');

    // Configurer le mock pour simuler que le fichier existe
    access.mockResolvedValue();

    // Vérifier que le fichier existe
    const fileExists = await access(logPath).then(() => true).catch(() => false);
    expect(fileExists).toBeTruthy();
    expect(access).toHaveBeenCalledWith(logPath);
  });

  it('should write logs in correct format', async () => {
    // Importer les mocks
    const { readFile, writeFile } = await import('fs/promises');

    // Préparer un contenu de log simulé
    const mockLogContent = [
      JSON.stringify({ level: 'info', message: 'Server started', timestamp: '2023-01-01T12:00:00.000Z' }),
      JSON.stringify({ level: 'debug', message: 'Debug message', timestamp: '2023-01-01T12:00:01.000Z' })
    ].join('\n');

    // Configurer le mock pour retourner le contenu simulé
    readFile.mockResolvedValue(mockLogContent);
    writeFile.mockResolvedValue();

    // Simuler l'écriture du fichier de log
    await writeFile(logPath, mockLogContent);

    // Lire le contenu du fichier
    const logContent = await readFile(logPath, 'utf8');
    const logLines = logContent.trim().split('\n');

    // Vérifier le format JSON de chaque ligne
    logLines.forEach(line => {
      expect(() => JSON.parse(line)).not.toThrow();
      const logEntry = JSON.parse(line);
      expect(logEntry).toHaveProperty('level');
      expect(logEntry).toHaveProperty('message');
      expect(logEntry).toHaveProperty('timestamp');
    });

    expect(writeFile).toHaveBeenCalledWith(logPath, mockLogContent);
    expect(readFile).toHaveBeenCalledWith(logPath, 'utf8');
  });
});
