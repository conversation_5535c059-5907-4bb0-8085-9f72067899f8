import { describe, it, expect, vi, beforeEach, afterEach, afterAll } from 'vitest';
import { __setPortOccupied, __clearAllPorts } from './__mocks__/net';
import { __setProcessOnPort, __clearAllProcesses } from './__mocks__/child_process';
import MockMCPServer from './mocks/mcpServer.js';

// Mock des modules natifs
vi.mock('net', () => import('./__mocks__/net').then(mod => mod.default));
vi.mock('child_process', () => import('./__mocks__/child_process').then(mod => mod.default));
vi.mock('fs');
vi.mock('dotenv');

// Mock de la configuration
vi.mock('./mcp-server/config/default.json', () => ({
  default: {
    server: {
      port: 5003
    },
    resources: {
      frontend: 'public'
    },
    tools: {
      chat: { enabled: false },
      deepseek: { enabled: false }
    }
  }
}), { virtual: true });

console.log('\n=== DÉBUT DES TESTS PORT MANAGER SIMPLIFIÉS ===\n');

describe('Port Manager Tests Simplifiés', () => {
  let mockServer;

  beforeEach(() => {
    // Réinitialiser les mocks
    vi.clearAllMocks();
    __clearAllPorts();
    __clearAllProcesses();

    // Créer une nouvelle instance du serveur mocké
    mockServer = new MockMCPServer();
  });

  afterEach(() => {
    // Nettoyer après chaque test
    __clearAllPorts();
    __clearAllProcesses();
  });

  afterAll(() => {
    // Nettoyer après tous les tests
    vi.restoreAllMocks();
    console.log('\n=== FIN DES TESTS PORT MANAGER SIMPLIFIÉS ===\n');
  });

  it('should initialize server with default port', () => {
    expect(mockServer.port).toBe(7000);
  });

  it('should handle port checking correctly', async () => {
    // Espionner la méthode checkPort
    const checkPortSpy = mockServer.checkPortSpy;

    // Appeler la méthode
    const isOccupied = await mockServer.checkPort(7000);

    // Vérifier que la méthode a été appelée
    expect(checkPortSpy).toHaveBeenCalledWith(7000);

    // Vérifier que le résultat est false (port libre)
    expect(isOccupied).toBe(false);
  });

  it('should handle port killing correctly', async () => {
    // Espionner la méthode forceKill
    const forceKillSpy = mockServer.forceKillSpy;

    // Appeler la méthode
    await mockServer.forceKill(7000);

    // Vérifier que la méthode a été appelée
    expect(forceKillSpy).toHaveBeenCalledWith(7000);
  });

  it('should start server on available port', async () => {
    // Simuler un port libre
    __clearAllPorts();

    // Espionner la méthode app.listen
    const listenSpy = vi.spyOn(mockServer.app, 'listen');

    // Démarrer le serveur
    await mockServer.start();

    // Vérifier que app.listen a été appelé avec le bon port
    expect(listenSpy).toHaveBeenCalledWith(7000, expect.any(Function));
  });

  it('should save port correctly', async () => {
    // Espionner la méthode savePort
    const savePortSpy = mockServer.savePortSpy;

    // Appeler la méthode
    await mockServer.savePort(8000);

    // Vérifier que la méthode a été appelée
    expect(savePortSpy).toHaveBeenCalledWith(8000);

    // Vérifier que le port a été mis à jour
    expect(mockServer.port).toBe(8000);
  });
});
