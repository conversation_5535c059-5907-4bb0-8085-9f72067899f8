import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import GDevelopPanel from '../../../../src/frontend/src/components/features/GDevelop/GDevelopPanel';
import { apiService } from '../../../../src/frontend/src/services/api';

// Mock the apiService
jest.mock('../../../../../src/frontend/src/services/api', () => ({
  apiService: {
    getGDevelopProjects: jest.fn(),
    getGDevelopProject: jest.fn(),
    getGDevelopEvents: jest.fn()
  }
}));

describe('GDevelopPanel Component', () => {
  const mockProjects = [
    { id: '1', title: 'Project 1' },
    { id: '2', title: 'Project 2' }
  ];

  beforeEach(() => {
    apiService.getGDevelopProjects.mockResolvedValue({
      projects: mockProjects
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('should render loading state initially', () => {
    render(<GDevelopPanel />);
    expect(screen.getByText('Chargement...')).toBeInTheDocument();
  });

  test('should render projects after loading', async () => {
    render(<GDevelopPanel />);
    
    await waitFor(() => {
      expect(apiService.getGDevelopProjects).toHaveBeenCalledTimes(1);
      expect(screen.getByText('GDevelop Integration')).toBeInTheDocument();
      expect(screen.getByText('Project 1')).toBeInTheDocument();
      expect(screen.getByText('Project 2')).toBeInTheDocument();
    });
  });

  test('should render error message when API fails', async () => {
    apiService.getGDevelopProjects.mockRejectedValue(new Error('API Error'));
    
    render(<GDevelopPanel />);
    
    await waitFor(() => {
      expect(screen.getByText(/Erreur:/)).toBeInTheDocument();
      expect(screen.getByText('API Error')).toBeInTheDocument();
    });
  });

  test('should render empty state when no projects', async () => {
    apiService.getGDevelopProjects.mockResolvedValue({ projects: [] });
    
    render(<GDevelopPanel />);
    
    await waitFor(() => {
      expect(screen.getByText('GDevelop Integration')).toBeInTheDocument();
      expect(screen.queryByText(/Project/)).not.toBeInTheDocument();
    });
  });
});
