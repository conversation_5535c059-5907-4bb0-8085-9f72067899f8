import { vi } from 'vitest';

/**
 * Mock standardisé pour le module fs
 * Ce mock est conçu pour être utilisé dans tous les tests
 */

// Système de fichiers en mémoire
const inMemoryFileSystem = new Map();

// Ajouter des fichiers par défaut au système de fichiers en mémoire
inMemoryFileSystem.set('config/default.json', JSON.stringify({
  server: {
    port: 5003,
    host: 'localhost'
  },
  modules: {
    chat: { enabled: true },
    gdevelop: { enabled: true },
    ai: { enabled: true }
  },
  security: {
    rbac: { enabled: true }
  }
}));

inMemoryFileSystem.set('config/private.key', 'MOCK_PRIVATE_KEY');
inMemoryFileSystem.set('config/public.key', 'MOCK_PUBLIC_KEY');
inMemoryFileSystem.set('config/test.json', '{}');

// Fonction utilitaire pour normaliser les chemins
function normalizePath(path) {
  // Extraire le chemin relatif à partir d'un chemin absolu
  // Par exemple, convertir "D:\Test\GDevAI\config\private.key" en "config/private.key"
  const relativePath = path.split(/[\/\\]/).slice(-2).join('/');
  return relativePath;
}

// Créer le mock
const fsMock = {
  // Méthodes synchrones
  readFileSync: vi.fn((path, options) => {
    console.log(`[FS MOCK] Tentative de lecture du fichier: ${path}`);

    // Normaliser le chemin
    const normalizedPath = normalizePath(path);
    console.log(`[FS MOCK] Chemin normalisé: ${normalizedPath}`);

    // Vérifier si le fichier existe dans notre système de fichiers en mémoire
    if (inMemoryFileSystem.has(normalizedPath)) {
      console.log(`[FS MOCK] Fichier trouvé: ${normalizedPath}`);
      return inMemoryFileSystem.get(normalizedPath);
    }

    // Vérifier si le chemin contient "private.key" ou "public.key"
    if (path.includes('private.key')) {
      console.log(`[FS MOCK] Retour de clé privée factice pour: ${path}`);
      return 'MOCK_PRIVATE_KEY';
    }

    if (path.includes('public.key')) {
      console.log(`[FS MOCK] Retour de clé publique factice pour: ${path}`);
      return 'MOCK_PUBLIC_KEY';
    }

    console.log(`[FS MOCK] Fichier non trouvé: ${path}`);
    throw new Error(`ENOENT: no such file or directory, open '${path}'`);
  }),

  writeFileSync: vi.fn((path, data) => {
    console.log(`[FS MOCK] Tentative d'écriture du fichier: ${path}`);

    // Normaliser le chemin
    const normalizedPath = normalizePath(path);
    console.log(`[FS MOCK] Chemin normalisé: ${normalizedPath}`);

    inMemoryFileSystem.set(normalizedPath, data);
    return true;
  }),

  existsSync: vi.fn((path) => {
    console.log(`[FS MOCK] Vérification de l'existence du fichier: ${path}`);

    // Normaliser le chemin
    const normalizedPath = normalizePath(path);
    console.log(`[FS MOCK] Chemin normalisé: ${normalizedPath}`);

    // Vérifier si le fichier existe dans notre système de fichiers en mémoire
    if (inMemoryFileSystem.has(normalizedPath)) {
      console.log(`[FS MOCK] Fichier trouvé: ${normalizedPath}`);
      return true;
    }

    // Vérifier si le chemin contient "private.key" ou "public.key"
    if (path.includes('private.key') || path.includes('public.key')) {
      console.log(`[FS MOCK] Fichier de clé trouvé: ${path}`);
      return true;
    }

    console.log(`[FS MOCK] Fichier non trouvé: ${path}`);
    return false;
  }),

  unlinkSync: vi.fn((path) => {
    console.log(`[FS MOCK] Tentative de suppression du fichier: ${path}`);

    // Normaliser le chemin
    const normalizedPath = normalizePath(path);
    console.log(`[FS MOCK] Chemin normalisé: ${normalizedPath}`);

    if (inMemoryFileSystem.has(normalizedPath)) {
      inMemoryFileSystem.delete(normalizedPath);
      return true;
    }
    throw new Error(`ENOENT: no such file or directory, unlink '${path}'`);
  }),

  // Méthodes asynchrones (promises)
  promises: {
    readFile: vi.fn(async (path, options) => {
      console.log(`[FS MOCK] Tentative de lecture asynchrone du fichier: ${path}`);

      // Normaliser le chemin
      const normalizedPath = normalizePath(path);
      console.log(`[FS MOCK] Chemin normalisé: ${normalizedPath}`);

      // Vérifier si le fichier existe dans notre système de fichiers en mémoire
      if (inMemoryFileSystem.has(normalizedPath)) {
        console.log(`[FS MOCK] Fichier trouvé: ${normalizedPath}`);
        return inMemoryFileSystem.get(normalizedPath);
      }

      // Vérifier si le chemin contient "private.key" ou "public.key"
      if (path.includes('private.key')) {
        console.log(`[FS MOCK] Retour de clé privée factice pour: ${path}`);
        return 'MOCK_PRIVATE_KEY';
      }

      if (path.includes('public.key')) {
        console.log(`[FS MOCK] Retour de clé publique factice pour: ${path}`);
        return 'MOCK_PUBLIC_KEY';
      }

      console.log(`[FS MOCK] Fichier non trouvé: ${path}`);
      throw new Error(`ENOENT: no such file or directory, open '${path}'`);
    }),

    writeFile: vi.fn(async (path, data) => {
      console.log(`[FS MOCK] Tentative d'écriture asynchrone du fichier: ${path}`);

      // Normaliser le chemin
      const normalizedPath = normalizePath(path);
      console.log(`[FS MOCK] Chemin normalisé: ${normalizedPath}`);

      inMemoryFileSystem.set(normalizedPath, data);
      return true;
    }),

    unlink: vi.fn(async (path) => {
      console.log(`[FS MOCK] Tentative de suppression asynchrone du fichier: ${path}`);

      // Normaliser le chemin
      const normalizedPath = normalizePath(path);
      console.log(`[FS MOCK] Chemin normalisé: ${normalizedPath}`);

      if (inMemoryFileSystem.has(normalizedPath)) {
        inMemoryFileSystem.delete(normalizedPath);
        return true;
      }
      throw new Error(`ENOENT: no such file or directory, unlink '${path}'`);
    })
  },

  // Méthodes utilitaires pour les tests
  __setFile: (path, content) => {
    inMemoryFileSystem.set(path, content);
  },

  __getFile: (path) => {
    return inMemoryFileSystem.get(path);
  },

  __hasFile: (path) => {
    return inMemoryFileSystem.has(path);
  },

  __deleteFile: (path) => {
    return inMemoryFileSystem.delete(path);
  },

  __clearFiles: () => {
    inMemoryFileSystem.clear();
  },

  __listFiles: () => {
    return Array.from(inMemoryFileSystem.keys());
  },

  // Réinitialiser tous les mocks
  __resetMocks: () => {
    vi.clearAllMocks();
  }
};

// Exporter le mock
export default fsMock;
