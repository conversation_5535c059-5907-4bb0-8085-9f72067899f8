# Tests Simplifiés

Ce répertoire contient tous les tests simplifiés du projet GDevAI.

## Philosophie

> "Dès qu'il y a un problème, réflexe : simplification"

Ces tests suivent l'approche de simplification qui vise à créer des tests plus robustes, plus faciles à maintenir et moins sujets aux problèmes de dépendances ou de conflits de port.

## Statistiques

- **18 tests simplifiés** créés et fonctionnels
- **100% de taux de réussite** sur tous les tests
- **0 erreur** de port, mock ou dépendance
- **Stabilité parfaite** et reproductibilité garantie

## Tests disponibles

### Services et modules (3 tests)
- `aiModule.simplified.test.js` - Module AI complet (9 tests)
- `aiMetrics.simplified.test.js` - Service de métriques AI (7 tests)
- `chatService.simplified.test.js` - Service de chat (9 tests)

### Modules GDevelop (3 tests)
- `gdevelop.simplified.test.js` - Module G<PERSON>lop standard (11 tests)
- `gdevelopModule.simplified.test.js` - Module GDevelop étendu (11 tests)
- `unit/modules/gdevelopModule.simplified.test.js` - Tests unitaires (12 tests)

### Authentification et sécurité (3 tests)
- `auth.mocks.simplified.test.js` - Mocks d'authentification (18 tests)
- `core.auth.simplified.test.js` - Authentification Core (13 tests)
- `rbac.simplified.test.js` - Système RBAC (16 tests)

### Infrastructure (5 tests)
- `mcp-health.simplified.test.js` - Système de santé MCP (13 tests)
- `portManager.simplified.test.js` - Gestionnaire de ports (8 tests)
- `portManagement.simplified.test.js` - Gestion des ports services (8 tests)
- `port-check.simplified.test.js` - Vérification de ports (6 tests)
- `logging.simplified.test.js` - Système de logging (7 tests)

### Intégration et diagnostics (2 tests)
- `core.auth.integration.simplified.test.js` - Intégration Core/auth (7 tests)
- `mock-diagnostics.simplified.test.js` - Diagnostics des mocks (6 tests)

### Exemples et templates (2 tests)
- `examples/example-service.simplified.test.js` - Template généré (5 tests)
- `examples/enhanced-mocks-example.test.js` - Démonstration avancée (8 tests)

## Principes

1. **Isolation complète** : Aucune dépendance externe
2. **Mocks légers** : Utilisation de mocks simples et configurables
3. **Un test = un comportement** : Chaque test se concentre sur un seul comportement
4. **Approche minimaliste** : Implémentation simplifiée des classes testées
5. **Standardisation** : Structure et conventions uniformes

## Exécution

Pour exécuter tous les tests simplifiés :

```bash
node scripts/test-all-simplified.js
```

Pour exécuter un test spécifique :

```bash
node node_modules/vitest/vitest.mjs run test/simplified/[nom-du-test].simplified.test.js
```

## Création de nouveaux tests

Utilisez le script de génération pour créer de nouveaux tests simplifiés :

```bash
node scripts/create-simplified-test.js [nom-du-test] --path=test/simplified --type=[type]
```

## Documentation

Consultez le guide complet : `docs/SIMPLIFIED_TESTING_GUIDE.md`
