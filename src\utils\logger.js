// Logger centralisé avec Winston
import winston from 'winston';
import { format } from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';

const { combine, timestamp, printf, errors } = format;

// Format personnalisé
const logFormat = printf(({ level, message, timestamp, stack }) => {
  return `${timestamp} [${level}]: ${stack || message}`;
});

// Transports principaux
const transports = [
  new DailyRotateFile({
    filename: 'logs/application-%DATE%.log',
    datePattern: 'YYYY-MM-DD',
    zippedArchive: true,
    maxSize: '20m',
    maxFiles: '14d'
  }),
  new winston.transports.Console({
    format: combine(
      format.colorize(),
      logFormat
    )
  })
];

// Configuration du logger
const logger = winston.createLogger({
  level: 'info',
  format: combine(
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    errors({ stack: true }),
    logFormat
  ),
  transports,
  handleExceptions: true
});

export default logger;

// Méthodes utilitaires
export const logRequest = (req, res, next) => {
  logger.info(`${req.method} ${req.url}`);
  next();
};

export const logError = (error, context = '') => {
  logger.error(`${context} - ${error.message}`, { stack: error.stack });
};
