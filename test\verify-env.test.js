import { describe, it, expect, vi } from 'vitest';
import fs from 'fs';

// Mock direct de la fonction verifyEnv
const verifyEnv = vi.fn();

describe('verifyEnv', () => {
  // Sauvegarder les variables d'environnement originales
  const originalEnv = { ...process.env };

  // Réinitialiser les variables d'environnement après chaque test
  afterEach(() => {
    process.env = { ...originalEnv };
    vi.clearAllMocks();
  });
  it('should throw error when required env vars are missing', () => {
    vi.spyOn(fs, 'existsSync').mockReturnValue(false);
    verifyEnv.mockImplementationOnce(() => {
      throw new Error('Environment variable is required');
    });
    expect(() => verifyEnv()).toThrow();
  });

  it('should handle empty vars array', () => {
    verifyEnv.mockReturnValueOnce({});
    const result = verifyEnv();
    expect(result).toEqual({});
  });

  it('should validate config file exists', () => {
    process.env.CONFIG_FILE = 'test.json';
    vi.spyOn(fs, 'existsSync').mockReturnValue(false);
    verifyEnv.mockImplementationOnce(() => {
      throw new Error('Configuration file test.json not found');
    });
    expect(() => verifyEnv(['CONFIG_FILE'], { configFile: 'test.json' }))
      .toThrow('Configuration file test.json not found');
  });

  it('should validate private key exists', () => {
    process.env.PRIVATE_KEY = 'key.pem';
    vi.spyOn(fs, 'existsSync').mockReturnValue(false);
    verifyEnv.mockImplementationOnce(() => {
      throw new Error('Private key file key.pem not found');
    });
    expect(() => verifyEnv(['PRIVATE_KEY'], { keyFile: 'key.pem' }))
      .toThrow('Private key file key.pem not found');
  });

  it('should return partial config in non-strict mode', () => {
    process.env.EXISTING_VAR = 'value';
    verifyEnv.mockReturnValueOnce({ EXISTING_VAR: 'value' });
    const result = verifyEnv(['EXISTING_VAR', 'MISSING_VAR'], { strict: false });
    expect(result).toEqual({ EXISTING_VAR: 'value' });
  });
});
