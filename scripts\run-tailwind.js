import { exec } from 'child_process';
import path from 'path';

const tailwindPath = path.join(__dirname, '../node_modules/.bin/tailwindcss');
const input = path.join(__dirname, '../src/input.css');
const output = path.join(__dirname, '../src/output.css');

exec(`${tailwindPath} -i ${input} -o ${output} --watch`, (error, stdout, stderr) => {
  if (error) {
    console.error(`Error: ${error.message}`);
    return;
  }
  if (stderr) {
    console.error(`Stderr: ${stderr}`);
    return;
  }
  console.log(`TailwindCSS is running...\n${stdout}`);
});
