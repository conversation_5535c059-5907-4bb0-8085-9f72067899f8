# Guide Utilisateur GDevAI

## 1. Premiers Pas

### 1.1 Installation
```bash
git clone https://github.com/votre-org/GDevAI.git
cd GDevAI
npm install
```

### 1.2 Configuration Initiale
```bash
cp .env.example .env
# Modifier les variables dans .env
```

## 2. Fonctionnalités Clés

### 2.1 Interface Principale
- Tableau de bord des projets
- Gestion des modèles IA
- Suivi des conversations

### 2.2 Intégration GDevelop
```javascript
// Exemple d'intégration
import GDevAI from 'gdev-ai';

const ai = new GDevAI({
  apiKey: 'votre_cle_api',
  projectId: 'votre_projet_gdevelop'
});
```

## 3. Utilisation Avancée

### 3.1 Workflows Automatisés
```mermaid
graph TD
    A[Événement GDevelop] --> B[Trigger GDevAI]
    B --> C[Traitement IA]
    C --> D[Retour à GDevelop]
```

### 3.2 Personnalisation
- Créer des modules custom dans `mcp-server/mcp-modules/`
- Ajouter des outils dans `mcp-server/tools/`

## 4. Bonnes Pratiques

### 4.1 Gestion des Contextes
- Limiter la taille des conversations
- Utiliser des résumés périodiques
- Sauvegarder les contextes importants

### 4.2 Sécurité
- Rotation régulière des clés API
- Audit des permissions RBAC
- Journalisation des activités

## 5. Dépannage

### 5.1 Problèmes Courants
- **Connexion à GDevelop** : Vérifier les credentials
- **Latence IA** : Optimiser les requêtes
- **Permissions** : Vérifier `config/roles.yaml`

### 5.2 Support
- Documentation : `/docs`
- Issues GitHub
- Email : <EMAIL>

## 6. Exemples Complets

### 6.1 Intégration avec GDevelop
```javascript
// Dans un événement GDevelop
gdjs.evtTools.gdevai.query({
  prompt: "Génère un dialogue pour ce personnage",
  callback: (response) => {
    // Utiliser la réponse dans le jeu
  }
});
```

### 6.2 Création d'outil MCP
```javascript
// mcp-server/tools/mon-outil.js
export default {
  name: 'mon-outil',
  execute: (params) => {
    // Implémentation ici
  }
}
```
