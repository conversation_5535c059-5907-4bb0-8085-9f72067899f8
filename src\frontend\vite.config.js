import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'
import aliases from '../../config/aliases.cjs'

const viteAliases = Object.entries(aliases).reduce((acc, [key, value]) => {
  acc[key] = path.resolve(__dirname, '../../', value)
  return acc
}, {})

export default defineConfig({
  resolve: {
    alias: viteAliases
  },
  plugins: [react()],
  server: {
    port: 57777,
    strictPort: false,  // Permet le fallback sur 57778-57779
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:50777',  // Port core principal
        changeOrigin: true,
        secure: false,
        retries: 3  // Ajout de retry pour le proxy
      },
      '/core': {
        target: 'http://localhost:50777',  // Port core principal
        changeOrigin: true,
        secure: false,
        retries: 3  // Ajout de retry pour le proxy
      }
    }
  },
  build: {
    outDir: '../dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, 'index.html')
      }
    }
  }
})
