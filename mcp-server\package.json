{"name": "mcp-server", "version": "1.0.0", "type": "module", "engines": {"node": ">=14.13.1"}, "description": "Serveur MCP modulaire", "main": "server.js", "scripts": {"start": "node server.js", "stop": "node scripts/stop.js", "status": "node scripts/status.js", "modules:list": "node -e \"import('./moduleLoader.cjs').then(ml => ml.default.listModules())\"", "modules:add": "node scripts/moduleManager.js add", "modules:remove": "node scripts/moduleManager.js remove", "dev": "nodemon server.js"}, "dependencies": {"dotenv": "^16.5.0", "express": "^4.18.2", "path": "^0.12.7"}, "devDependencies": {"nodemon": "^3.1.0"}}