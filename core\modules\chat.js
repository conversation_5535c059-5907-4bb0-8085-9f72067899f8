import AIModule from './ai.js';
import ChatService from '../../src/backend/services/chatService.js';

class ChatModule {
  constructor(logger) {
    this.logger = logger;
    this.service = new ChatService(logger);
    this.aiModule = new AIModule();
  }

  async process(message) {
    try {
      this.logger.info('Processing chat message:', message);
      
      if (message.useAI) {
        try {
          const aiResponse = await this.aiModule.process(message);
          return {
            text: aiResponse.text,
            sender: 'assistant',
            isAI: true
          };
        } catch (aiError) {
          this.logger.error('AI processing failed:', aiError);
          const serviceResponse = await this.service.processMessage(message);
          return {
            text: serviceResponse.text,
            sender: 'assistant',
            isAI: false
          };
        }
      }
      
      const serviceResponse = await this.service.processMessage(message);
      return {
        text: serviceResponse.text,
        sender: 'assistant',
        isAI: false
      };
    } catch (error) {
      this.logger.error('Error in chat module:', error);
      throw new Error(`Chat processing failed: ${error.message}`);
    }
  }
}

export default ChatModule;
