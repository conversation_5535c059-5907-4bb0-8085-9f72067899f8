import fs from 'fs/promises'
import path from 'path'
import crypto from 'crypto'
import { fileURLToPath } from 'url'

const __dirname = path.dirname(fileURLToPath(import.meta.url))
const COVERAGE_DIR = path.join(__dirname, '../coverage')
const ARCHIVE_DIR = path.join(__dirname, '../logs/coverage_history')
const RETENTION_DAYS = 30

// Couleurs console (ANSI)
const colors = {
  green: text => `\x1b[32m${text}\x1b[0m`,
  yellow: text => `\x1b[33m${text}\x1b[0m`,
  red: text => `\x1b[31m${text}\x1b[0m`
}

async function generateHash(filePath) {
  const fileBuffer = await fs.readFile(filePath)
  return crypto.createHash('sha256').update(fileBuffer).digest('hex')
}

async function archiveCoverage() {
  try {
    // 1. Créer le dossier d'archive si besoin
    await fs.mkdir(ARCHIVE_DIR, { recursive: true })

    const now = new Date()
    const dateStr = now.toISOString().split('T')[0]
    let archiveFolder = path.join(ARCHIVE_DIR, dateStr)
    
    // 2. Créer le dossier daté (avec gestion des doublons)
    let suffix = 1
    while (true) {
      try {
        await fs.mkdir(archiveFolder)
        break
      } catch (err) {
        if (err.code === 'EEXIST') {
          archiveFolder = path.join(ARCHIVE_DIR, `${dateStr}_${suffix++}`)
          continue
        }
        throw err
      }
    }

    // 3. Vérifier et copier coverage.json si disponible
    let coverageData = {}
    try {
      await fs.access(COVERAGE_DIR)
      const coverageFile = path.join(COVERAGE_DIR, 'coverage.json')
      const archiveFile = path.join(archiveFolder, 'coverage.json')
      await fs.copyFile(coverageFile, archiveFile)
      
      const fileHash = await generateHash(archiveFile)
      await fs.writeFile(
        path.join(archiveFolder, 'integrity.sha256'), 
        fileHash
      )
      coverageData = JSON.parse(await fs.readFile(coverageFile))
    } catch (err) {
      console.warn(colors.yellow('⚠ Aucun rapport de couverture trouvé, génération d\'un rapport minimal'))
      coverageData = {
        numTotalTestSuites: 0,
        numPassedTestSuites: 0,
        numFailedTestSuites: 0,
        numPendingTestSuites: 0,
        testResults: {}
      }
    }

    // 4. Générer le rapport Markdown
    const markdownReport = generateMarkdownReport(coverageData, dateStr)
    await fs.writeFile(
      path.join(archiveFolder, 'REPORT.md'),
      markdownReport
    )

    // 5. Nettoyer les anciennes archives
    await cleanOldArchives()

    console.log(colors.green(`✔ Archivage réussi : ${archiveFolder}`))
    return true
  } catch (error) {
    console.error(colors.red(`✖ Erreur d'archivage : ${error.message}`))
    return false
  }
}

function validateCoverageData(data) {
  if (!data || typeof data !== 'object') {
    throw new Error('Données de couverture invalides: format JSON incorrect')
  }
  
  // Structure minimale attendue
  if (typeof data.numTotalTestSuites === 'undefined') {
    data.numTotalTestSuites = 0
    data.numPassedTestSuites = 0
    data.numFailedTestSuites = 0
    data.numPendingTestSuites = 0
    data.testResults = {}
  }
  
  return data
}

function generateMarkdownReport(data, date) {
  try {
    data = validateCoverageData(data)
    
    return `# Rapport de couverture - ${date}

## Statistiques globales
- Suites de tests : ${data.numTotalTestSuites} total
  - ✓ Réussies : ${data.numPassedTestSuites}
  - ✗ Échouées : ${data.numFailedTestSuites}
  - ⏱ En attente : ${data.numPendingTestSuites}

## Détails par fichier
${Object.entries(data.testResults)
  .map(([file, result]) => {
    const passed = result.numPassingTests || 0
    const total = result.numTotalTests || 0
    return `- ${file} : ${passed}/${total} tests passés`
  })
  .join('\n')}

## Vérification d'intégrité
\`\`\`
SHA256: Voir integrity.sha256
\`\`\`
`
  } catch (error) {
    return `# Rapport de couverture - ${date}

## Erreur
${error.message}

Le fichier coverage.json est vide ou corrompu.
`
  }
}

async function cleanOldArchives() {
  const now = new Date()
  const folders = await fs.readdir(ARCHIVE_DIR)
  
  for (const folder of folders) {
    const folderPath = path.join(ARCHIVE_DIR, folder)
    const stat = await fs.stat(folderPath)
    const ageDays = (now - stat.birthtime) / (1000 * 60 * 60 * 24)
    
    if (ageDays > RETENTION_DAYS) {
      await fs.rm(folderPath, { recursive: true })
      console.log(colors.yellow(`⌫ Archive nettoyée : ${folder} (${Math.floor(ageDays)} jours)`))
    }
  }
}

// Exécution
archiveCoverage()
