# Guide Local GDevAI

## 1. Installation & Lancement
```bash
npm install
npm start
```

## 2. URLs Locales
- Frontend : http://localhost:3000
- API : http://localhost:3007 
- MCP : http://localhost:7000

## 3. Intégration GDevelop
```javascript
gdjs.evtTools.gdevai.setConfig({
  endpoint: "http://localhost:3000/api",
  localMode: true
});
```

## 4. Dépannage
- Ports occupés : `npm run kill-ports`
- Logs : dans `logs/` et `mcp-server/logs/`

## 5. Option Docker
```bash 
docker-compose up

## 6. Système de Backup
### Méthodes disponibles :
1. **7-Zip** (Recommandé)
```bash
choco install 7zip -y
```
2. **PowerShell Native** (Méthode par défaut)
```javascript
// CompressionLevel Optimal activé
// Barre de progression désactivée

### Configuration :
```env
SEVEN_ZIP_PATH="C:\\Program Files\\7-Zip\\7z.exe"
```

### Test du système :
```bash
node scripts/backup.js
```
