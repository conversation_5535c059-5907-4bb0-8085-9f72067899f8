import { describe, it, expect, vi } from 'vitest';
import { execSync } from 'child_process';
import fs from 'fs';
import keytar from 'keytar';
import { loadRolesConfig } from '../src/utils/rbac.js';

// Mocks
vi.mock('keytar');
vi.mock('fs');
vi.mock('../src/utils/rbac.js');

describe('Security Tests', () => {
  describe('Vault File', () => {
    it('should exist', () => {
      fs.existsSync.mockReturnValue(true);
      expect(fs.existsSync('.secure-vault')).toBe(true);
    });

    it('should return false if vault file missing', () => {
      fs.existsSync.mockReturnValue(false);
      expect(fs.existsSync('.secure-vault')).toBe(false);
    });
  });

  describe('Master Key', () => {
    it('should exist in credential manager', async () => {
      keytar.getPassword.mockResolvedValue('mock-password');
      const password = await keytar.getPassword('GDevAI', 'system-key');
      expect(password).toBeTruthy();
    });

    it('should return null if master key missing', async () => {
      keytar.getPassword.mockResolvedValue(null);
      const result = await keytar.getPassword('GDevAI', 'system-key');
      expect(result).toBeNull();
    });
  });

  describe('RBAC System', () => {
    it('should load valid RBAC config', () => {
      const mockConfig = {
        roles: {
          admin: { permissions: ['*'] }
        }
      };
      loadRolesConfig.mockReturnValue(mockConfig);
      expect(loadRolesConfig()).toEqual(mockConfig);
    });
  });

  if (process.platform === 'win32') {
    describe('Scheduled Task (Windows)', () => {
      it('should exist', () => {
        vi.spyOn(execSync, 'toString').mockReturnValue('GDevAI Backup');
        const output = execSync('schtasks /query /tn "GDevAI Backup" /fo list').toString();
        expect(output).toContain('GDevAI Backup');
      });
    });
  }

  describe('GDevelop Integration', () => {
    const mockExport = { project: 'test', data: 'sensitive' };
    
    it('should encrypt exports with AES-256', async () => {
      vi.doMock('../integrations/gdevelop/api.js', () => ({
        exportProject: vi.fn().mockImplementation((project) => ({
          ...project,
          iv: 'mockIV1234567890',
          content: 'encryptedData'
        }))
      }));
      
      const { exportProject } = await import('../integrations/gdevelop/api.js');
      const result = await exportProject({ project: 'test', data: 'sensitive' });
      
      expect(result).toHaveProperty('iv');
      expect(result.iv).toBe('mockIV1234567890');
      expect(result).toHaveProperty('content');
      expect(result.content).toBe('encryptedData');
    });
  });
});
