import postcss from 'postcss';
import tailwind from '@tailwindcss/postcss';
import fs from 'fs';
import path from 'path';
import { pathToFileURL } from 'url';

const inputFile = path.resolve('src/input.css');
const outputFile = path.resolve('src/output.css');
const configFile = path.resolve('tailwind.config.js');

const configUrl = pathToFileURL(configFile);
let config = (await import(configUrl)).default;

async function build() {
  const css = fs.readFileSync(inputFile, 'utf8');
  const result = await postcss([
    tailwind(config)
  ]).process(css, { from: inputFile, to: outputFile });
  
  fs.writeFileSync(outputFile, result.css);
  console.log('TailwindCSS compiled successfully');
}

fs.watch(inputFile, () => build().catch(console.error));
fs.watch(configFile, () => {
  import(configUrl).then(m => {
    config = m.default;
    build().catch(console.error);
  });
});

build().catch(console.error);
