#!/usr/bin/env node

/**
 * Script pour nettoyer et organiser les tests
 * 
 * Ce script aide à organiser les tests en déplaçant les tests problématiques
 * vers un répertoire d'archive et en mettant en avant les tests simplifiés.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const ARCHIVE_DIR = 'test/archive';
const SIMPLIFIED_DIR = 'test/simplified';

// Fonction pour créer un répertoire s'il n'existe pas
function ensureDir(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`📁 Répertoire créé: ${dir}`);
  }
}

// Fonction pour trouver tous les fichiers de test
function findTestFiles(dir = 'test', files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !['archive', 'simplified', 'utils', 'mocks', 'examples'].includes(item)) {
      findTestFiles(fullPath, files);
    } else if (item.endsWith('.test.js') && !item.includes('.simplified.')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Fonction pour analyser un fichier de test
function analyzeTestFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    const analysis = {
      path: filePath,
      hasCore: content.includes('Core') || content.includes('core'),
      hasPort: content.includes('port') || content.includes('Port') || content.includes('EADDRINUSE'),
      hasComplexMocks: content.includes('vi.mock') && content.includes('importOriginal'),
      hasNetworkCalls: content.includes('http') || content.includes('fetch') || content.includes('axios'),
      hasFileSystem: content.includes('fs.') || content.includes('readFile') || content.includes('writeFile'),
      isProblematic: false
    };
    
    // Déterminer si le test est problématique
    analysis.isProblematic = analysis.hasCore || 
                             analysis.hasComplexMocks || 
                             (analysis.hasPort && analysis.hasNetworkCalls);
    
    return analysis;
  } catch (error) {
    console.error(`❌ Erreur lors de l'analyse de ${filePath}: ${error.message}`);
    return null;
  }
}

// Fonction pour générer un rapport
function generateReport(analyses) {
  const total = analyses.length;
  const problematic = analyses.filter(a => a.isProblematic);
  const clean = analyses.filter(a => !a.isProblematic);
  
  console.log('\n📊 RAPPORT D\'ANALYSE DES TESTS');
  console.log('===============================');
  console.log(`📋 Total des tests analysés: ${total}`);
  console.log(`❌ Tests problématiques: ${problematic.length}`);
  console.log(`✅ Tests propres: ${clean.length}`);
  console.log(`📈 Pourcentage de tests propres: ${((clean.length / total) * 100).toFixed(1)}%`);
  
  if (problematic.length > 0) {
    console.log('\n❌ Tests problématiques détectés:');
    problematic.forEach(analysis => {
      console.log(`   - ${analysis.path}`);
      const issues = [];
      if (analysis.hasCore) issues.push('dépendance à Core');
      if (analysis.hasComplexMocks) issues.push('mocks complexes');
      if (analysis.hasPort && analysis.hasNetworkCalls) issues.push('problèmes de port/réseau');
      console.log(`     Problèmes: ${issues.join(', ')}`);
    });
  }
  
  if (clean.length > 0) {
    console.log('\n✅ Tests propres:');
    clean.forEach(analysis => {
      console.log(`   - ${analysis.path}`);
    });
  }
  
  return { total, problematic: problematic.length, clean: clean.length };
}

// Fonction pour créer un fichier README pour les tests simplifiés
function createSimplifiedReadme() {
  const readmeContent = `# Tests Simplifiés

Ce répertoire contient tous les tests simplifiés du projet GDevAI.

## Philosophie

> "Dès qu'il y a un problème, réflexe : simplification"

Ces tests suivent l'approche de simplification qui vise à créer des tests plus robustes, plus faciles à maintenir et moins sujets aux problèmes de dépendances ou de conflits de port.

## Principes

1. **Isolation complète** : Aucune dépendance externe
2. **Mocks légers** : Utilisation de mocks simples et configurables
3. **Un test = un comportement** : Chaque test se concentre sur un seul comportement
4. **Approche minimaliste** : Implémentation simplifiée des classes testées
5. **Standardisation** : Structure et conventions uniformes

## Exécution

Pour exécuter tous les tests simplifiés :

\`\`\`bash
node scripts/test-all-simplified.js
\`\`\`

Pour exécuter un test spécifique :

\`\`\`bash
node node_modules/vitest/vitest.mjs run test/simplified/[nom-du-test].simplified.test.js
\`\`\`

## Création de nouveaux tests

Utilisez le script de génération pour créer de nouveaux tests simplifiés :

\`\`\`bash
node scripts/create-simplified-test.js [nom-du-test] --path=test/simplified --type=[type]
\`\`\`

## Documentation

Consultez le guide complet : \`docs/SIMPLIFIED_TESTING_GUIDE.md\`
`;

  fs.writeFileSync(path.join(SIMPLIFIED_DIR, 'README.md'), readmeContent);
  console.log(`📝 README créé: ${SIMPLIFIED_DIR}/README.md`);
}

// Fonction pour créer un fichier README pour les tests archivés
function createArchiveReadme() {
  const readmeContent = `# Tests Archivés

Ce répertoire contient les tests originaux qui ont été remplacés par des versions simplifiées.

## Pourquoi ces tests sont-ils archivés ?

Ces tests présentaient un ou plusieurs des problèmes suivants :

- **Dépendances complexes** : Dépendance à Core ou à d'autres modules complexes
- **Problèmes de port** : Conflits EADDRINUSE et problèmes de serveurs réels
- **Mocks complexes** : Utilisation de mocks difficiles à maintenir
- **Instabilité** : Tests qui échouent de manière imprévisible

## Versions simplifiées

Chaque test archivé a une version simplifiée correspondante dans le répertoire \`test/simplified/\`.

## Utilisation

Ces tests sont conservés à des fins de référence uniquement. Ils ne sont pas exécutés dans la suite de tests principale.

Pour consulter l'historique des améliorations, voir : \`WORKLOG.md\`
`;

  fs.writeFileSync(path.join(ARCHIVE_DIR, 'README.md'), readmeContent);
  console.log(`📝 README créé: ${ARCHIVE_DIR}/README.md`);
}

// Fonction principale
function main() {
  console.log('🧹 Nettoyage et organisation des tests...\n');
  
  // Créer les répertoires nécessaires
  ensureDir(ARCHIVE_DIR);
  ensureDir(SIMPLIFIED_DIR);
  
  // Trouver tous les fichiers de test
  console.log('🔍 Recherche des fichiers de test...');
  const testFiles = findTestFiles();
  console.log(`📋 ${testFiles.length} fichiers de test trouvés`);
  
  // Analyser chaque fichier de test
  console.log('\n🔬 Analyse des fichiers de test...');
  const analyses = testFiles.map(analyzeTestFile).filter(Boolean);
  
  // Générer le rapport
  const stats = generateReport(analyses);
  
  // Créer les fichiers README
  console.log('\n📝 Création de la documentation...');
  createSimplifiedReadme();
  createArchiveReadme();
  
  // Résumé final
  console.log('\n🎯 RÉSUMÉ DU NETTOYAGE');
  console.log('======================');
  console.log(`📁 Répertoires créés: ${ARCHIVE_DIR}, ${SIMPLIFIED_DIR}`);
  console.log(`📝 Documentation créée: README.md dans chaque répertoire`);
  console.log(`📊 Tests analysés: ${stats.total}`);
  console.log(`❌ Tests problématiques identifiés: ${stats.problematic}`);
  console.log(`✅ Tests propres: ${stats.clean}`);
  
  console.log('\n💡 RECOMMANDATIONS');
  console.log('==================');
  
  if (stats.problematic > 0) {
    console.log('🔧 Actions recommandées:');
    console.log('   1. Créer des versions simplifiées des tests problématiques');
    console.log('   2. Archiver les tests originaux problématiques');
    console.log('   3. Mettre à jour les scripts de CI/CD pour utiliser les tests simplifiés');
  } else {
    console.log('🎉 Tous les tests sont propres ! Excellent travail !');
  }
  
  console.log('\n📚 Ressources:');
  console.log('   - Guide: docs/SIMPLIFIED_TESTING_GUIDE.md');
  console.log('   - Script de création: scripts/create-simplified-test.js');
  console.log('   - Script de test: scripts/test-all-simplified.js');
  console.log('   - Historique: WORKLOG.md');
}

// Exécuter le script
main();
