#!/usr/bin/env node
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import path from 'path';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

function listPorts() {
  try {
    const output = execSync('netstat -ano').toString();
    console.log('Ports utilisés :\n', output);
  } catch (err) {
    console.error('Erreur :', err.message);
  }
}

function killPort(port) {
  try {
    const output = execSync(`netstat -ano | findstr :${port}`).toString();
    const pids = [...new Set(output.match(/\d+$/gm))];
    
    if (pids.length === 0) {
      console.log(`Aucun processus sur le port ${port}`);
      return;
    }

    pids.forEach(pid => {
      console.log(`Arrêt du PID ${pid}...`);
      execSync(`taskkill /PID ${pid} /F`);
    });
    
    console.log(`${pids.length} processus arrêtés sur le port ${port}`);
  } catch (err) {
    console.error('Erreur :', err.message);
  }
}

// Usage: node portManager.js [list|kill PORT]
const [,, command, arg] = process.argv;

if (command === 'list') {
  listPorts();
} else if (command === 'kill' && arg) {
  killPort(arg);
} else {
  console.log('Usage:');
  console.log('  node portManager.js list');
  console.log('  node portManager.js kill PORT');
  process.exit(1);
}
