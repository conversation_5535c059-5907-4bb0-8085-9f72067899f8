import GDevelopAPI from './mocks/gdevelop.js';
import { vi } from 'vitest';

console.log('[SETUP] Loading test setup with standardized mocks');

// Créer une variable pour stocker les mocks d'authentification
const authMockFunctions = {
  authMiddleware: vi.fn((req, res, next) => {
    req.user = {
      userId: 'test-user-id',
      roles: [],
      permissions: []
    };

    if (req.errorTest) {
      throw new Error('Test error handling');
    }

    return next();
  }),
  generateToken: vi.fn(() => 'MOCK_TOKEN'),
  verifyToken: vi.fn(() => ({
    userId: 'test-user-id',
    roles: [],
    permissions: []
  }))
};

// Configurer le mock pour auth.js
vi.mock('@utils/auth.js', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    ...authMockFunctions
  };
});

console.log('Auth mocks configurés avec succès');

// External mocks
vi.mock('jsonwebtoken', () => ({
  sign: vi.fn(),
  verify: vi.fn(),
  decode: vi.fn()
}));

// Importer le mock fs
import fsMock from './mocks/fs.js';

// Appliquer le mock fs
vi.mock('fs', () => fsMock);

// Mock pour fs/promises
vi.mock('fs/promises', () => ({
  readFile: fsMock.promises.readFile,
  writeFile: fsMock.promises.writeFile,
  unlink: fsMock.promises.unlink
}));

// Mock pour aiMetrics
vi.mock('../../src/utils/aiMetrics', () => ({
  logAIMetrics: vi.fn(),
  trackRequest: vi.fn(),
  getAIStats: vi.fn().mockResolvedValue({
    avgResponseTime: 0,
    successRate: 0,
    totalTokens: 0
  }),
  default: {
    logAIMetrics: vi.fn(),
    trackRequest: vi.fn(),
    getAIStats: vi.fn().mockResolvedValue({
      avgResponseTime: 0,
      successRate: 0,
      totalTokens: 0
    })
  }
}));

// Global mocks
vi.mock('#logger', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    default: {
      child: () => ({
        info: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn()
      })
    }
  };
});

vi.mock('@gdevelop/api', () => ({
  default: GDevelopAPI
}));

// Reset before each test
beforeEach(() => {
  console.log('[SETUP] Clearing all mocks before test');
  vi.clearAllMocks();
});

// Cleanup after all tests
afterAll(() => {
  console.log('[SETUP] Restoring all mocks after tests');
  vi.restoreAllMocks();
});
