name: CI Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
    
    - name: Install specific npm version
      run: npm install -g npm@11.4.0
    
    - name: Install dependencies
      run: |
        npm install -g npm-force-resolutions
        npm install --legacy-peer-deps
        npm run preinstall
    
    - name: Run tests
      run: npm run test:ci
      
    - name: Upload coverage
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: coverage
