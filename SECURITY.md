# Procédures de Sécurité GDevAI

## Vérifications Automatisées
```bash
npm run check-security
```

## Vérifications Manuelles
1. **Tâche Planifiée** (Windows) :
```powershell
schtasks /query /tn "GDevAI Backup" /v
```

2. **<PERSON><PERSON> Maître** :
```powershell
cmdkey /list | findstr "GDevAI"
```

3. **Fichiers de Sécurité** :
- `.secure-vault` (chiffré)
- `config/backup.json` (configuration)

## Politique de Rotation
- Mot de passe maître : Tous les 3 mois
- Clés de chiffrement : Tous les 6 mois

## Procédure d'Urgence
1. R<PERSON><PERSON><PERSON> toutes les clés :
```bash
npm run revoke-keys
```
2. Régénérer le coffre :
```bash
node scripts/setup-secure.mjs --emergency
