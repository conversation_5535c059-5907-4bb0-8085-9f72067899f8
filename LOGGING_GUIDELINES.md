# Directives de journalisation (Logging Guidelines)

Ce document définit les bonnes pratiques et conventions pour la journalisation dans le projet GDevAI.

## Niveaux de journalisation

Utilisez les niveaux de journalisation appropriés selon le contexte :

- **ERROR** : Erreurs critiques qui empêchent le fonctionnement normal de l'application
- **WARN** : Situations anormales qui n'empêchent pas le fonctionnement mais méritent attention
- **INFO** : Informations générales sur le fonctionnement de l'application
- **DEBUG** : Informations détaillées utiles pour le débogage

## Format des messages

### Structure recommandée

```
[CONTEXTE] Action ou événement - détails supplémentaires
```

Exemples :
- `[AUTH] Échec d'authentification - token expiré`
- `[CHAT] Message traité avec succès - longueur: 156 caractères`

### Informations à inclure

- **Identifiants** : IDs utilisateur, session, requête
- **Contexte** : Mo<PERSON><PERSON>, composant, fonction
- **Données pertinentes** : Valeurs clés (sans données sensibles)
- **Résultat** : Succès/échec, codes d'erreur

## Bonnes pratiques

1. **Cohérence** : Utilisez le même niveau pour des événements similaires
2. **Concision** : Messages courts mais informatifs
3. **Sécurité** : Ne jamais logger de données sensibles (mots de passe, tokens)
4. **Contexte** : Toujours inclure suffisamment d'information pour comprendre le contexte
5. **Traçabilité** : Inclure des identifiants pour suivre les flux d'exécution

## Exemples par niveau

### ERROR
```javascript
logger.error(`[DATABASE] Échec de connexion - ${err.message}`, { 
  code: err.code, 
  service: 'postgres' 
});
```

### WARN
```javascript
logger.warn(`[RBAC] Tentative d'accès non autorisé - user: ${user.username}`, {
  resource: 'chat.manage',
  userId: user.id
});
```

### INFO
```javascript
logger.info(`[SERVER] Démarrage du serveur - port: ${port}`, {
  env: process.env.NODE_ENV,
  version: '1.0.0'
});
```

### DEBUG
```javascript
logger.debug(`[CHAT] Traitement du message - longueur: ${message.length}`, {
  messageId: id,
  processingTime: `${endTime - startTime}ms`
});
```

## Configuration des logs dans les tests

Pour les tests, configurez les logs pour qu'ils n'interfèrent pas avec l'exécution :

```javascript
// Dans setupTests.js
vi.mock('#logger', () => ({
  default: {
    error: vi.fn(),
    warn: vi.fn(),
    info: vi.fn(),
    debug: vi.fn()
  }
}));
```

## Vérification des logs dans les tests

Vérifiez que les logs appropriés sont générés dans vos tests :

```javascript
test('should log error when permission denied', async () => {
  // ...code de test...
  
  expect(mockLogger.error).toHaveBeenCalledWith(
    expect.stringContaining('Permission denied'),
    expect.objectContaining({ code: 'CHAT_001' })
  );
});
```
