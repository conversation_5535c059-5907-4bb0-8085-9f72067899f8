#!/usr/bin/env node
// Script de vérification de l'environnement

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const REQUIRED_NODE_VERSION = '>=16.0.0';
const REQUIRED_NPM_VERSION = '>=7.0.0';

const nodePath = './nodejs/node-v20.12.2-win-x64/node.exe';
const npmPath = './nodejs/node-v20.12.2-win-x64/npm.cmd';

function checkNode() {
  try {
    const version = execSync(`"${nodePath}" --version`).toString().trim();
    console.log(`✓ Node.js: ${version}`);
    return true;
  } catch {
    console.error('× Node.js n\'est pas accessible');
    return false;
  }
}

function checkNpm() {
  try {
    const version = execSync(`"${npmPath}" --version`).toString().trim();
    console.log(`✓ npm: ${version}`);
    return true;
  } catch {
    console.error('× npm n\'est pas accessible');
    return false;
  }
}

function checkDependencies() {
  try {
    const pkg = JSON.parse(fs.readFileSync('package.json'));
    const deps = Object.keys(pkg.dependencies || {});
    const devDeps = Object.keys(pkg.devDependencies || {});
    
    console.log(`✓ ${deps.length} dépendances et ${devDeps.length} devDépendances configurées`);
    return true;
  } catch {
    console.error('× Impossible de lire les dépendances');
    return false;
  }
}

console.log('Vérification de l\'environnement...');
const nodeOk = checkNode();
const npmOk = checkNpm();
const depsOk = checkDependencies();

if (nodeOk && npmOk && depsOk) {
  console.log('\n✅ Environnement correctement configuré !');
  process.exit(0);
} else {
  console.log('\n❌ Des problèmes ont été détectés. Exécutez "npm run setup" pour configurer.');
  process.exit(1);
}
