/**
 * Utilitaires de mock simples pour les tests
 * 
 * Ce fichier contient des fonctions pour créer facilement des mocks
 * pour les composants couramment utilisés dans les tests.
 */
import { vi } from 'vitest';

/**
 * Crée un mock simple pour un serveur HTTP/HTTPS
 * @returns {Object} Un mock de serveur
 */
export function createServerMock() {
  return {
    listen: vi.fn((port, callback) => {
      if (callback) callback();
      return {
        address: () => ({ port: port || 3000 }),
        close: vi.fn(cb => cb && cb())
      };
    }),
    close: vi.fn(cb => cb && cb())
  };
}

/**
 * Crée un mock simple pour le logger
 * @returns {Object} Un mock de logger avec des méthodes de réinitialisation
 */
export function createLoggerMock() {
  const loggerMock = {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn(),
    // Méthode pour réinitialiser tous les mocks du logger
    resetMocks: function() {
      this.info.mockReset();
      this.error.mockReset();
      this.warn.mockReset();
      this.debug.mockReset();
    }
  };
  
  return loggerMock;
}

/**
 * Crée un mock simple pour un module
 * @param {string} name - Nom du module
 * @param {Function} processFunction - Fonction de traitement (mock)
 * @returns {Object} Un mock de module
 */
export function createModuleMock(name, processFunction = vi.fn()) {
  return {
    name,
    process: processFunction,
    initialize: vi.fn().mockResolvedValue(undefined)
  };
}

/**
 * Crée un mock simple pour le router
 * @param {Map} modules - Map des modules (par défaut vide)
 * @returns {Object} Un mock de router
 */
export function createRouterMock(modules = new Map()) {
  return {
    modules,
    routeMessage: vi.fn((moduleName, message) => {
      const module = modules.get(moduleName);
      if (!module) {
        throw new Error(`Module not found: ${moduleName}`);
      }
      return module.process(message);
    })
  };
}

/**
 * Crée un mock simple pour net.Server
 * @param {boolean} isPortFree - Si le port est libre ou non
 * @returns {Object} Un mock de net.Server
 */
export function createNetServerMock(isPortFree = true) {
  return {
    listen: vi.fn((port, callback) => {
      if (!isPortFree) {
        const error = new Error('EADDRINUSE');
        error.code = 'EADDRINUSE';
        throw error;
      }
      
      if (callback) callback();
      
      return {
        address: () => ({ port }),
        close: vi.fn(cb => cb && cb())
      };
    }),
    close: vi.fn(cb => cb && cb()),
    once: vi.fn((event, callback) => {
      if (event === 'listening' && isPortFree) {
        callback();
      } else if (event === 'error' && !isPortFree) {
        const error = new Error('EADDRINUSE');
        error.code = 'EADDRINUSE';
        callback(error);
      }
      return this;
    })
  };
}

/**
 * Crée un mock simple pour le module fs
 * @param {Object} fileContents - Contenu des fichiers (clé: chemin, valeur: contenu)
 * @returns {Object} Un mock de fs
 */
export function createFsMock(fileContents = {}) {
  return {
    readFile: vi.fn((path, options, callback) => {
      if (typeof options === 'function') {
        callback = options;
        options = {};
      }
      
      if (fileContents[path]) {
        callback(null, fileContents[path]);
      } else {
        callback(new Error(`ENOENT: no such file or directory, open '${path}'`));
      }
    }),
    
    readFileSync: vi.fn((path, options) => {
      if (fileContents[path]) {
        return fileContents[path];
      }
      throw new Error(`ENOENT: no such file or directory, open '${path}'`);
    }),
    
    writeFile: vi.fn((path, data, options, callback) => {
      if (typeof options === 'function') {
        callback = options;
        options = {};
      }
      
      fileContents[path] = data;
      if (callback) callback(null);
    }),
    
    writeFileSync: vi.fn((path, data, options) => {
      fileContents[path] = data;
    }),
    
    existsSync: vi.fn((path) => {
      return !!fileContents[path];
    })
  };
}

/**
 * Crée un mock simple pour le module http
 * @returns {Object} Un mock de http
 */
export function createHttpMock() {
  return {
    createServer: vi.fn(() => createServerMock())
  };
}

/**
 * Crée un mock simple pour le module https
 * @returns {Object} Un mock de https
 */
export function createHttpsMock() {
  return {
    createServer: vi.fn(() => createServerMock())
  };
}

/**
 * Crée un mock simple pour le module net
 * @param {boolean} isPortFree - Si le port est libre ou non
 * @returns {Object} Un mock de net
 */
export function createNetMock(isPortFree = true) {
  return {
    createServer: vi.fn(() => createNetServerMock(isPortFree))
  };
}

/**
 * Crée un mock simple pour une réponse HTTP
 * @returns {Object} Un mock de réponse HTTP
 */
export function createResponseMock() {
  const res = {
    status: vi.fn().mockReturnThis(),
    json: vi.fn().mockReturnThis(),
    send: vi.fn().mockReturnThis(),
    end: vi.fn().mockReturnThis(),
    setHeader: vi.fn().mockReturnThis(),
    headers: {}
  };
  return res;
}

/**
 * Crée un mock simple pour une requête HTTP
 * @param {Object} params - Paramètres de la requête
 * @returns {Object} Un mock de requête HTTP
 */
export function createRequestMock(params = {}) {
  return {
    params: params.params || {},
    query: params.query || {},
    body: params.body || {},
    headers: params.headers || {},
    user: params.user || { id: 'test-user-id' }
  };
}
