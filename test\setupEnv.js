/**
 * Configuration d'environnement pour les tests
 * 
 * Ce fichier est chargé automatiquement par Vitest avant l'exécution des tests
 * grâce à la configuration dans vitest.config.ts
 */
import { vi } from 'vitest';

/**
 * Configure l'environnement de test avec des valeurs par défaut
 */
export function setupTestEnvironment() {
  // Variables d'environnement essentielles
  process.env.NODE_ENV = 'test';
  process.env.DEEPSEEK_API_KEY = 'test-api-key';
  process.env.LOG_LEVEL = 'silent';
  
  // Autres variables d'environnement utiles pour les tests
  process.env.TEST_MODE = 'true';
  process.env.DISABLE_METRICS = 'true';
  
  // Désactiver les logs console pendant les tests
  mockConsole();
  
  console.log('Environnement de test configuré');
}

/**
 * Nettoie l'environnement de test
 */
export function cleanupTestEnvironment() {
  // Supprimer les variables d'environnement spécifiques aux tests
  delete process.env.DEEPSEEK_API_KEY;
  delete process.env.TEST_MODE;
  delete process.env.DISABLE_METRICS;
  
  // Restaurer les logs console
  restoreConsole();
  
  console.log('Environnement de test nettoyé');
}

// Sauvegarde des fonctions console originales
const originalConsole = {
  log: console.log,
  error: console.error,
  warn: console.warn,
  info: console.info,
  debug: console.debug
};

/**
 * Remplace les fonctions console par des mocks
 */
function mockConsole() {
  console.log = vi.fn();
  console.error = vi.fn();
  console.warn = vi.fn();
  console.info = vi.fn();
  console.debug = vi.fn();
}

/**
 * Restaure les fonctions console originales
 */
function restoreConsole() {
  console.log = originalConsole.log;
  console.error = originalConsole.error;
  console.warn = originalConsole.warn;
  console.info = originalConsole.info;
  console.debug = originalConsole.debug;
}

// Configurer l'environnement au chargement du fichier
setupTestEnvironment();

// Nettoyer l'environnement après tous les tests
afterAll(() => {
  cleanupTestEnvironment();
});
