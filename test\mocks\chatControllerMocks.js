import { formatResponse } from '../../src/utils/responseFormatter.js'

export const createChatControllerMocks = () => ({
  req: {
    body: {
      message: 'Test message'
    },
    interactionId: 'test-interaction-123'
  },
  res: {
    json: vi.fn(),
    status: vi.fn().mockReturnThis()
  },
  chatService: {
    processMessage: vi.fn().mockResolvedValue('Mock response')
  },
  getExpectedResponseStructure: () => ({
    status: 'success',
    data: {
      text: 'Mock response',
      sender: 'assistant'
    },
    metadata: {
      interactionId: 'test-interaction-123',
      version: '1.0',
      timestamp: expect.any(Number)
    }
  })
})
