import { execSync } from 'child_process'
import { fileURLToPath } from 'url'
import { dirname } from 'path'

const __dirname = dirname(fileURLToPath(import.meta.url))

try {
  // Force clean coverage dir (Windows compatible)
  execSync('if exist coverage rd /s /q coverage', { stdio: 'inherit', shell: true })
  
  // Run tests with coverage
  execSync('npx vitest run --coverage', { 
    stdio: 'inherit',
    env: { 
      ...process.env, 
      CI: 'true' 
    }
  })
} catch (error) {
  console.error('Error:', error)
  process.exit(1)
}
