import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const LOG_FILE = path.join(__dirname, '../../logs/mock-generator.log');

class MockLogger {
  constructor(moduleName) {
    this.moduleName = moduleName;
    this.ensureLogFile();
  }

  ensureLogFile() {
    if (!fs.existsSync(LOG_FILE)) {
      fs.writeFileSync(LOG_FILE, '');
    }
  }

  log(level, message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] [${level}] [${this.moduleName}] ${message}\n`;
    
    fs.appendFileSync(LOG_FILE, logEntry);
    console[level](logEntry.trim());
  }

  info(message) {
    this.log('info', message);
  }

  success(message) {
    this.log('log', `✅ ${message}`);
  }

  error(error) {
    this.log('error', `❌ ${error.message}\n${error.stack}`);
  }
}

export default MockLogger;
