import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mocks pour les dépendances
const mockReadFile = vi.fn();
const mockWriteFile = vi.fn();
const mockCheckPort = vi.fn();
const mockForceKill = vi.fn();

// Mock du module fs
vi.mock('fs/promises', () => ({
  readFile: mockReadFile,
  writeFile: mockWriteFile
}));

// Configuration simulée
const config = {
  server: {
    port: null // Sera configuré dans les tests
  }
};

// Fonction loadOrFindPort isolée pour le test
async function loadOrFindPort() {
  const portFile = 'port.txt';
  
  // Priorité au port configuré
  if (config.server.port) {
    await mockForceKill(config.server.port);
    return config.server.port;
  }
  
  try {
    // Essayer de lire le port sauvegardé
    const savedPort = parseInt(await mockReadFile(portFile, 'utf-8'));
    console.log(`Port précédent détecté: ${savedPort}`);
    await mockForceKill(savedPort);
    return savedPort;
  } catch {
    console.log('Aucun port sauvegardé, recherche en cours...');
    // Chercher un port disponible
    for (let port = 7000; port <= 7010; port++) {
      const used = await mockCheckPort(port);
      if (!used) {
        await mockWriteFile(portFile, port.toString());
        return port;
      }
    }
    throw new Error('Aucun port disponible');
  }
}

describe('Port Find Tests', () => {
  beforeEach(() => {
    // Réinitialiser les mocks et la configuration
    vi.clearAllMocks();
    config.server.port = null;
    
    // Configuration par défaut des mocks
    mockForceKill.mockResolvedValue(undefined);
    mockCheckPort.mockResolvedValue(false); // Par défaut, les ports sont libres
    mockWriteFile.mockResolvedValue(undefined);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should use configured port if available', async () => {
    // Configurer un port spécifique
    config.server.port = 5000;
    
    const port = await loadOrFindPort();
    
    // Vérifier que le port configuré est utilisé
    expect(port).toBe(5000);
    
    // Vérifier que forceKill a été appelé pour ce port
    expect(mockForceKill).toHaveBeenCalledWith(5000);
    
    // Vérifier que readFile n'a pas été appelé
    expect(mockReadFile).not.toHaveBeenCalled();
  });

  it('should use saved port if no configured port', async () => {
    // Simuler un port sauvegardé
    mockReadFile.mockResolvedValue('6000');
    
    const port = await loadOrFindPort();
    
    // Vérifier que le port sauvegardé est utilisé
    expect(port).toBe(6000);
    
    // Vérifier que forceKill a été appelé pour ce port
    expect(mockForceKill).toHaveBeenCalledWith(6000);
    
    // Vérifier que readFile a été appelé
    expect(mockReadFile).toHaveBeenCalledWith('port.txt', 'utf-8');
  });

  it('should find free port if no saved port', async () => {
    // Simuler une erreur lors de la lecture du fichier
    mockReadFile.mockRejectedValue(new Error('File not found'));
    
    // Simuler que les ports 7000 et 7001 sont occupés, mais 7002 est libre
    mockCheckPort.mockImplementation(async (port) => {
      return port < 7002; // Les ports < 7002 sont occupés
    });
    
    const port = await loadOrFindPort();
    
    // Vérifier que le premier port libre est utilisé
    expect(port).toBe(7002);
    
    // Vérifier que checkPort a été appelé pour les ports 7000, 7001 et 7002
    expect(mockCheckPort).toHaveBeenCalledWith(7000);
    expect(mockCheckPort).toHaveBeenCalledWith(7001);
    expect(mockCheckPort).toHaveBeenCalledWith(7002);
    
    // Vérifier que writeFile a été appelé pour sauvegarder le port
    expect(mockWriteFile).toHaveBeenCalledWith('port.txt', '7002');
  });

  it('should throw error if no port available', async () => {
    // Simuler une erreur lors de la lecture du fichier
    mockReadFile.mockRejectedValue(new Error('File not found'));
    
    // Simuler que tous les ports sont occupés
    mockCheckPort.mockResolvedValue(true);
    
    // Vérifier que la fonction lance une erreur
    await expect(loadOrFindPort()).rejects.toThrow('Aucun port disponible');
    
    // Vérifier que checkPort a été appelé pour tous les ports
    for (let port = 7000; port <= 7010; port++) {
      expect(mockCheckPort).toHaveBeenCalledWith(port);
    }
  });
});
