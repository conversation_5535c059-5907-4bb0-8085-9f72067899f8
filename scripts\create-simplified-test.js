#!/usr/bin/env node

/**
 * Script pour générer un nouveau test simplifié
 *
 * Usage: node scripts/create-simplified-test.js <nom-du-test> [options]
 *
 * Options:
 *   --path=<chemin>    Chemin où créer le test (par défaut: test/)
 *   --type=<type>      Type de test (service, api, integration, etc.)
 *   --help             Afficher l'aide
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Analyser les arguments
const args = process.argv.slice(2);
if (args.length === 0 || args.includes('--help')) {
  console.log(`
Usage: node scripts/create-simplified-test.js <nom-du-test> [options]

Options:
  --path=<chemin>    Chemin où créer le test (par défaut: test/)
  --type=<type>      Type de test (service, api, integration, etc.)
  --help             Afficher l'aide

Exemples:
  node scripts/create-simplified-test.js userService
  node scripts/create-simplified-test.js authApi --path=tests/integration/api --type=api
  `);
  process.exit(0);
}

// Extraire le nom du test
const testName = args[0];

// Extraire les options
let testPath = 'test';
let testType = 'service';

args.slice(1).forEach(arg => {
  if (arg.startsWith('--path=')) {
    testPath = arg.substring(7);
  } else if (arg.startsWith('--type=')) {
    testType = arg.substring(7);
  }
});

// Convertir le nom du test en PascalCase pour le nom de la classe
function toPascalCase(str) {
  return str
    .split(/[-_]/)
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

// Convertir le nom du test en kebab-case pour le nom du fichier
function toKebabCase(str) {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

const className = toPascalCase(testName);
const fileName = `${toKebabCase(testName)}.simplified.test.js`;
const filePath = path.join(testPath, fileName);

// Déterminer le chemin relatif vers les utilitaires de mock
const relativePath = path.relative(testPath, 'test/utils').replace(/\\/g, '/');
const mockImportPath = relativePath ? `${relativePath}/simpleMocks.js` : './utils/simpleMocks.js';

// Déterminer les imports nécessaires en fonction du type de test
let imports = `import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from '${mockImportPath}';`;

if (testType === 'api') {
  imports = `import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock, createRouterMock } from '${mockImportPath}';`;
} else if (testType === 'integration') {
  imports = `import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock, createRouterMock, createModuleMock } from '${mockImportPath}';`;
} else if (testType === 'port') {
  imports = `import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock, createNetMock } from '${mockImportPath}';`;
}

// Créer le contenu du fichier de test
const testContent = `/**
 * Tests ultra-simplifiés pour ${testName}
 *
 * Ce fichier contient des tests extrêmement simples pour ${testName},
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
${imports}

// Mock du logger
const loggerMock = createLoggerMock();

// Classe simplifiée pour ${testName}
class ${className} {
  constructor(options = {}) {
    this.options = {
      // Options par défaut
      ...options
    };

    this.logger = options.logger || loggerMock;
    // Autres propriétés
  }

  // Méthodes simplifiées
  async exampleMethod(param) {
    this.logger.info(\`Méthode exemple appelée avec \${param}\`);
    return { status: 'success', param };
  }
}

console.log('\\n=== DÉBUT DES TESTS ${testName.toUpperCase()} SIMPLIFIÉS ===\\n');

describe('${className} (Simplifié)', () => {
  let instance;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();

    // Créer une nouvelle instance
    instance = new ${className}({ logger: loggerMock });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('devrait appeler la méthode exemple', async () => {
    // Arrange
    const param = 'test';

    // Act
    const result = await instance.exampleMethod(param);

    // Assert
    expect(result).toEqual({ status: 'success', param: 'test' });
    expect(loggerMock.info).toHaveBeenCalledWith(expect.stringContaining('Méthode exemple appelée avec test'));
  });

  // TODO: Ajouter d'autres tests spécifiques à ${testName}
});

console.log('\\n=== FIN DES TESTS ${testName.toUpperCase()} SIMPLIFIÉS ===\\n');
`;

// Créer le répertoire s'il n'existe pas
if (!fs.existsSync(testPath)) {
  fs.mkdirSync(testPath, { recursive: true });
}

// Vérifier si le fichier existe déjà
if (fs.existsSync(filePath)) {
  console.error(`Erreur: Le fichier ${filePath} existe déjà.`);
  process.exit(1);
}

// Écrire le fichier
fs.writeFileSync(filePath, testContent);

console.log(`Test simplifié créé avec succès: ${filePath}`);
console.log(`
Pour exécuter ce test:
node node_modules/vitest/vitest.mjs run ${filePath}
`);
