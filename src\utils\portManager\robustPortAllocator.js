import { execSync } from 'child_process';
import logger from '../logger.js';
import { readFileSync } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

// Configuration par défaut
const DEFAULT_CONFIG = {
  server: {
    portCheck: {
      retryStrategy: {
        maxAttempts: 5,
        delayMs: 1000
      },
      alternatePorts: [50000, 50001, 50002, 50003, 50004]
    }
  }
};

let config = DEFAULT_CONFIG;

try {
  const configPath = path.resolve(__dirname, '../../../core/config/default.json');
  config = JSON.parse(readFileSync(configPath, 'utf8'));
  logger.debug('Configuration loaded from file');
} catch (err) {
  logger.warn('Using default port configuration - could not load config file');
}

const PORT_STRATEGY = {
  maxAttempts: config?.server?.portCheck?.retryStrategy?.maxAttempts || DEFAULT_CONFIG.server.portCheck.retryStrategy.maxAttempts,
  baseDelay: config?.server?.portCheck?.retryStrategy?.delayMs || DEFAULT_CONFIG.server.portCheck.retryStrategy.delayMs,
  timeout: 10000,
  portRange: {
    min: config?.server?.portCheck?.alternatePorts?.[0] || DEFAULT_CONFIG.server.portCheck.alternatePorts[0],
    max: config?.server?.portCheck?.alternatePorts?.[config.server.portCheck.alternatePorts.length - 1] || 
         DEFAULT_CONFIG.server.portCheck.alternatePorts[DEFAULT_CONFIG.server.portCheck.alternatePorts.length - 1]
  }
};

logger.debug('Port allocation strategy:', PORT_STRATEGY);

class PortAllocationError extends Error {
  constructor(message) {
    super(message);
    this.name = 'PortAllocationError';
  }
}

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function checkPort(port) {
  try {
    const result = execSync(`netstat -ano | findstr :${port}`).toString();
    return result.trim().length > 0;
  } catch (err) {
    return false;
  }
}

function killProcessOnPort(port) {
  try {
    const result = execSync(`netstat -ano | findstr :${port}`).toString();
    const pid = result.trim().split(/\s+/).pop();
    if (pid) {
      execSync(`taskkill /PID ${pid} /F`);
      return true;
    }
    return false;
  } catch (err) {
    logger.error(`Failed to kill process on port ${port}:`, err);
    return false;
  }
}

export async function allocatePort(startingPort) {
  const timer = setTimeout(() => {
    throw new PortAllocationError(`Port allocation timeout (${PORT_STRATEGY.timeout}ms)`);
  }, PORT_STRATEGY.timeout);

  try {
    let port = startingPort || PORT_STRATEGY.portRange.min;
    let attempts = 0;

    while (attempts < PORT_STRATEGY.maxAttempts && port <= PORT_STRATEGY.portRange.max) {
      try {
        logger.debug(`Port allocation attempt ${attempts + 1}/${PORT_STRATEGY.maxAttempts} for port ${port}`);

        if (!checkPort(port)) {
          logger.debug(`Port ${port} is available`);
          return port;
        }

        logger.warn(`Port ${port} is occupied, attempting to free...`);
        if (killProcessOnPort(port)) {
          logger.info(`Successfully freed port ${port}`);
          return port;
        }

        await delay(PORT_STRATEGY.baseDelay);
        port++;
        attempts++;
      } catch (err) {
        logger.error(`Port allocation error: ${err.message}`);
        port++;
        attempts++;
      }
    }

    throw new PortAllocationError(
      `Failed to allocate port after ${PORT_STRATEGY.maxAttempts} attempts in range ${PORT_STRATEGY.portRange.min}-${PORT_STRATEGY.portRange.max}`
    );
  } finally {
    clearTimeout(timer);
  }
}
