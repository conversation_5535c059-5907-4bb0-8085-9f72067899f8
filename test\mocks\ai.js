import { vi } from 'vitest';

export default vi.fn(() => ({
  generateText: vi.fn().mockResolvedValue('mocked response'),
  analyzeCode: vi.fn().mockResolvedValue({
    suggestions: ['Mocked suggestion'],
    qualityScore: 0.8
  }),
  init: vi.fn().mockImplementation(async () => {
    console.log('Mock AIService init called');
    return true;
  }),
  __forceFallback: vi.fn().mockImplementation((prompt) => ({
    text: `Mock fallback response to: ${prompt}`,
    tokens: 30,
    isFallback: true
  })),
  __forceError: vi.fn().mockImplementation(() => {
    throw new Error('Mock AI error');
  })
}));
