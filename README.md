# GDevAI - Plateforme d'Intelligence Artificielle pour GDevelop

![GDevAI Logo](Logos/GDevAI_Red.png)

## 📌 Table des Matières
- [Fonctionnalités](#-fonctionnalités)
- [Installation](#-installation)
- [Configuration](#-configuration)
- [Architecture](#-architecture)
- [Développement](#-développement)
- [Tests](#-tests)
- [Déploiement](#-déploiement)
- [Contribution](#-contribution)
- [Licence](#-licence)

## 🌟 Fonctionnalités

### Principales Capacités
- **Intégration GDevelop** : Connecteur API pour GDevelop
- **Modèles IA** : Support pour multiples fournisseurs d'IA
- **RBAC** : Gestion fine des permissions
- **MCP Server** : Serveur modulaire extensible

### Composants
- **Frontend** : Interface utilisateur (Vite + React)
- **Backend** : API principale (Node.js)
- **Services** : Modules métier (IA, Chat, etc.)

## 🚀 Installation

### Prérequis
- Node.js v20+
- npm v9+
- GDevelop (optionnel pour l'intégration)
- Express v5
- React Router v7

```bash
git clone https://github.com/votre-repo/GDevAI.git
cd GDevAI
npm run setup-local
```

## ⚙️ Configuration

### Fichiers Importants
- `config/roles.yaml` : Définition des rôles RBAC
- `.env` : Variables d'environnement
- `tailwind.config.js` : Configuration CSS

### Variables d'Environnement
```ini
API_KEY=votre_cle_api
PORT=3000
NODE_ENV=development
```

## 📊 État d'avancement (2025-05-09)
- **Version actuelle** : v0.6.2 (dernière mise à jour 2025-04-24)
- **Fonctionnalités stables** :
  - Système RBAC complet avec tests
  - Documentation des rôles/permissions
  - Standardisation des réponses d'erreur
- **En développement** :
  - Monitoring et métriques IA
  - Corrections des erreurs TypeScript

## 🏗️ Architecture

### Diagramme des Composants
```mermaid
graph TD
    A[Frontend] --> B[Backend]
    B --> C[Services]
    C --> D[Base de Données]
    C --> E[API GDevelop]
    C --> F[Modèles IA]
```

### Structure des Répertoires
```
src/
├── frontend/    # Application React
├── backend/     # API Node.js
└── utils/       # Utilitaires partagés
```

## 💻 Développement

### Commandes Principales
```bash
npm start       # Lance tous les services
npm run dev     # Mode développement
npm run lint    # Vérification du code
```

### Workflow Recommandé
1. Créer une branche pour chaque fonctionnalité
2. Écrire des tests unitaires
3. Vérifier avec `npm test`
4. Soumettre une Pull Request

## 🧪 Tests

### Stratégie de Test
- **Tests Simplifiés** : 18 tests avec 100% de réussite (approche de simplification)
- **Unitaires** : Vitest (100% couverture)
- **Intégration** : Tests API
- **E2E** : Cypress (à venir)

### Tests Simplifiés - Nouvelle Approche ✨
Notre approche révolutionnaire de simplification des tests a transformé la suite de tests :
- **18 tests simplifiés** créés avec 100% de réussite
- **0 erreur** de port, mock ou dépendance
- **Stabilité parfaite** et reproductibilité garantie
- **Philosophie** : "Dès qu'il y a un problème, réflexe : simplification"

```bash
# Exécuter tous les tests simplifiés
node scripts/test-all-simplified.js

# Créer un nouveau test simplifié
node scripts/create-simplified-test.js [nom-du-test]
```

### Documentation des Tests
- **[Guide des tests simplifiés](./docs/SIMPLIFIED_TESTING_GUIDE.md)** - Nouvelle approche recommandée
- [Guide des bonnes pratiques](./TEST_DOCUMENTATION.md)
- [Gestion des logs](./LOGGING_GUIDELINES.md)

```bash
npm test        # Exécute tous les tests
npm test:watch  # Mode surveillance
```

## 🚀 Déploiement

### Options
1. **Local** : `npm start`
2. **Docker** : `docker-compose up`
3. **Cloud** : Scripts de déploiement AWS/GCP

## 💾 Système de Sauvegarde

### Fonctionnalités
- Compression optimale avec 7-Zip (LZMA2)
- Fallback sur PowerShell si 7-Zip absent
- Rotation automatique des sauvegardes (7j/4s/12m)

### Commandes
```bash
# Lancer une sauvegarde manuelle
node scripts/backup.js

# Options de configuration (dans .env)
SEVEN_ZIP_PATH="C:\Chemin\Personnalisé\7z.exe"
```

### Stratégie de Rotation
- 7 sauvegardes quotidiennes
- 4 sauvegardes hebdomadaires
- 12 sauvegardes mensuelles

### Documentation Technique
- [Guide de compression](./docs/BACKUP_GUIDE.md)

## 🤝 Contribution

### Processus
1. Forker le dépôt
2. Créer une branche (`git checkout -b feature/ma-fonctionnalité`)
3. Commiter (`git commit -m 'Ajout d'une super fonctionnalité'`)
4. Pusher (`git push origin feature/ma-fonctionnalité`)
5. Ouvrir une Pull Request

### Bonnes Pratiques
- Respecter les conventions de code
- Documenter les nouvelles fonctionnalités
- Maintenir la couverture de test

## 📜 Licence

MIT License - Voir [LICENSE](LICENSE) pour plus de détails.
