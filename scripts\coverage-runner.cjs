const { execSync } = require('child_process')

try {
  console.log('Cleaning coverage directory...')
  execSync('if exist coverage rmdir /s /q coverage', { stdio: 'inherit' })
  
  console.log('Running tests with coverage...')
  execSync('npx vitest run --coverage', { 
    stdio: 'inherit',
    env: { 
      ...process.env,
      CI: 'true',
      NODE_OPTIONS: '--no-warnings'
    }
  })
} catch (error) {
  console.error('Error generating coverage:', error)
  process.exit(1)
}
