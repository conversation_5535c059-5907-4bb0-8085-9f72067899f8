import { vi } from 'vitest';

// Mock global RBAC avant tout import de service
vi.mock('../../mcp-server/utils/rbac', () => {
  console.log('--- Global RBAC Mock Applied ---');
  return {
    hasPermission: vi.fn().mockImplementation((user, permission) => {
      console.log(`Mock RBAC called with ${user?.username}, ${permission}`);
      return true;
    })
  };
});
import loggerMock from './mocks/logger';
import GDevelopAPIMock from './mocks/gdevelop';

vi.mock('#logger', () => loggerMock);
vi.mock('../integrations/gdevelop/api', () => ({
  default: GDevelopAPIMock
}));
import logger from './mocks/logger';

import * as mockAiMetrics from './mocks/aiMetrics.js';
import mockDeepseekService from './mocks/deepseekService.js';
import * as mockAIService from './mocks/ai.js';

// Mock aiMetrics avec export par défaut
vi.mock('../../src/utils/aiMetrics', async () => {
  const mod = await import('./mocks/aiMetrics');
  return {
    ...mod,
    default: mod.default,
    logAIMetrics: mod.logAIMetrics,
    trackRequest: mod.trackRequest,
    getAIStats: mod.getAIStats
  };
});

// Mock DeepseekService
vi.mock('../../src/backend/services/deepseekService', () => ({
  default: vi.fn().mockImplementation(() => ({
    generateText: vi.fn(),
    analyzeCode: vi.fn(),
    init: vi.fn().mockResolvedValue(true)
  }))
}));

// Mock AIService
vi.mock('../../src/backend/services/aiService', () => ({
  default: vi.fn().mockImplementation(() => ({
    processRequest: vi.fn()
  }))
}));

// Mock simplifié du logger
vi.mock('../../src/utils/logger.js', () => ({
  __esModule: true,
  default: {
    error: vi.fn(),
    debug: vi.fn(),
    info: vi.fn(),
    warn: vi.fn()
  },
  logError: vi.fn(),
  logRequest: vi.fn()
}));

if (process.env.DEBUG_MOCKS === 'true') {
  console.log('Vitest setup complete - Mocks initialized');
}
