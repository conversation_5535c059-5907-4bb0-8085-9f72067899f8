# Changelog GDevAI

## [v0.9.0] - 2025-05-29
### Added
- Documentation complète du système de gestion des ports
- Tests unitaires pour MCPServer
  - Vérification des ports occupés/libres
  - Gestion des ports invalides
  - Tests de kill process

### Fixed
- Correction de l'export de MCPServer
- Validation renforcée des numéros de port
- Gestion des erreurs améliorée
- Configuration manquante alternatePorts ajoutée
- Résolution des conflits de ports dans les tests d'intégration du core
- Simplification du système d'allocation de ports (auto-assignation)
- Amélioration de la fiabilité des tests d'intégration

## [v0.8.0] - 2025-05-12
### Added
- Sauvegarde automatisée avec tâches planifiées
  - Exécution quotidienne à 02:00
  - Vérification au démarrage
- Nouvelle API backup avec endpoints:
  - /run (lancement manuel)
  - /status (état des sauvegardes)
  - /logs (consultation des logs)
- Documentation complète dans BACKUP_GUIDE.md
- Tests unitaires pour le système de sauvegarde

### Fixed
- Amélioration de la gestion des erreurs
- Optimisation de l'espace disque utilisé

## [v0.7.0] - 2025-05-12
### Added
- Système de sauvegarde avec compression 7-Zip (LZMA2)
- Documentation complète du backup (README + BACKUP_GUIDE)
- Tests d'intégrité des archives
- Rotation automatique des sauvegardes (7j/4s/12m)
- Fallback PowerShell si 7-Zip absent

[Le reste du contenu original...]
