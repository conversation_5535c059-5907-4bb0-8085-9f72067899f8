/**
 * Tests ultra-simplifiés pour GDevelop Module
 *
 * Ce fichier contient des tests extrêmement simples pour le module GDevelop,
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from './utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Classe GDevelop Module simplifiée pour les tests
class SimplifiedGDevelopModule {
  constructor(options = {}) {
    this.logger = options.logger || loggerMock;
    this.name = 'gdevelop';
    this.version = '1.0.0';
    this.projects = new Map();
    this.resources = new Map();
  }

  async getProject(projectId) {
    if (!projectId) {
      throw new Error('Project ID is required');
    }

    this.logger.debug(`Getting project: ${projectId}`);

    // Simuler la récupération d'un projet
    const project = this.projects.get(projectId) || {
      id: projectId,
      name: `Mock project ${projectId}`,
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      scenes: [],
      objects: [],
      events: []
    };

    this.projects.set(projectId, project);
    return project;
  }

  async updateProject(projectData) {
    if (!projectData || !projectData.id) {
      throw new Error('Project data with ID is required');
    }

    this.logger.debug(`Updating project: ${projectData.id}`);

    const existingProject = this.projects.get(projectData.id);
    const updatedProject = {
      ...existingProject,
      ...projectData,
      updatedAt: new Date().toISOString()
    };

    this.projects.set(projectData.id, updatedProject);
    return true;
  }

  async syncResources(projectId = null) {
    this.logger.debug(`Syncing resources for project: ${projectId || 'all'}`);

    const mockResources = [
      'sprite1.png',
      'background.jpg',
      'sound1.wav',
      'music.mp3'
    ];

    if (projectId) {
      this.resources.set(projectId, mockResources);
    }

    return {
      success: true,
      count: mockResources.length,
      resources: mockResources,
      syncedAt: new Date().toISOString()
    };
  }

  async deleteProject(projectId) {
    if (!projectId) {
      throw new Error('Project ID is required');
    }

    this.logger.debug(`Deleting project: ${projectId}`);

    const existed = this.projects.has(projectId);
    this.projects.delete(projectId);
    this.resources.delete(projectId);

    return {
      success: existed,
      deletedAt: new Date().toISOString()
    };
  }

  async listProjects() {
    this.logger.debug('Listing all projects');

    return Array.from(this.projects.values());
  }

  // Méthode pour forcer une erreur (pour les tests)
  __forceError(message = 'Mock error') {
    throw new Error(message);
  }

  // Méthode pour réinitialiser l'état (pour les tests)
  __reset() {
    this.projects.clear();
    this.resources.clear();
  }
}

console.log('\n=== DÉBUT DES TESTS GDEVELOP MODULE SIMPLIFIÉS ===\n');

describe('GDevelop Module (Simplifié)', () => {
  let gdevelopModule;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();
    
    // Créer une nouvelle instance
    gdevelopModule = new SimplifiedGDevelopModule({
      logger: loggerMock
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Gestion des projets', () => {
    it('devrait récupérer un projet avec succès', async () => {
      // Arrange
      const projectId = 'test123';

      // Act
      const project = await gdevelopModule.getProject(projectId);

      // Assert
      expect(project).toMatchObject({
        id: projectId,
        name: `Mock project ${projectId}`,
        version: '1.0.0',
        createdAt: expect.any(String),
        scenes: expect.any(Array),
        objects: expect.any(Array),
        events: expect.any(Array)
      });
      expect(loggerMock.debug).toHaveBeenCalledWith(`Getting project: ${projectId}`);
    });

    it('devrait rejeter si le projectId est manquant', async () => {
      // Act & Assert
      await expect(gdevelopModule.getProject()).rejects.toThrow('Project ID is required');
    });

    it('devrait mettre à jour un projet avec succès', async () => {
      // Arrange
      const projectData = {
        id: 'test123',
        name: 'Updated Project',
        description: 'New description'
      };

      // Act
      const result = await gdevelopModule.updateProject(projectData);

      // Assert
      expect(result).toBe(true);
      expect(loggerMock.debug).toHaveBeenCalledWith(`Updating project: ${projectData.id}`);

      // Vérifier que le projet a été mis à jour
      const updatedProject = await gdevelopModule.getProject(projectData.id);
      expect(updatedProject.name).toBe('Updated Project');
      expect(updatedProject.description).toBe('New description');
      expect(updatedProject.updatedAt).toBeDefined();
    });

    it('devrait rejeter la mise à jour sans données valides', async () => {
      // Act & Assert
      await expect(gdevelopModule.updateProject({})).rejects.toThrow('Project data with ID is required');
      await expect(gdevelopModule.updateProject(null)).rejects.toThrow('Project data with ID is required');
    });

    it('devrait supprimer un projet avec succès', async () => {
      // Arrange
      const projectId = 'test123';
      await gdevelopModule.getProject(projectId); // Créer le projet d'abord

      // Act
      const result = await gdevelopModule.deleteProject(projectId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.deletedAt).toBeDefined();
      expect(loggerMock.debug).toHaveBeenCalledWith(`Deleting project: ${projectId}`);
    });

    it('devrait lister tous les projets', async () => {
      // Arrange
      await gdevelopModule.getProject('project1');
      await gdevelopModule.getProject('project2');

      // Act
      const projects = await gdevelopModule.listProjects();

      // Assert
      expect(projects).toHaveLength(2);
      expect(projects[0].id).toBe('project1');
      expect(projects[1].id).toBe('project2');
      expect(loggerMock.debug).toHaveBeenCalledWith('Listing all projects');
    });
  });

  describe('Gestion des ressources', () => {
    it('devrait synchroniser les ressources avec succès', async () => {
      // Act
      const result = await gdevelopModule.syncResources();

      // Assert
      expect(result).toMatchObject({
        success: true,
        count: 4,
        resources: expect.arrayContaining(['sprite1.png', 'background.jpg', 'sound1.wav', 'music.mp3']),
        syncedAt: expect.any(String)
      });
      expect(loggerMock.debug).toHaveBeenCalledWith('Syncing resources for project: all');
    });

    it('devrait synchroniser les ressources pour un projet spécifique', async () => {
      // Arrange
      const projectId = 'test123';

      // Act
      const result = await gdevelopModule.syncResources(projectId);

      // Assert
      expect(result.success).toBe(true);
      expect(result.count).toBe(4);
      expect(loggerMock.debug).toHaveBeenCalledWith(`Syncing resources for project: ${projectId}`);
    });
  });

  describe('Gestion des erreurs', () => {
    it('devrait forcer une erreur quand demandé', () => {
      // Act & Assert
      expect(() => gdevelopModule.__forceError()).toThrow('Mock error');
    });

    it('devrait forcer une erreur avec un message personnalisé', () => {
      // Arrange
      const customMessage = 'Custom error message';

      // Act & Assert
      expect(() => gdevelopModule.__forceError(customMessage)).toThrow(customMessage);
    });
  });

  describe('Utilitaires de test', () => {
    it('devrait réinitialiser l\'état du module', async () => {
      // Arrange
      await gdevelopModule.getProject('test1');
      await gdevelopModule.syncResources('test1');

      // Act
      gdevelopModule.__reset();

      // Assert
      const projects = await gdevelopModule.listProjects();
      expect(projects).toHaveLength(0);
    });
  });
});

console.log('\n=== FIN DES TESTS GDEVELOP MODULE SIMPLIFIÉS ===\n');
