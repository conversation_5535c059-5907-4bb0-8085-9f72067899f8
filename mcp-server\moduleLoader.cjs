const fs = require('fs').promises;
const path = require('path');
const { pathToFileURL } = require('url');

class ModuleLoader {
  constructor() {
    this.modules = new Map();
    this.modulesPath = path.join(__dirname, 'mcp-modules');
  }

  async loadModules() {
    try {
      const moduleDirs = await fs.readdir(this.modulesPath, { withFileTypes: true });
      
      for (const dirent of moduleDirs) {
        if (!dirent.isDirectory()) continue;
        
        const modulePath = path.join(this.modulesPath, dirent.name);
        const manifestPath = path.join(modulePath, 'manifest.json');
        
        try {
          const manifest = JSON.parse(await fs.readFile(manifestPath, 'utf8'));
          if (manifest.active !== false) {
            const module = {
              name: manifest.name,
              version: manifest.version,
              path: modulePath,
              manifest,
              routes: manifest.type === 'common' ? null : 
                     manifest.type === 'esm' ? (await import(pathToFileURL(path.join(modulePath, manifest.routes)))).default :
                     require(path.join(modulePath, manifest.routes))
            };
            
            if (manifest.type !== 'common') {
              this.modules.set(manifest.name, module);
            }
            console.log(`Module chargé: ${manifest.name} v${manifest.version}`);
          }
        } catch (err) {
          console.error(`Erreur chargement module ${dirent.name}:`, err);
        }
      }
    } catch (err) {
      console.error('Erreur lecture dossier modules:', err);
    }
  }

  getModule(name) {
    return this.modules.get(name);
  }

  getAllModules() {
    return Array.from(this.modules.values());
  }
}

module.exports = ModuleLoader;
