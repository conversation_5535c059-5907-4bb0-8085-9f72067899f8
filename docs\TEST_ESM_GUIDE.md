# Guide des Tests ESM

## Workflow Standardisé pour les Mocks

### 1. Création d'un Mock
Utilisez le générateur :
```bash
node scripts/generate-mock.mjs mcp-server/mcp-modules/chat/routes.mjs
```

### 2. Structure du Mock
```javascript
import { createESMock } from '../test/__mocks__/esm-template.mjs';

export default createESMock('module/path', {
  // Implémentation
  myFunction: vi.fn().mockImplementation(() => {
    // Logique mockée
  })
});
```

### 3. Utilisation dans les Tests
```javascript
import mockModule from '../__mocks__/routes.mock.mjs';

beforeEach(() => {
  vi.mock('module/path', () => mockModule);
});
```

## Bonnes Pratiques
- Toujours utiliser `createESMock` pour la cohérence
- Documenter le comportement mocké
- Vérifier les appels avec `toHaveBeenCalledWith`
- Isoler les tests avec `vi.clearAllMocks()`

## Exemple Complet
Voir [test/mcp-health.test.js] et son mock associé
