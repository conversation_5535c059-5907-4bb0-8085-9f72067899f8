import DeepseekService from '../../src/backend/services/deepseekService.js';
import AIService from '../../src/backend/services/aiService.js'; // Fallback
import logger from '#logger';
import { trackRequest } from '../../src/utils/aiMetrics.js';
import { readFile } from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));
const configPath = path.join(__dirname, '../config/default.json');
const configContent = await readFile(configPath, 'utf8');
const config = JSON.parse(configContent);

class AIModule {
  async initialize() {
    try {
      this.service = new DeepseekService();
      this.logger = logger;
      this.logger.info('DeepseekService initialisé');
    } catch (error) {
      this.service = new AIService({ logger }); // Fallback
      this.logger = logger;
      this.logger.warn('Utilisation du fallback AIService');
    }
  }

  async process(payload) {
    try {
      switch(payload.action) {
        case 'generateText':
          return this.handleTextGeneration(payload);
        case 'analyzeCode':
          return this.handleCodeAnalysis(payload);
        default:
          throw new Error(`Action non supportée: ${payload.action}`);
      }
    } catch (error) {
      this.logger.error('Erreur dans le module IA:', error);
      throw error;
    }
  }

  async handleTextGeneration(payload) {
    const { prompt, context } = payload;
    const startTime = Date.now();
    let success = true;
    let response;
    
    try {
      response = await this.service.generateText(prompt, context);
    } catch (error) {
      success = false;
      throw error;
    } finally {
      const provider = this.service.constructor.name.toLowerCase().includes('deepseek') 
        ? 'deepseek' 
        : 'openai';
        
      trackRequest({
        provider,
        responseTime: Date.now() - startTime,
        success,
        tokens: response?.length || 0,
        model: config.modules.ia.model
      });
    }
    
    return {
      data: {
        text: response,
        contextId: context?.id || null
      },
      metadata: {
        timestamp: new Date().toISOString(),
        isAI: true,
        source: this.service.constructor.name
      }
    };
  }

  async handleCodeAnalysis(payload) {
    const { code, language } = payload;
    const startTime = Date.now();
    let success = true;
    let response;
    
    try {
      response = await this.service.analyzeCode(code, language);
    } catch (error) {
      success = false;
      throw error;
    } finally {
      const provider = this.service.constructor.name.toLowerCase().includes('deepseek') 
        ? 'deepseek' 
        : 'openai';
        
      trackRequest({
        provider,
        responseTime: Date.now() - startTime,
        success,
        tokens: response?.length || 0,
        model: config.modules.ia.model
      });
    }
    
    return {
      data: {
        analysis: response,
        language: language
      },
      metadata: {
        timestamp: new Date().toISOString(),
        isAI: true,
        source: this.service.constructor.name
      }
    };
  }
}

// Export as ES module
export default AIModule;

// Méthode statique pour création asynchrone
AIModule.create = async () => {
  const instance = new AIModule();
  await instance.initialize();
  return instance;
};
