# Documentation Avancée des Mocks ESM

## Architecture Technique

```mermaid
classDiagram
    class ESM_Mock {
        +originalModule: Module
        +mockImplementations: Map
        +restore()
        +mockImplementation(method, impl)
    }
    
    class VirtualFS {
        +files: Map
        +readFile(path): string
        +writeFile(path, content)
    }
    
    class NetworkMock {
        +servers: Map
        +createServer(port): MockServer
    }
    
    ESM_Mock --> VirtualFS
    ESM_Mock --> NetworkMock
```

## Exemples Concrets

### Mocking de `fs` en ESM
```javascript
// test/__mocks__/fs.js
import { vi } from 'vitest';

const mockFS = new Map();

export default {
  readFileSync: vi.fn((path) => {
    if (!mockFS.has(path)) throw new Error('File not found');
    return mockFS.get(path);
  }),
  
  writeFileSync: vi.fn((path, content) => {
    mockFS.set(path, content);
  })
};
```

### Gestion des Imports Dynamiques
```javascript
// test/setupMocks.mjs
export async function mockDynamicImport(modulePath, mockImpl) {
  const original = await import(modulePath);
  vi.doMock(modulePath, () => ({
    ...original,
    ...mockImpl
  }));
}
```

## Tableau des Solutions Spécifiques ESM

| Problème | Solution ESM | Exemple |
|----------|-------------|---------|
| Top-level await | Mock asynchrone | `vi.mock('module', async () => ({...}))` |
| Circular imports | Injection dynamique | `vi.hoisted(() => {...})` |
| Default vs named | Transformation explicite | `export default { ...namedExports }` |

[//]: # (Additional ESM-specific patterns will be added)
