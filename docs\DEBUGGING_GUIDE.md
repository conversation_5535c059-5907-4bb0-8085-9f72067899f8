# Guide Avancé de Débogage des Tests ESM

## Techniques de Diagnostic

```mermaid
flowchart TD
    A[Test Failing] --> B{Logs?}
    B -->|Yes| C[Analyze Stack Trace]
    B -->|No| D[Enable Verbose Logging]
    C --> E[Isolate Failure]
    D --> E
    E --> F[Check Mock States]
    F --> G[Reproduce in Isolation]
```

## Outils Spécifiques

### 1. Inspection des Mocks
```javascript
// Afficher l'état d'un mock
console.log(vi.mocked(module.method).mock.calls);
```

### 2. Couverture Dynamique
```bash
npm run test:cov -- --watch
```

### 3. Profileur Mé<PERSON>ire
```javascript
// test/utils/memoryProfiler.mjs
export async function profileTest(testFn) {
  const start = process.memoryUsage();
  await testFn();
  const end = process.memoryUsage();
  return {
    heapDiff: end.heapUsed - start.heapUsed,
    details: { start, end }
  };
}
```

## Tableau de Référence <PERSON>

| Symptôme | Outil | Commande |
|----------|-------|----------|
| Mock non appliqué | Vérif ESM | `vi.mock('module', {}, { virtual: true })` |
| Fuite mémoire | Heap Snapshot | `node --inspect test.js` |
| Test asynchrone | Timeline | `--test-timeout=5000` |

## Intégration avec la Documentation Existante

Consultez aussi :
- [Documentation des Mocks ESM](./TEST_MOCKS_ESM.md)
- [Guide Principal des Tests](./TEST_DOCUMENTATION.md)

[//]: # (Additional debugging scenarios will be added)
