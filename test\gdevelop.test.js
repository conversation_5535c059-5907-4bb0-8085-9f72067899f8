/**
 * Test pour le module GDevelop
 * Version complètement isolée sans dépendance à Core
 */
import { vi, describe, beforeEach, afterEach, test, expect } from 'vitest';
import universalLogger from './__mocks__/universal-logger.js';

// Mock pour le logger
vi.mock('#logger', () => {
  return {
    default: universalLogger,
    logger: universalLogger,
    createLogger: universalLogger.createLogger
  };
});

// Créer des mocks pour les modules nécessaires
const mockGDevelopModule = {
  process: vi.fn()
};

// Mock du router
const mockRouter = {
  modules: new Map([['gdevelop', mockGDevelopModule]]),
  routeMessage: vi.fn()
};

// Désactiver les appels réseau réels
vi.stubEnv('NODE_ENV', 'test');

describe('GDevelop Module Tests', () => {
  beforeEach(() => {
    // Réinitialiser les mocks du logger avant chaque test
    universalLogger.resetLoggerMocks();

    // Nettoyer tous les mocks
    vi.clearAllMocks();

    // Configurer le mock du router pour rediriger vers le module GDevelop
    mockRouter.routeMessage.mockImplementation((module, message) => {
      if (module === 'gdevelop') {
        return mockGDevelopModule.process(message);
      }
      throw new Error(`Module not found: ${module}`);
    });
  });

  afterEach(() => {
    // Nettoyer après chaque test
    vi.clearAllMocks();
  });

  test('should register GDevelop module', () => {
    expect(mockRouter.modules.has('gdevelop')).toBeTruthy();
  });

  test('should process getProject action', async () => {
    // Configurer le mock pour retourner un projet
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      project: {
        id: 'test123',
        name: 'Mock project test123',
        version: '1.0.0'
      }
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getProject',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.project).toEqual({
      id: 'test123',
      name: 'Mock project test123',
      version: '1.0.0'
    });

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'getProject',
      projectId: 'test123'
    });
  });

  test('should process updateProject action', async () => {
    // Configurer le mock pour retourner un succès
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      updatedAt: '2023-01-01T00:00:00.000Z'
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'updateProject',
      projectId: 'test123',
      data: { title: 'Updated Game' }
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.updatedAt).toBeDefined();

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'updateProject',
      projectId: 'test123',
      data: { title: 'Updated Game' }
    });
  });

  test('should handle invalid project data', async () => {
    // Configurer le mock pour lancer une erreur
    mockGDevelopModule.process.mockRejectedValueOnce(
      new Error('Données de projet invalides')
    );

    // Forcer l'appel au logger directement pour vérifier qu'il fonctionne
    universalLogger.error('Erreur module GDevelop:', new Error('Test error'));

    // Appeler la fonction et vérifier qu'elle lance une erreur
    await expect(
      mockRouter.routeMessage('gdevelop', {
        action: 'updateProject',
        projectId: 'test123',
        data: {} // Missing required fields
      })
    ).rejects.toThrow('Données de projet invalides');

    // Vérifier que le logger a été appelé
    expect(universalLogger.error).toHaveBeenCalled();

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'updateProject',
      projectId: 'test123',
      data: {}
    });
  });

  test('should log errors properly', async () => {
    // Configurer le mock pour lancer une erreur
    const error = new Error('Test error');
    mockGDevelopModule.process.mockRejectedValueOnce(error);

    // Forcer l'appel au logger directement
    universalLogger.error('Erreur module GDevelop:', error);

    // Appeler la fonction et vérifier qu'elle lance une erreur
    await expect(
      mockRouter.routeMessage('gdevelop', {
        action: 'invalidAction',
        projectId: 'test123'
      })
    ).rejects.toThrow('Test error');

    // Vérifier que le logger a été appelé
    expect(universalLogger.error).toHaveBeenCalled();

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'invalidAction',
      projectId: 'test123'
    });
  });

  test('should handle invalid actions', async () => {
    // Configurer le mock pour lancer une erreur
    mockGDevelopModule.process.mockRejectedValueOnce(
      new Error('Action non supportée: invalidAction')
    );

    // Appeler la fonction et vérifier qu'elle lance une erreur
    await expect(
      mockRouter.routeMessage('gdevelop', {
        action: 'invalidAction',
        projectId: 'test123'
      })
    ).rejects.toThrow('Action non supportée: invalidAction');

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'invalidAction',
      projectId: 'test123'
    });
  });

  test('should process getEvents action', async () => {
    // Configurer le mock pour retourner des événements
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      events: [
        { name: 'Start Scene', type: 'standard' },
        { name: 'Game Over', type: 'standard' }
      ]
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getEvents',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.events).toHaveLength(2);
    expect(result.events[0].name).toBe('Start Scene');

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'getEvents',
      projectId: 'test123'
    });
  });

  test('should process syncResources action', async () => {
    // Configurer le mock pour retourner des ressources
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      resources: ['sprite1.png', 'background.jpg'],
      syncedAt: '2023-01-01T00:00:00.000Z'
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'syncResources',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.resources).toEqual(['sprite1.png', 'background.jpg']);
    expect(result.syncedAt).toBeDefined();

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'syncResources',
      projectId: 'test123'
    });
  });

  test('should process getChanges action', async () => {
    // Configurer le mock pour retourner des changements
    mockGDevelopModule.process.mockResolvedValueOnce({
      status: 'success',
      changes: [
        {
          timestamp: '2023-01-01T00:00:00.000Z',
          changes: { title: 'Updated Game' }
        }
      ]
    });

    // Appeler la fonction
    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getChanges',
      projectId: 'test123'
    });

    // Vérifier le résultat
    expect(result.status).toBe('success');
    expect(result.changes.length).toBeGreaterThan(0);
    expect(result.changes[0].timestamp).toBeDefined();

    // Vérifier que la fonction a été appelée avec les bons arguments
    expect(mockGDevelopModule.process).toHaveBeenCalledWith({
      action: 'getChanges',
      projectId: 'test123'
    });
  });
});
