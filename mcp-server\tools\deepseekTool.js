import axios from 'axios';

const DEEPSEEK_API_URL = 'https://api.deepseek.com/v3';
const apiKey = process.env.DEEPSEEK_API_KEY;

export default (router) => {
  console.log('[DeepSeek] Initialisation de l\'outil...');
  console.log('[DeepSeek] Clé API:', process.env.DEEPSEEK_API_KEY ? 'définie' : 'non définie');
  console.log('[DeepSeek] Configuration:', { DEEPSEEK_API_URL });
  router.post('/deepseek', async (req, res) => {
    console.log('[DeepSeek] Test endpoint - Headers:', req.headers);
    console.log('[DeepSeek] Requête reçue:', req.body.prompt);
    try {
      const response = await axios.post(`${DEEPSEEK_API_URL}/chat/completions`, {
        model: "deepseek-chat-v3",
        messages: [{ role: "user", content: req.body.prompt }],
        temperature: 0.7
      }, {
        headers: { 
          'Authorization': `Bearer ${apiKey}`,
          'Content-Type': 'application/json'
        }
      });
      res.json(response.data);
    } catch (error) {
      res.status(500).json({ 
        error: error.response?.data?.error?.message || error.message 
      });
    }
  });
};
