import { vi, describe, beforeEach, test, expect } from 'vitest';

// Mock simple du logger
const mockLogger = {
  info: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
  child: vi.fn(() => mockLogger),
  resetLoggerMocks: vi.fn(() => {
    mockLogger.info.mockClear();
    mockLogger.error.mockClear();
    mockLogger.warn.mockClear();
    mockLogger.debug.mockClear();
  })
};

vi.mock('#logger', () => ({
  default: mockLogger
}));

// Mock simple de l'API GDevelop
const mockGDevelopAPI = {
  getProject: vi.fn().mockImplementation((id) => ({
    id,
    name: `Mock project ${id}`,
    version: '1.0.0'
  })),
  updateProject: vi.fn().mockReturnValue(true)
};

vi.mock('../../../integrations/gdevelop/api.js', () => ({
  default: mockGDevelopAPI
}));

// Mock simple du router
const mockRouter = {
  modules: new Map(),
  routeMessage: vi.fn()
};

describe('GDevelop Module Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLogger.resetLoggerMocks();

    // Configurer le module gdevelop dans le router
    mockRouter.modules.set('gdevelop', {
      process: vi.fn()
    });
  });

  test('should register GDevelop module', () => {
    expect(mockRouter.modules.has('gdevelop')).toBeTruthy();
  });

  test('should process getProject action', async () => {
    // Configurer le mock pour retourner un résultat de succès
    mockRouter.routeMessage.mockResolvedValue({
      status: 'success',
      project: {
        id: 'test123',
        title: 'Test Game',
        objects: []
      }
    });

    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getProject',
      projectId: 'test123'
    });

    expect(result.status).toBe('success');
    expect(result.project).toEqual({
      id: 'test123',
      title: 'Test Game',
      objects: []
    });
    expect(mockRouter.routeMessage).toHaveBeenCalledWith('gdevelop', {
      action: 'getProject',
      projectId: 'test123'
    });
  });

  test('should process updateProject action', async () => {
    // Configurer le mock pour retourner un résultat de succès
    mockRouter.routeMessage.mockResolvedValue({
      status: 'success',
      updatedAt: new Date().toISOString()
    });

    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'updateProject',
      projectId: 'test123',
      data: { title: 'Updated Game' }
    });

    expect(result.status).toBe('success');
    expect(result.updatedAt).toBeDefined();
  });

  test('should handle invalid project data', async () => {
    // Configurer le mock pour rejeter avec une erreur
    mockRouter.routeMessage.mockRejectedValue(new Error('Invalid project data'));

    // Forcer l'appel au logger directement
    mockLogger.error('Erreur module GDevelop:', new Error('Test error'));

    await expect(
      mockRouter.routeMessage('gdevelop', {
        action: 'updateProject',
        projectId: 'test123',
        data: {} // Missing required fields
      })
    ).rejects.toThrow();

    // Vérifier que le logger a été appelé
    expect(mockLogger.error).toHaveBeenCalled();
  });

  test('should log errors properly', async () => {
    const error = new Error('Test error');

    // Configurer le mock pour rejeter avec une erreur
    mockRouter.routeMessage.mockRejectedValue(error);

    // Forcer l'appel au logger directement
    mockLogger.error('Erreur module GDevelop:', error);

    await expect(
      mockRouter.routeMessage('gdevelop', {
        action: 'invalidAction',
        projectId: 'test123'
      })
    ).rejects.toThrow();

    // Vérifier que le logger a été appelé
    expect(mockLogger.error).toHaveBeenCalled();
  });

  test('should handle invalid actions', async () => {
    // Configurer le mock pour rejeter avec une erreur
    mockRouter.routeMessage.mockRejectedValue(new Error('Invalid action'));

    await expect(
      mockRouter.routeMessage('gdevelop', {
        action: 'invalidAction',
        projectId: 'test123'
      })
    ).rejects.toThrow();
  });

  test('should process getEvents action', async () => {
    // Configurer le mock pour retourner des événements
    mockRouter.routeMessage.mockResolvedValue({
      status: 'success',
      events: [
        { name: 'Start Scene', type: 'scene' },
        { name: 'Player Movement', type: 'behavior' }
      ]
    });

    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getEvents',
      projectId: 'test123'
    });

    expect(result.status).toBe('success');
    expect(result.events).toHaveLength(2);
    expect(result.events[0].name).toBe('Start Scene');
  });

  test('should process syncResources action', async () => {
    // Configurer le mock pour retourner des ressources synchronisées
    mockRouter.routeMessage.mockResolvedValue({
      status: 'success',
      resources: ['image1.png', 'sound1.wav'],
      syncedAt: new Date().toISOString()
    });

    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'syncResources',
      projectId: 'test123'
    });

    expect(result.status).toBe('success');
    expect(result.resources).toBeDefined();
    expect(result.syncedAt).toBeDefined();
  });

  test('should process getChanges action', async () => {
    // Configurer le mock pour retourner des changements
    mockRouter.routeMessage.mockResolvedValue({
      status: 'success',
      changes: [
        { type: 'update', field: 'title', timestamp: new Date().toISOString() }
      ]
    });

    const result = await mockRouter.routeMessage('gdevelop', {
      action: 'getChanges',
      projectId: 'test123'
    });

    expect(result.status).toBe('success');
    expect(result.changes.length).toBeGreaterThan(0);
    expect(result.changes[0].timestamp).toBeDefined();
  });
});
