import { generateToken, verifyToken } from '@utils/auth.js';
import { vi } from 'vitest';

// Utilise le mock global défini dans setupMocks.js
describe('Auth Token Functions', () => {
  test('generateToken returns MOCK_TOKEN', () => {
    expect(generateToken('test-user')).toBe('MOCK_TOKEN');
  });

  test('verifyToken returns mock user', () => {
    expect(verifyToken('token')).toEqual({
      userId: 'test-user-id',
      roles: [],
      permissions: []
    });
  });
});
