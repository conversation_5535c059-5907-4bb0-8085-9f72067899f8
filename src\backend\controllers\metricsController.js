import metricsService from '../services/metricsService.js'

class MetricsController {
  async getMetrics(req, res) {
    try {
      const metrics = metricsService.getCurrentMetrics()
      res.json(metrics)
    } catch (error) {
      res.status(500).json({ error: error.message })
    }
  }

  async triggerArchive(req, res) {
    try {
      await metricsService.archiveMetrics()
      res.json({ status: 'Archivage réussi' })
    } catch (error) {
      res.status(500).json({ error: error.message })
    }
  }
}

export default new MetricsController()
