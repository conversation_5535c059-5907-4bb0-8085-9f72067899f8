# Test Suite Documentation

```mermaid
graph LR
    A[Test Suite] --> B[Unit Tests]
    A --> C[Integration Tests]
    A --> D[E2E Tests]
    B --> E[Module Mocks]
    C --> F[Service Tests]
    D --> G[Full System Tests]
```

## Documentation Structure

1. **[Main Test Guide](../docs/TEST_DOCUMENTATION.md)**  
   Overview of testing architecture and principles

2. **[ESM Mocking Guide](../docs/TEST_MOCKS_ESM.md)**  
   Advanced techniques for mocking in ESM environment

3. **[Debugging Guide](../docs/DEBUGGING_GUIDE.md)**  
   Troubleshooting and diagnostic tools

## Key Directories

| Directory | Purpose |
|-----------|---------|
| `__mocks__` | Global mock implementations |
| `mocks` | Test-specific mock data |
| `utils` | Testing utilities |

## Running Tests

```bash
# Run all tests
npm test

# Run specific test file
npm test path/to/test/file.test.js

# Run with coverage
npm run test:cov

# Debug tests
npm run test:debug
```

## Best Practices

- Use `vi.mock()` for module mocking
- Prefer `describe.each` for parameterized tests
- Clean up mocks in `afterEach` hooks
- Use `.concurrent` for parallelizable tests

[//]: # (Additional test-specific documentation)
