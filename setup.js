#!/usr/bin/env node
// Script de configuration durable pour le projet

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

  const nodePath = './nodejs/node-v20.12.2-win-x64/node.exe';
  const npmPath = './nodejs/node-v20.12.2-win-x64/npm.cmd';

  // 1. Vérifier la présence de Node.js
  try {
    const nodeVersion = execSync(`"${nodePath}" --version`).toString().trim();
    console.log(`✓ Node.js détecté (${nodeVersion})`);
  } catch (error) {
    console.error('× Node.js n\'est pas accessible');
    process.exit(1);
  }

  // 2. Vérifier npm
  try {
    const npmVersion = execSync(`"${npmPath}" --version`).toString().trim();
    console.log(`✓ npm détecté (${npmVersion})`);
  } catch (error) {
    console.error('× npm n\'est pas accessible');
    process.exit(1);
  }

  // 3. Installer les dépendances
  console.log('Installation des dépendances...');
  try {
    execSync(`"${npmPath}" install`, { stdio: 'inherit' });
  console.log('✓ Dépendances installées avec succès');
} catch (error) {
  console.error('× Échec de l\'installation des dépendances');
  process.exit(1);
}

// 4. Créer des alias pratiques
const packageJsonPath = path.join(__dirname, 'package.json');
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath));

// Ajouter des scripts personnalisés
packageJson.scripts = {
  ...packageJson.scripts,
  "setup": "node setup.js",
  "verify": "node verify-env.js",
  "start": "npm run dev"
};

fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
console.log('✓ Configuration mise à jour dans package.json');

console.log('\n✅ Configuration terminée avec succès !');
console.log('Vous pouvez maintenant utiliser les commandes standard :');
console.log('  npm run dev - Lancer le développement');
console.log('  npm test - Exécuter les tests');
