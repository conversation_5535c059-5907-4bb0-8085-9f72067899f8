import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock de child_process.exec
const mockExec = vi.fn();

// Mock du module child_process
vi.mock('child_process', () => ({
  exec: mockExec
}));

// Fonction killProcessOnPort isolée pour le test
async function killProcessOnPort(port) {
  return new Promise((resolve) => {
    // Commande pour trouver le processus sur le port
    const command = `netstat -ano | findstr :${port}`;
    
    mockExec(command, (err, stdout, stderr) => {
      if (err || !stdout.trim()) {
        // Aucun processus trouvé
        resolve(false);
        return;
      }
      
      // Extraire le PID du résultat
      const lines = stdout.trim().split('\n');
      const pids = new Set();
      
      lines.forEach(line => {
        const match = line.trim().split(/\s+/);
        if (match.length > 4) pids.add(match[4]);
      });
      
      if (pids.size === 0) {
        resolve(false);
        return;
      }
      
      // Tuer chaque processus trouvé
      let completed = 0;
      pids.forEach(pid => {
        mockExec(`taskkill /PID ${pid} /F`, () => {
          completed++;
          if (completed === pids.size) {
            resolve(true);
          }
        });
      });
    });
  });
}

describe('Port Kill Tests', () => {
  beforeEach(() => {
    // Réinitialiser les mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('should kill process on port', async () => {
    // Simuler un processus sur le port
    mockExec.mockImplementation((command, callback) => {
      if (command.includes('netstat')) {
        callback(null, '  TCP    0.0.0.0:7000           0.0.0.0:0              LISTENING       1234\r\n', '');
      } else if (command.includes('taskkill')) {
        callback(null, 'SUCCESS: Le processus avec PID 1234 a été terminé.', '');
      }
    });

    const result = await killProcessOnPort(7000);
    
    // Vérifier que le résultat est true (processus tué)
    expect(result).toBe(true);
    
    // Vérifier que les commandes ont été appelées
    expect(mockExec).toHaveBeenCalledWith(expect.stringContaining('netstat -ano | findstr :7000'), expect.any(Function));
    expect(mockExec).toHaveBeenCalledWith(expect.stringContaining('taskkill /PID 1234 /F'), expect.any(Function));
  });

  it('should handle no process on port', async () => {
    // Simuler aucun processus sur le port
    mockExec.mockImplementation((command, callback) => {
      if (command.includes('netstat')) {
        callback(null, '', '');
      }
    });

    const result = await killProcessOnPort(7000);
    
    // Vérifier que le résultat est false (aucun processus à tuer)
    expect(result).toBe(false);
    
    // Vérifier que seule la commande netstat a été appelée
    expect(mockExec).toHaveBeenCalledWith(expect.stringContaining('netstat -ano | findstr :7000'), expect.any(Function));
    expect(mockExec).not.toHaveBeenCalledWith(expect.stringContaining('taskkill'), expect.any(Function));
  });

  it('should handle error in netstat command', async () => {
    // Simuler une erreur dans la commande netstat
    mockExec.mockImplementation((command, callback) => {
      if (command.includes('netstat')) {
        callback(new Error('Command failed'), '', 'Command failed');
      }
    });

    const result = await killProcessOnPort(7000);
    
    // Vérifier que le résultat est false (erreur dans la commande)
    expect(result).toBe(false);
    
    // Vérifier que seule la commande netstat a été appelée
    expect(mockExec).toHaveBeenCalledWith(expect.stringContaining('netstat -ano | findstr :7000'), expect.any(Function));
    expect(mockExec).not.toHaveBeenCalledWith(expect.stringContaining('taskkill'), expect.any(Function));
  });
});
