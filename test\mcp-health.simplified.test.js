/**
 * Tests ultra-simplifiés pour MCP Health Check
 *
 * Ce fichier contient des tests extrêmement simples pour le système de santé MCP,
 * sans aucune dépendance à Core ou à d'autres modules complexes.
 */
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLoggerMock } from './utils/simpleMocks.js';

// Mock du logger
const loggerMock = createLoggerMock();

// Classe MCP Health Check simplifiée pour les tests
class SimplifiedMCPHealthCheck {
  constructor(options = {}) {
    this.logger = options.logger || loggerMock;
    this.services = new Map();
    this.lastCheck = null;
    this.status = 'unknown';
  }

  // Enregistrer un service à surveiller
  registerService(name, checkFunction) {
    if (!name || typeof checkFunction !== 'function') {
      throw new Error('Service name and check function are required');
    }

    this.services.set(name, {
      name,
      check: checkFunction,
      lastStatus: 'unknown',
      lastCheck: null
    });

    this.logger.debug(`Service registered: ${name}`);
  }

  // Vérifier la santé d'un service spécifique
  async checkService(serviceName) {
    const service = this.services.get(serviceName);
    if (!service) {
      throw new Error(`Service not found: ${serviceName}`);
    }

    this.logger.debug(`Checking service: ${serviceName}`);

    try {
      const result = await service.check();
      service.lastStatus = 'healthy';
      service.lastCheck = new Date().toISOString();
      
      return {
        name: serviceName,
        status: 'healthy',
        timestamp: service.lastCheck,
        details: result
      };
    } catch (error) {
      service.lastStatus = 'unhealthy';
      service.lastCheck = new Date().toISOString();
      
      this.logger.error(`Service check failed for ${serviceName}:`, error);
      
      return {
        name: serviceName,
        status: 'unhealthy',
        timestamp: service.lastCheck,
        error: error.message
      };
    }
  }

  // Vérifier la santé de tous les services
  async checkAllServices() {
    this.logger.debug('Checking all services');

    const results = [];
    let overallStatus = 'healthy';

    for (const [serviceName] of this.services) {
      try {
        const result = await this.checkService(serviceName);
        results.push(result);
        
        if (result.status === 'unhealthy') {
          overallStatus = 'unhealthy';
        }
      } catch (error) {
        results.push({
          name: serviceName,
          status: 'error',
          timestamp: new Date().toISOString(),
          error: error.message
        });
        overallStatus = 'unhealthy';
      }
    }

    this.status = overallStatus;
    this.lastCheck = new Date().toISOString();

    return {
      status: overallStatus,
      timestamp: this.lastCheck,
      services: results,
      summary: {
        total: results.length,
        healthy: results.filter(r => r.status === 'healthy').length,
        unhealthy: results.filter(r => r.status === 'unhealthy').length,
        errors: results.filter(r => r.status === 'error').length
      }
    };
  }

  // Obtenir le statut global
  getOverallStatus() {
    return {
      status: this.status,
      lastCheck: this.lastCheck,
      servicesCount: this.services.size
    };
  }

  // Simuler une réponse HTTP de santé
  getHealthResponse() {
    const timestamp = new Date().toISOString();
    
    return {
      status: 200,
      data: {
        status: this.status === 'unknown' ? 'OK' : this.status.toUpperCase(),
        timestamp,
        server: 'MCP Server',
        version: '1.0.0',
        uptime: process.uptime ? Math.floor(process.uptime()) : 0
      }
    };
  }

  // Réinitialiser l'état (pour les tests)
  __reset() {
    this.services.clear();
    this.lastCheck = null;
    this.status = 'unknown';
  }
}

console.log('\n=== DÉBUT DES TESTS MCP HEALTH SIMPLIFIÉS ===\n');

describe('MCP Health Check (Simplifié)', () => {
  let healthCheck;

  beforeEach(() => {
    vi.clearAllMocks();
    loggerMock.resetMocks();
    
    // Créer une nouvelle instance
    healthCheck = new SimplifiedMCPHealthCheck({
      logger: loggerMock
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Enregistrement des services', () => {
    it('devrait enregistrer un service avec succès', () => {
      // Arrange
      const serviceName = 'database';
      const checkFunction = vi.fn().mockResolvedValue({ connected: true });

      // Act
      healthCheck.registerService(serviceName, checkFunction);

      // Assert
      expect(healthCheck.services.has(serviceName)).toBe(true);
      expect(loggerMock.debug).toHaveBeenCalledWith(`Service registered: ${serviceName}`);
    });

    it('devrait rejeter l\'enregistrement sans nom de service', () => {
      // Act & Assert
      expect(() => healthCheck.registerService()).toThrow('Service name and check function are required');
      expect(() => healthCheck.registerService('test')).toThrow('Service name and check function are required');
    });
  });

  describe('Vérification des services individuels', () => {
    beforeEach(() => {
      // Enregistrer un service de test
      healthCheck.registerService('testService', vi.fn().mockResolvedValue({ ok: true }));
    });

    it('devrait vérifier un service sain avec succès', async () => {
      // Act
      const result = await healthCheck.checkService('testService');

      // Assert
      expect(result).toMatchObject({
        name: 'testService',
        status: 'healthy',
        timestamp: expect.any(String),
        details: { ok: true }
      });
      expect(loggerMock.debug).toHaveBeenCalledWith('Checking service: testService');
    });

    it('devrait gérer les services défaillants', async () => {
      // Arrange
      const error = new Error('Service unavailable');
      healthCheck.registerService('failingService', vi.fn().mockRejectedValue(error));

      // Act
      const result = await healthCheck.checkService('failingService');

      // Assert
      expect(result).toMatchObject({
        name: 'failingService',
        status: 'unhealthy',
        timestamp: expect.any(String),
        error: 'Service unavailable'
      });
      expect(loggerMock.error).toHaveBeenCalledWith(
        'Service check failed for failingService:',
        error
      );
    });

    it('devrait rejeter pour un service inexistant', async () => {
      // Act & Assert
      await expect(healthCheck.checkService('nonexistent')).rejects.toThrow('Service not found: nonexistent');
    });
  });

  describe('Vérification de tous les services', () => {
    beforeEach(() => {
      // Enregistrer plusieurs services
      healthCheck.registerService('service1', vi.fn().mockResolvedValue({ status: 'ok' }));
      healthCheck.registerService('service2', vi.fn().mockResolvedValue({ status: 'ok' }));
      healthCheck.registerService('service3', vi.fn().mockRejectedValue(new Error('Failed')));
    });

    it('devrait vérifier tous les services et retourner un résumé', async () => {
      // Act
      const result = await healthCheck.checkAllServices();

      // Assert
      expect(result).toMatchObject({
        status: 'unhealthy', // Car service3 échoue
        timestamp: expect.any(String),
        services: expect.arrayContaining([
          expect.objectContaining({ name: 'service1', status: 'healthy' }),
          expect.objectContaining({ name: 'service2', status: 'healthy' }),
          expect.objectContaining({ name: 'service3', status: 'unhealthy' })
        ]),
        summary: {
          total: 3,
          healthy: 2,
          unhealthy: 1,
          errors: 0
        }
      });
      expect(loggerMock.debug).toHaveBeenCalledWith('Checking all services');
    });

    it('devrait retourner un statut sain si tous les services sont OK', async () => {
      // Arrange
      healthCheck.__reset();
      healthCheck.registerService('service1', vi.fn().mockResolvedValue({ ok: true }));
      healthCheck.registerService('service2', vi.fn().mockResolvedValue({ ok: true }));

      // Act
      const result = await healthCheck.checkAllServices();

      // Assert
      expect(result.status).toBe('healthy');
      expect(result.summary.healthy).toBe(2);
      expect(result.summary.unhealthy).toBe(0);
    });
  });

  describe('Statut global', () => {
    it('devrait retourner le statut global', () => {
      // Act
      const status = healthCheck.getOverallStatus();

      // Assert
      expect(status).toMatchObject({
        status: 'unknown',
        lastCheck: null,
        servicesCount: 0
      });
    });

    it('devrait mettre à jour le statut après vérification', async () => {
      // Arrange
      healthCheck.registerService('test', vi.fn().mockResolvedValue({ ok: true }));

      // Act
      await healthCheck.checkAllServices();
      const status = healthCheck.getOverallStatus();

      // Assert
      expect(status.status).toBe('healthy');
      expect(status.lastCheck).toBeDefined();
      expect(status.servicesCount).toBe(1);
    });
  });

  describe('Réponse HTTP de santé', () => {
    it('devrait générer une réponse HTTP valide', () => {
      // Act
      const response = healthCheck.getHealthResponse();

      // Assert
      expect(response).toMatchObject({
        status: 200,
        data: {
          status: 'OK',
          timestamp: expect.any(String),
          server: 'MCP Server',
          version: '1.0.0',
          uptime: expect.any(Number)
        }
      });
    });

    it('devrait refléter le statut actuel dans la réponse', async () => {
      // Arrange
      healthCheck.registerService('failing', vi.fn().mockRejectedValue(new Error('Failed')));
      await healthCheck.checkAllServices();

      // Act
      const response = healthCheck.getHealthResponse();

      // Assert
      expect(response.data.status).toBe('UNHEALTHY');
    });
  });

  describe('Gestion des erreurs', () => {
    it('devrait gérer les erreurs de connexion gracieusement', () => {
      // Arrange
      const throwError = () => {
        throw new Error('Connection refused');
      };

      // Act & Assert
      expect(throwError).toThrow('Connection refused');
    });

    it('devrait logger les erreurs appropriées', async () => {
      // Arrange
      const error = new Error('Test error');
      healthCheck.registerService('errorService', vi.fn().mockRejectedValue(error));

      // Act
      await healthCheck.checkService('errorService');

      // Assert
      expect(loggerMock.error).toHaveBeenCalledWith(
        'Service check failed for errorService:',
        error
      );
    });
  });
});

console.log('\n=== FIN DES TESTS MCP HEALTH SIMPLIFIÉS ===\n');
