class ErrorHandler {
  constructor(logger) {
    this.logger = logger;
  }

  handle(error, context = {}) {
    this.logger.error('Chat processing error:', {
      error: error.message,
      stack: error.stack,
      context
    });

    return {
      text: this.getErrorMessage(error),
      sender: 'system',
      isAI: false,
      timestamp: new Date().toISOString()
    };
  }

  getErrorMessage(error) {
    if (error.isNetworkError) {
      return 'Network error - please try again later';
    }
    if (error.isAuthError) {
      return 'Authentication failed';
    }
    return 'Service unavailable';
  }
}

export default ErrorHandler;
