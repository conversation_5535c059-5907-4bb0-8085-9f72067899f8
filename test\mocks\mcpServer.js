import { vi } from 'vitest';

/**
 * Mock amélioré pour MCPServer
 * Ce mock évite complètement les boucles infinies et simule correctement le comportement du serveur
 */
class MockMCPServer {
  constructor() {
    this.port = 7000;
    this.portFile = 'port.txt';
    this.maxPortAttempts = 3; // Limiter le nombre de tentatives
    this.currentPortAttempt = 0;

    // Mock de l'application Express
    this.app = {
      listen: vi.fn((port, callback) => {
        if (callback) callback();
        return {
          on: vi.fn((event, handler) => {
            // Ne jamais appeler le handler pour éviter les boucles infinies
            return this;
          })
        };
      })
    };

    // Spies pour les tests
    this.checkPortSpy = vi.spyOn(this, 'checkPort');
    this.forceKillSpy = vi.spyOn(this, 'forceKill');
    this.savePortSpy = vi.spyOn(this, 'savePort');
    this.startSpy = vi.spyOn(this, 'start');
  }

  async start() {
    // Approche itérative au lieu de récursive
    let attempts = 0;
    const maxAttempts = this.maxPortAttempts;

    while (attempts < maxAttempts) {
      attempts++;
      console.log(`[MOCK] Tentative de démarrage ${attempts}/${maxAttempts}`);

      try {
        this.port = await this.loadOrFindPort();

        const server = this.app.listen(this.port, () => {
          console.log(`🚀 [MOCK] Serveur démarré sur le port ${this.port}`);
        });

        // Retourner immédiatement le serveur sans attacher de handler d'erreur réel
        return server;
      } catch (error) {
        console.log(`[MOCK] Erreur au démarrage: ${error.message}`);
        this.port++;
        await this.savePort(this.port);

        if (attempts >= maxAttempts) {
          console.log(`[MOCK] Nombre maximum de tentatives atteint (${maxAttempts})`);
          return null;
        }
      }
    }

    return null;
  }

  async loadOrFindPort() {
    // Ajouter un timeout pour éviter les blocages
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        console.log('[MOCK] Timeout dans loadOrFindPort, retour port par défaut');
        resolve(7000);
      }, 1000);

      // Simuler la recherche d'un port disponible
      this.findAvailablePort().then(port => {
        clearTimeout(timeout);
        resolve(port);
      }).catch(err => {
        clearTimeout(timeout);
        console.log(`[MOCK] Erreur dans loadOrFindPort: ${err.message}`);
        resolve(7000); // Fallback en cas d'erreur
      });
    });
  }

  async findAvailablePort() {
    // Limiter la recherche à 3 ports pour éviter les boucles trop longues
    for (let port = 7000; port < 7003; port++) {
      const used = await this.checkPort(port);
      if (!used) {
        await this.savePort(port);
        return port;
      }
    }
    return 7000; // Fallback
  }

  async checkPort(port) {
    // Ajouter un timeout pour éviter les blocages
    return new Promise(resolve => {
      const timeout = setTimeout(() => {
        console.log(`[MOCK] Timeout dans checkPort pour port ${port}`);
        resolve(false); // Considérer le port comme libre en cas de timeout
      }, 500);

      // Simuler la vérification d'un port
      // Pour les tests, on peut simuler que certains ports sont occupés
      const isOccupied = port === 7001; // Simuler que le port 7001 est occupé

      clearTimeout(timeout);
      resolve(isOccupied);
    });
  }

  async forceKill(port) {
    // Ajouter un timeout pour éviter les blocages
    return new Promise(resolve => {
      const timeout = setTimeout(() => {
        console.log(`[MOCK] Timeout dans forceKill pour port ${port}`);
        resolve();
      }, 500);

      // Simuler la libération du port
      console.log(`[MOCK] Libération du port ${port}`);

      // Simuler un délai pour l'opération
      setTimeout(() => {
        clearTimeout(timeout);
        resolve();
      }, 50);
    });
  }

  async savePort(port) {
    // Ajouter un timeout pour éviter les blocages
    return new Promise(resolve => {
      const timeout = setTimeout(() => {
        console.log(`[MOCK] Timeout dans savePort pour port ${port}`);
        this.port = port;
        resolve();
      }, 500);

      // Simuler la sauvegarde du port
      this.port = port;

      // Simuler un délai pour l'opération
      setTimeout(() => {
        clearTimeout(timeout);
        resolve();
      }, 50);
    });
  }

  // Méthode pour réinitialiser les spies
  resetSpies() {
    this.checkPortSpy.mockClear();
    this.forceKillSpy.mockClear();
    this.savePortSpy.mockClear();
    this.startSpy.mockClear();
  }

  // Méthode pour réinitialiser l'état du mock
  reset() {
    this.port = 7000;
    this.currentPortAttempt = 0;
    this.resetSpies();
  }
}

export default MockMCPServer;
