import archiveMetrics from '../../../scripts/archiveMetrics.js'

class MetricsService {
  constructor() {
    this.metrics = {
      responseTimes: [],
      successRates: [],
      tokenUsage: []
    }
    this.archivingInterval = null
  }

  recordResponse(timeMs, success, tokens) {
    this.metrics.responseTimes.push(timeMs)
    this.metrics.successRates.push(success ? 1 : 0)
    this.metrics.tokenUsage.push(tokens)
  }

  getCurrentMetrics() {
    return {
      ...this.metrics,
      timestamp: new Date().toISOString()
    }
  }

  startAutoArchiving(intervalHours = 24) {
    this.archivingInterval = setInterval(async () => {
      await this.archiveMetrics()
    }, intervalHours * 60 * 60 * 1000)
  }

  async archiveMetrics() {
    const metrics = this.getCurrentMetrics()
    await archiveMetrics(metrics)
    // Réinitialiser après archivage
    this.metrics = {
      responseTimes: [],
      successRates: [], 
      tokenUsage: []
    }
  }
}

export default new MetricsService()
