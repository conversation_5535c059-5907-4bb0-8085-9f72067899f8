import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link, NavLink } from 'react-router-dom';
import Home from '../../pages/Home';
import Chat from '../../pages/Chat';
import './Nav.css';

function MainLayout() {
  return (
    <Router>
      <nav>
        <ul>
        <li><NavLink to="/" className={({isActive}) => isActive ? 'active' : ''}>Accueil</NavLink></li>
        <li><NavLink to="/chat" className={({isActive}) => isActive ? 'active' : ''}>Chat</NavLink></li>
        </ul>
      </nav>

      <Routes>
        <Route path="/" element={<Home />} />
        <Route path="/chat" element={<Chat />} />
      </Routes>
    </Router>
  );
}

export default MainLayout;
