import { createRequire } from 'module';
const require = createRequire(import.meta.url);
const MessageStorage = require('../shared/MessageStorage.cjs');

class ChatService {
  constructor({ logger = console, storage } = {}) {
    this.storage = storage || new MessageStorage();
    this.logger = logger;
    this.MAX_HISTORY = 20;
    
    // Initialisation du stockage
    if (!this.storage.messages) {
      this.storage.messages = [];
    }
  }

  async sendMessage(user, message) {
    try {
      this.logger?.debug(`Processing message from user: ${user?.username}`);

      // Ajout du message avec limite d'historique
      await this.storage.addMessage(user, message);
      const history = this.storage.getHistory() || [];

      if (history?.length >= this.MAX_HISTORY) {
        const messagesToRemove = history.length - this.MAX_HISTORY + 1;
        await this.storage.removeOldestMessages(messagesToRemove);
      }

      return {
        success: true,
        message: 'Message processed',
        history: this.storage.getHistory()
      };
    } catch (error) {
      this.logger?.error('Message processing failed:', error);
      throw error;
    }
  }

  async getMessageHistory() {
    return this.storage.getHistory();
  }

  async deleteMessage(messageId) {
    return this.storage.removeMessage(messageId);
  }
}

// Exporter une instance de ChatService au lieu de la classe
export default new ChatService();
