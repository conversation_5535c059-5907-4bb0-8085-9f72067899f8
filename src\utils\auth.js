import jwt from 'jsonwebtoken';
import { readFileSync } from 'fs';
import path from 'path';

const privateKey = readFileSync(path.join(process.cwd(), 'config/private.key'));
const publicKey = readFileSync(path.join(process.cwd(), 'config/public.key'));

export const generateToken = (userId, roles = [], permissions = []) => {
  return jwt.sign({
    userId,
    roles,
    permissions
  }, privateKey, {
    algorithm: 'RS256',
    expiresIn: '1h'
  });
};

export const verifyToken = (token) => {
  try {
    const decoded = jwt.verify(token, publicKey, { algorithms: ['RS256'] });
    return {
      userId: decoded.userId,
      roles: decoded.roles || [],
      permissions: decoded.permissions || []
    };
  } catch (err) {
    throw new Error('Token invalide');
  }
};

export const authMiddleware = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    res.set('X-Auth-Status', 'Missing');
    return res.status(401).json({ error: 'Token manquant' });
  }

  try {
    req.user = verifyToken(token);
    res.set('X-Auth-Status', 'Valid');
    next();
  } catch (err) {
    res.set('X-Auth-Status', 'Invalid');
    return res.status(403).json({ error: err.message });
  }
};
