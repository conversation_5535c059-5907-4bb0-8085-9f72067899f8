import { vi } from 'vitest';

/**
 * Mock standardisé pour le module child_process
 * Ce mock est conçu pour être utilisé dans tous les tests
 */

// Registre des processus simulés
const mockProcesses = new Map();
const mockPorts = new Map();

// Créer le mock
const childProcessMock = {
  // Méthodes principales
  exec: vi.fn((command, options, callback) => {
    if (typeof options === 'function') {
      callback = options;
      options = {};
    }

    // Simuler l'exécution de commandes spécifiques
    if (command.includes('netstat') && command.includes('findstr')) {
      const portMatch = command.match(/:(\d+)/);
      const port = portMatch ? portMatch[1] : null;
      
      if (port && mockPorts.has(parseInt(port))) {
        const pid = mockPorts.get(parseInt(port));
        const stdout = `  TCP    0.0.0.0:${port}           0.0.0.0:0              LISTENING       ${pid}`;
        if (callback) callback(null, { stdout, stderr: '' });
        return { stdout, stderr: '' };
      } else {
        if (callback) callback(null, { stdout: '', stderr: '' });
        return { stdout: '', stderr: '' };
      }
    }
    
    if (command.includes('taskkill') && command.includes('/PID')) {
      const pidMatch = command.match(/\/PID\s+(\d+)/);
      const pid = pidMatch ? parseInt(pidMatch[1]) : null;
      
      if (pid && mockProcesses.has(pid)) {
        mockProcesses.delete(pid);
        // Supprimer également les ports associés à ce PID
        for (const [port, processPid] of mockPorts.entries()) {
          if (processPid === pid) {
            mockPorts.delete(port);
          }
        }
        
        const stdout = `SUCCESS: Le processus avec PID ${pid} a été arrêté.`;
        if (callback) callback(null, { stdout, stderr: '' });
        return { stdout, stderr: '' };
      } else {
        const stderr = `ERROR: Le processus avec PID ${pid} n'a pas été trouvé.`;
        if (callback) callback(new Error(stderr), { stdout: '', stderr });
        return { stdout: '', stderr };
      }
    }
    
    if (command.includes('schtasks') && command.includes('/query')) {
      const taskNameMatch = command.match(/\/tn\s+"([^"]+)"/);
      const taskName = taskNameMatch ? taskNameMatch[1] : null;
      
      if (taskName === 'GDevAI Backup') {
        const stdout = 'Nom de la tâche: GDevAI Backup\nStatut: Prêt\nPlanification: Quotidienne';
        if (callback) callback(null, { stdout, stderr: '' });
        return { stdout, stderr: '' };
      } else {
        const stderr = `ERROR: La tâche planifiée "${taskName}" n'existe pas.`;
        if (callback) callback(new Error(stderr), { stdout: '', stderr });
        return { stdout, stderr };
      }
    }
    
    // Commande par défaut
    const stdout = `Exécution simulée de: ${command}`;
    if (callback) callback(null, { stdout, stderr: '' });
    return { stdout, stderr: '' };
  }),
  
  execSync: vi.fn((command) => {
    // Simuler l'exécution synchrone de commandes spécifiques
    if (command.includes('netstat') && command.includes('findstr')) {
      const portMatch = command.match(/:(\d+)/);
      const port = portMatch ? portMatch[1] : null;
      
      if (port && mockPorts.has(parseInt(port))) {
        const pid = mockPorts.get(parseInt(port));
        return Buffer.from(`  TCP    0.0.0.0:${port}           0.0.0.0:0              LISTENING       ${pid}`);
      } else {
        return Buffer.from('');
      }
    }
    
    if (command.includes('taskkill') && command.includes('/PID')) {
      const pidMatch = command.match(/\/PID\s+(\d+)/);
      const pid = pidMatch ? parseInt(pidMatch[1]) : null;
      
      if (pid && mockProcesses.has(pid)) {
        mockProcesses.delete(pid);
        // Supprimer également les ports associés à ce PID
        for (const [port, processPid] of mockPorts.entries()) {
          if (processPid === pid) {
            mockPorts.delete(port);
          }
        }
        
        return Buffer.from(`SUCCESS: Le processus avec PID ${pid} a été arrêté.`);
      } else {
        throw new Error(`ERROR: Le processus avec PID ${pid} n'a pas été trouvé.`);
      }
    }
    
    if (command.includes('schtasks') && command.includes('/query')) {
      const taskNameMatch = command.match(/\/tn\s+"([^"]+)"/);
      const taskName = taskNameMatch ? taskNameMatch[1] : null;
      
      if (taskName === 'GDevAI Backup') {
        return Buffer.from('Nom de la tâche: GDevAI Backup\nStatut: Prêt\nPlanification: Quotidienne');
      } else {
        throw new Error(`ERROR: La tâche planifiée "${taskName}" n'existe pas.`);
      }
    }
    
    // Commande par défaut
    return Buffer.from(`Exécution simulée de: ${command}`);
  }),
  
  // Méthodes utilitaires pour les tests
  __setProcess: (pid, info) => {
    mockProcesses.set(pid, info);
  },
  
  __setProcessOnPort: (port, pid) => {
    mockPorts.set(port, pid);
  },
  
  __clearAllProcesses: () => {
    mockProcesses.clear();
    mockPorts.clear();
  },
  
  __getProcess: (pid) => {
    return mockProcesses.get(pid);
  },
  
  __getProcessOnPort: (port) => {
    return mockPorts.get(port);
  },
  
  // Réinitialiser tous les mocks
  __resetMocks: () => {
    vi.clearAllMocks();
  }
};

// Exporter les fonctions utilitaires
export const __setProcessOnPort = childProcessMock.__setProcessOnPort;
export const __clearAllProcesses = childProcessMock.__clearAllProcesses;

// Exporter le mock
export default childProcessMock;
