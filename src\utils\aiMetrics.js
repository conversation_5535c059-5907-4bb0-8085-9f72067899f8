import logger from './logger.js';

/**
 * Enregistre les métriques d'une requête IA
 * @param {Object} params 
 * @param {string} params.provider - Fournisseur IA (deepseek/openai)
 * @param {number} params.duration - Durée en ms
 * @param {boolean} params.success - Si la requête a réussi
 * @param {number} params.tokenCount - Nombre de tokens utilisés
 * @param {string} params.model - Modèle utilisé
 */
export function logAIMetrics({ provider, duration, success, tokenCount, model }) {
  logger.info('AI request metrics', {
    provider,
    duration,
    success,
    tokenCount,
    model,
    timestamp: new Date().toISOString()
  });
}

/**
 * Track an AI request with standard metrics
 * @param {Object} params
 * @param {string} params.provider - AI provider
 * @param {number} params.responseTime - Response time in ms
 * @param {boolean} params.success - If request succeeded
 * @param {number} params.tokens - Token count used
 */
export function trackRequest({ provider, responseTime, success, tokens }) {
  logAIMetrics({
    provider,
    duration: responseTime,
    success,
    tokenCount: tokens,
    model: 'default'
  });
}

/**
 * Récupère les statistiques de performance
 * @returns {Promise<Object>} Statistiques consolidées
 */
export async function getAIStats() {
  // À implémenter - lecture et analyse des logs
  return {
    avgResponseTime: 0,
    successRate: 0,
    totalTokens: 0
  };
}
