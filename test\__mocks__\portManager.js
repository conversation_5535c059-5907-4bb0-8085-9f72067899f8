/**
 * Mock pour le module portManager
 * Utilisé dans les tests
 */
import { vi } from 'vitest';
import { EventEmitter } from 'events';
import universalLogger from './universal-logger.js';

// Classe de base mockée
class MockPortManagerCore extends EventEmitter {
  constructor() {
    super();
    this.adapters = new Map();
    this.plugins = new Map();
    this.logger = universalLogger;
  }

  registerAdapter(name, adapter) {
    this.adapters.set(name, adapter);
    return this;
  }

  registerPlugin(name, plugin) {
    this.plugins.set(name, plugin);
    this.emit('pluginRegistered', name);
    return this;
  }

  async findFreePort(options = {}) {
    this.emit('beforeFindPort', options);
    const port = 50000 + Math.floor(Math.random() * 10000);
    this.emit('afterFindPort', port);
    return port;
  }

  async killProcessOnPort(port) {
    this.emit('beforeKillPort', port);
    this.emit('afterKillPort', port);
    return true;
  }
}

// Adapter mocké
class MockAdapter {
  constructor() {
    this.logger = universalLogger;
  }

  async findFreePort() {
    return 50000 + Math.floor(Math.random() * 10000);
  }

  async killProcessOnPort(port) {
    return true;
  }

  async isPortOccupied(port) {
    // Simuler un port occupé si le port est 4000 (utilisé dans le test)
    return port === 4000;
  }
}

// Fonction factory mockée
const createPortManager = vi.fn(() => {
  const instance = new MockPortManagerCore();
  instance.registerAdapter('linux', new MockAdapter());
  return instance;
});

// Instance par défaut
const defaultInstance = createPortManager();

export { createPortManager, MockPortManagerCore, MockAdapter };
export default defaultInstance;
