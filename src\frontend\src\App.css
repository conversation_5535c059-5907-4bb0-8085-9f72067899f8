/* Styles globaux */
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* Navigation */
.main-nav {
  background-color: #1a1a1a;
  padding: 1rem;
  display: flex;
  gap: 1rem;
  border-bottom: 1px solid #444;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.nav-link:hover {
  background-color: #646cff;
}

/* Contenu principal */
.main-content {
  flex: 1;
  padding: 2rem;
  background-color: #242424;
}

/* Styles pour la page d'accueil */
.home-page {
  max-width: 1200px;
  margin: 0 auto;
}

.home-page h1 {
  color: white;
  margin-bottom: 2rem;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background-color: #1a1a1a;
  padding: 2rem;
  border-radius: 8px;
  color: white;
  text-decoration: none;
  transition: transform 0.2s;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card h2 {
  color: #646cff;
  margin-bottom: 0.5rem;
}

/* Styles pour le chat (conservés depuis la version précédente) */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 80vh;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  border: 1px solid #444;
  border-radius: 8px;
  overflow: hidden;
}

.messages {
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  background-color: #1a1a1a;
}

.message {
  margin: 0.5rem 0;
  padding: 0.5rem 1rem;
  border-radius: 18px;
  max-width: 70%;
}

.message.user {
  margin-left: auto;
  background-color: #646cff;
  color: white;
}

.message.assistant {
  margin-right: auto;
  background-color: #333;
}

.input-area {
  display: flex;
  padding: 1rem;
  background-color: #1a1a1a;
  border-top: 1px solid #444;
}

.input-area input {
  flex: 1;
  padding: 0.5rem 1rem;
  border-radius: 18px;
  border: 1px solid #444;
  background-color: #242424;
  color: white;
}

.input-area button {
  margin-left: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 18px;
  border: none;
  background-color: #646cff;
  color: white;
  cursor: pointer;
}

.input-area button:hover {
  background-color: #535bf2;
}
