/**
 * Mock complet pour le module de chat
 */
import { vi } from 'vitest';

// Créer des mocks pour les réponses
const createMockResponses = () => {
  const standardResponse = (text) => ({
    text: text || 'Default response',
    sender: 'assistant',
    isAI: false,
    timestamp: new Date().toISOString()
  });

  const aiResponse = (text) => ({
    text: text || 'Mock AI response',
    sender: 'assistant',
    isAI: true,
    tokens: 150,
    timestamp: new Date().toISOString()
  });

  const errorResponse = (message) => ({
    text: message || 'Service unavailable',
    sender: 'system',
    isAI: false,
    timestamp: new Date().toISOString()
  });

  const fallbackResponse = () => ({
    text: 'Fallback response',
    sender: 'assistant',
    isAI: false,
    timestamp: new Date().toISOString()
  });

  return {
    standardResponse,
    aiResponse,
    errorResponse,
    fallbackResponse
  };
};

// Créer le mock du module de chat
export const createChatModuleMock = () => {
  const responses = createMockResponses();
  
  return {
    process: vi.fn().mockImplementation((message) => {
      if (message.useAI) {
        return responses.aiResponse();
      }
      return responses.standardResponse(`Mock response to: ${message.text}`);
    }),
    
    responseBuilder: {
      buildStandard: vi.fn().mockImplementation((text) => 
        responses.standardResponse(text)
      ),
      buildAI: vi.fn().mockImplementation((aiResponse) => 
        responses.aiResponse(aiResponse)
      ),
      buildError: vi.fn().mockImplementation((message) => 
        responses.errorResponse(message)
      )
    },
    
    serviceHandler: {
      process: vi.fn().mockImplementation((message) => 
        responses.standardResponse(`Mock response to: ${message.text}`)
      )
    },
    
    aiHandler: {
      process: vi.fn().mockImplementation(() => 
        responses.aiResponse()
      )
    },
    
    errorHandler: {
      handle: vi.fn().mockImplementation((error) => 
        responses.errorResponse(error.message)
      )
    },
    
    logger: {
      info: vi.fn(),
      error: vi.fn(),
      debug: vi.fn()
    }
  };
};
