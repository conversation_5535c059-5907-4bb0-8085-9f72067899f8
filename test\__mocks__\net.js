/**
 * <PERSON>ck amélioré pour le module net
 * Utilisé principalement pour les tests de gestion des ports
 */
import { vi } from 'vitest';
import { EventEmitter } from 'events';

// Simuler les ports occupés
const occupiedPorts = new Set();

// Classe de serveur mockée qui étend EventEmitter
class MockServer extends EventEmitter {
  constructor() {
    super();
    this._port = null;
    this._address = null;
    this._listening = false;
    this._closed = false;
  }

  listen(port, host, callback) {
    this._port = port;
    this._address = host || '0.0.0.0';
    
    // Simuler un port occupé
    if (occupiedPorts.has(port) && port !== 0) {
      const error = new Error('EADDRINUSE');
      error.code = 'EADDRINUSE';
      process.nextTick(() => {
        this.emit('error', error);
        if (callback) callback(error);
      });
    } else {
      // Si port=0, assigner un port aléatoire
      if (port === 0) {
        this._port = Math.floor(Math.random() * 10000) + 50000;
      }
      
      // Marquer le port comme occupé
      occupiedPorts.add(this._port);
      
      this._listening = true;
      process.nextTick(() => {
        this.emit('listening');
        if (callback) callback();
      });
    }
    
    return this;
  }

  close(callback) {
    if (this._port) {
      occupiedPorts.delete(this._port);
    }
    
    this._listening = false;
    this._closed = true;
    
    process.nextTick(() => {
      this.emit('close');
      if (callback) callback();
    });
    
    return this;
  }

  address() {
    if (!this._listening) {
      return null;
    }
    
    return {
      address: this._address,
      family: 'IPv4',
      port: this._port
    };
  }
}

// Fonction principale pour créer un serveur
const createServer = vi.fn(() => new MockServer());

// Méthodes utilitaires pour les tests
const __setPortOccupied = (port) => {
  occupiedPorts.add(port);
};

const __setPortFree = (port) => {
  occupiedPorts.delete(port);
};

const __clearAllPorts = () => {
  occupiedPorts.clear();
};

// Exporter le mock
export { createServer, __setPortOccupied, __setPortFree, __clearAllPorts };
export default { createServer, __setPortOccupied, __setPortFree, __clearAllPorts };
